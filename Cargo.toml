[package]
name = "tool-hub"
version = "0.1.0"
edition = "2024"

[workspace]
members = [
  ".",
  "crates/*",
]

[workspace.dependencies]
actor = { path = "./crates/actor" }
ai = { path = "./crates/ai" }
anyhow = "1.0.98"
async-openai = { version = "0.29.0", features = ["byot"] }
async-trait = "0.1.88"
axum = { version = "0.8.4", features = ["http2", "macros", "multipart", "ws"] }
axum-extra = { version = "0.10.1", features = ["cookie", "typed-header"] }
axum-jrpc = "0.8.0"
bson = { version = "2.15.0", features = ["time-0_3", "uuid-1"] }
bytes = "1.10.1"
chat-delta-macro = { path = "./crates/ai/chat-delta-macro" }
colored = "3.0.0"
config = { version = "0.15.13", features = ["json", "yaml"] }
content = { path = "./crates/content" }
entity = { path = "./crates/entity" }
error = { path = "./crates/error" }
expression = { path = "./crates/expression" }
field_name_macro = { path = "./crates/entity/field_name_macro" }
futures = "0.3.31"
heck = "0.5.0"
integral = { path = "./crates/integral" }
interaction = { path = "./crates/interaction" }
jsonschema = "0.30.0"
jsonwebtoken = "9.3.1"
macros = { path = "./crates/macros" }
minio = "0.3.0"
mlua = { version = "0.11.1", features = [
  "anyhow",
  "async",
  "lua54",
  "send",
  "serialize",
  "vendored",
] }
mockall = "0.13.1"
mongodb = "3.2.4"
nacos-sdk = { version = "0.5.1", features = ["default"] }
once_cell = "1.21.3"
opentelemetry = { version = "0.30.0", features = ["trace"] }
opentelemetry_sdk = { version = "0.30.0", features = ["trace"] }
opentelemetry-otlp = { version = "0.30.0", features = ["grpc-tonic", "metrics"] }
opentelemetry-semantic-conventions = { version = "0.30.0", features = [
  "semconv_experimental",
] }
opentelemetry-stdout = { version = "0.30.0", features = ["metrics", "trace"] }
pest = "2.8.1"
pest_derive = "2.8.1"
pin-project-lite = "0.2.16"
proc-macro2 = "1.0.95"
pyo3 = { version = "0.25.1", features = [
  "abi3",
  "abi3-py312",
  "anyhow",
  "experimental-async",
  "serde",
] }
pyo3-async-runtimes = { version = "0.25.0", features = [
  "attributes",
  "tokio-runtime",
] }
pyo3-bytes = "0.3.0"
pythonize = "0.25.0"
quote = "1.0.40"
ractor = { version = "0.15.6", features = ["async-trait", "serde"] }
rand = "0.9.2"
redis = { version = "0.32.4", features = [
  "aio",
  "cluster-async",
  "connection-manager",
  "tokio-rustls-comp",
  "uuid",
] }
regex = "1.11.1"
reqwest = { version = "0.12.22", default-features = false, features = [
  "charset",
  "http2",
  "json",
  "rustls-tls",
] }
reqwest-eventsource = "0.6.0"
reqwest-websocket = { version = "0.5.0" }
resource = { path = "./crates/resource" }
rmcp = { version = "0.3.0", features = [
  "client",
  "reqwest",
  "server",
  "transport-child-process",
  "transport-streamable-http-client",
  "transport-streamable-http-server",
] }
script = { path = "./crates/script" }
semver = { version = "1.0.26", features = ["serde"] }
serde = { version = "1.0.219", features = ["derive", "rc", "serde_derive"] }
serde_json = "1.0.141"
syn = { version = "2.0.104", features = ["full"] }
thiserror = "2.0.12"
time = { version = "0.3.41", features = ["formatting", "local-offset", "serde"] }
tokio = { version = "1.46.1", features = ["full"] }
tokio-stream = { version = "0.1.17", features = ["full"] }
tokio-util = { version = "0.7.15", features = ["full"] }
tool = { path = "./crates/tool" }
tower-http = { version = "0.6.6", features = ["cors", "trace"] }
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-core = "0.1.34"
tracing-opentelemetry = "0.31.0"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
url = { version = "2.5.4", features = ["serde"] }
uuid = { version = "1.17.0", features = ["serde", "v7"] }
workflow = { path = "./crates/workflow" }
zip = "4.3.0"

[lib]
name = "shared_core"
path = "src/lib.rs"

[[bin]]
name = "tools-server"
path = "src/tools_server/main.rs"

[[bin]]
name = "agent-server"
path = "src/agent_server/main.rs"

# [[bin]]
# name = "agent-runner"
# path = "src/agent_runner.rs"

[dependencies]
actor = { workspace = true }
ai = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
axum = { workspace = true }
axum-extra = { workspace = true }
axum-jrpc = { workspace = true }
bson = { workspace = true }
bytes = { workspace = true }
colored = { workspace = true }
config = { workspace = true }
content = { workspace = true }
entity = { workspace = true }
error = { workspace = true }
expression = { workspace = true }
futures = { workspace = true }
integral = { workspace = true }
interaction = { workspace = true }
jsonschema = { workspace = true }
jsonwebtoken = { workspace = true }
minio = { workspace = true }
mongodb = { workspace = true }
nacos-sdk = { workspace = true }
opentelemetry = { workspace = true }
opentelemetry_sdk = { workspace = true }
opentelemetry-otlp = { workspace = true }
opentelemetry-semantic-conventions = { workspace = true }
opentelemetry-stdout = { workspace = true }
pin-project-lite = { workspace = true }
ractor = { workspace = true }
rand = { workspace = true }
reqwest = { workspace = true }
resource = { workspace = true }
rmcp = { workspace = true }
script = { workspace = true }
semver = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
time = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tool = { workspace = true }
tower-http = { workspace = true }
tracing = { workspace = true }
tracing-appender = { workspace = true }
tracing-opentelemetry = { workspace = true }
tracing-subscriber = { workspace = true }
url = { workspace = true }
uuid = { workspace = true }
workflow = { workspace = true }
zip = { workspace = true }

[profile.release]
debug = true
