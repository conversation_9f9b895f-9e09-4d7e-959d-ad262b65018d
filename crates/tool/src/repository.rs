use std::sync::Arc;

use script::manager::ContextId;

use crate::{<PERSON><PERSON><PERSON>, Tool};

#[async_trait::async_trait]
pub trait ToolRepository {
    async fn list_modules(&self) -> error::Result<Vec<Arc<Module>>>;

    async fn list_tools(&self) -> error::Result<Vec<Arc<Tool>>>;

    async fn call_tool(&self, input: CallToolInput) -> error::Result<CallToolOutput>;

    async fn get_tool_with_alias(
        &self,
        module_name_or_alias: &str,
        tool_name_or_alias: &str,
    ) -> Option<Arc<Tool>> {
        let tools = self.list_tools().await.ok()?;

        tools.into_iter().find(|tool| match tool.as_ref() {
            Tool::Script(script) => {
                (script.module_name == module_name_or_alias
                    || script
                        .module_alias
                        .contains(&module_name_or_alias.to_string()))
                    && (script.name == tool_name_or_alias
                        || script.alias.contains(&tool_name_or_alias.to_string()))
            }
            other => other.module_name() == module_name_or_alias,
        })
    }
}

pub struct CallToolInput {
    pub module_name: String,
    pub tool_name: String,
    pub tenant_id: String,
    pub user_id: String,
    pub session_id: String,
    pub executation_id: String,
    pub payload: serde_json::Value,
    pub metadata: Option<serde_json::Value>,
    pub temp_view_sender: Option<tokio::sync::mpsc::Sender<serde_json::Value>>,
}

impl CallToolInput {
    pub fn context_id(&self) -> ContextId {
        ContextId {
            tenant_id: self.tenant_id.clone(),
            user_id: self.user_id.clone(),
            session_id: self.session_id.clone(),
            executation_id: self.executation_id.clone(),
        }
    }
}

pub struct CallValidatorInput {
    pub module_name: String,
    pub validator_name: String,
    pub tenant_id: String,
    pub user_id: String,
    pub session_id: String,
    pub value: serde_json::Value,
    pub payload: Option<serde_json::Value>,
}

impl CallValidatorInput {
    pub fn context_id(&self) -> ContextId {
        ContextId {
            tenant_id: self.tenant_id.clone(),
            user_id: self.user_id.clone(),
            session_id: self.session_id.clone(),
            executation_id: "".to_string(),
        }
    }
}

impl Default for CallToolInput {
    fn default() -> Self {
        Self {
            module_name: "".to_string(),
            tool_name: "".to_string(),
            tenant_id: "".to_string(),
            user_id: "".to_string(),
            session_id: "".to_string(),
            executation_id: "".to_string(),
            payload: serde_json::Value::Null,
            metadata: None,
            temp_view_sender: None,
        }
    }
}

impl Default for CallValidatorInput {
    fn default() -> Self {
        Self {
            module_name: "default".to_string(),
            validator_name: "default".to_string(),
            tenant_id: "default".to_string(),
            user_id: "default".to_string(),
            session_id: "default".to_string(),
            value: serde_json::Value::Null,
            payload: None,
        }
    }
}

pub struct CallToolOutput {
    pub payload: serde_json::Value,
}
