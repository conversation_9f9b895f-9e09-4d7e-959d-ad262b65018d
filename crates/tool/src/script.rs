use std::{collections::HashMap, sync::Arc};

pub use script::{<PERSON>riptLanguage, ScriptTool};
use script::{
    config::ScriptModuleDefinition,
    manager::{ContextId, ScriptManager},
};

use crate::{Module, ModuleSource, ScriptModuleSource, Tool, repository::*};

#[derive(Clone)]
pub struct ScriptToolRepository {
    manager: Arc<dyn ScriptManager + Send + Sync>,
    modules_definitions: Vec<Arc<ScriptModuleDefinition>>,
    modules: Vec<Arc<Module>>,
    tools: Vec<Arc<Tool>>,
}

impl ScriptToolRepository {
    pub fn new(manager: Arc<dyn ScriptManager + Send + Sync>) -> Self {
        Self {
            manager,
            modules_definitions: vec![],
            modules: vec![],
            tools: vec![],
        }
    }

    pub fn manager(&self) -> Arc<dyn ScriptManager + Send + Sync> {
        self.manager.clone()
    }

    pub async fn load(
        &mut self,
        modules_definitions: Vec<Arc<ScriptModuleDefinition>>,
    ) -> error::Result<Vec<String>> {
        let mut errors = vec![];

        self.modules_definitions = modules_definitions;

        let script_errors = self
            .manager
            .load_modules(self.modules_definitions.clone())
            .await;

        errors.extend(script_errors);

        let tools = match self.manager.list_tools().await {
            Ok(tools) => tools,
            Err(e) => {
                tracing::error!("failed to list tools: {:?}", e);
                errors.push(format!("failed to list tools: {e:?}"));
                vec![]
            }
        };

        self.tools = tools
            .into_iter()
            .map(|tool| Arc::new(Tool::Script(tool)))
            .collect();

        let mut modules_map = HashMap::new();

        // 从 modules_definitions 中读取 module 的名称，并将其加入到 modules_map 中
        for module_definition in self.modules_definitions.iter() {
            modules_map.insert(
                module_definition.name.clone(),
                Module {
                    name: module_definition.name.clone(),
                    description: module_definition.description.clone(),
                    alias: module_definition.alias.clone().unwrap_or_default(),
                    source: ModuleSource::Script(ScriptModuleSource {
                        base: module_definition.base.clone(),
                    }),
                    config: module_definition.config.clone(),
                    tools: vec![],
                },
            );
        }

        // 从 tools 中读取 tool 的 module 名称，并将其加入到 modules_map 中
        for tool in self.tools.iter() {
            let module_name = tool.module_name();
            if let Some(module) = modules_map.get_mut(module_name) {
                module.tools.push(tool.clone());
            } else {
                tracing::error!("module {} not found", module_name);
                errors.push(format!("module {module_name} not found"));
            }
        }

        self.modules = modules_map
            .values()
            .map(|module| Arc::new(module.clone()))
            .collect();

        Ok(errors)
    }
}

#[async_trait::async_trait]
impl ToolRepository for ScriptToolRepository {
    async fn list_modules(&self) -> error::Result<Vec<Arc<Module>>> {
        Ok(self.modules.clone())
    }

    async fn list_tools(&self) -> error::Result<Vec<Arc<Tool>>> {
        self.manager.list_tools().await.map(|tools| {
            tools
                .into_iter()
                .map(|tool| Arc::new(Tool::Script(tool)))
                .collect()
        })
    }

    async fn call_tool(&self, input: CallToolInput) -> error::Result<CallToolOutput> {
        let result = self
            .manager
            .run_tool(
                ContextId {
                    tenant_id: input.tenant_id,
                    user_id: input.user_id,
                    session_id: input.session_id,
                    executation_id: input.executation_id,
                },
                input.module_name.as_str(),
                input.tool_name.as_str(),
                &input.payload,
                input.temp_view_sender,
            )
            .await?;

        Ok(CallToolOutput { payload: result })
    }
}
