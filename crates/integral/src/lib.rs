use serde::{Deserialize, Serialize};
use uuid::Uuid;

pub struct IntegralManager {
    pub integral_api: String,
    pub calculate_integral: bool,
}

#[derive(Debug)]
pub enum IntegralActorState {
    Ready,
    Calculating,
    Done,
}

#[derive(Debug)]
pub enum IntegralEvent {
    CalculateIntegral {
        conversation_id: Uuid,
        user_name: String,
        tenant_id: String,
        integral_type: IntegralType,
    },

    ExecutionError {
        conversation_id: Uuid,
        reason: String,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IntegralType {
    ///每日自动签到赠送
    #[serde(rename = "sign_in")]
    SignIn,
    ///常规问答
    #[serde(rename = "normal_qa")]
    NormalQa,
    ///深度探索
    #[serde(rename = "deep_exploration")]
    DeepExploration,
    ///Agent能力调用
    #[serde(rename = "agent_action")]
    AgentAction,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct IntegralRequest {
    pub data: IntegralRequsetData,
    #[serde(rename = "requestBase")]
    pub base: IntegralRequestBase,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegralRequsetData {
    pub duraction: u32,
    #[serde(rename = "pointsEvent")]
    pub points_event: IntegralType,
    #[serde(rename = "userId")]
    pub user_name: String,
    #[serde(rename = "tenantId")]
    pub tenant_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegralRequestBase {
    pub page: String,
    pub sort: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegralResponse {
    #[serde(rename = "isSuccess")]
    pub is_success: bool,
    pub success: bool,
    pub code: String,
    #[serde(rename = "requestId")]
    pub executation_id: String,
    pub msg: String,
    pub content: IntegralResponseContent,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegralResponseContent {
    pub code: IntegralResponseCode,
    pub desc: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IntegralResponseCode {
    ///积分变更成功
    #[serde(rename = "SUCCESS")]
    Success,
    ///用户不存在
    #[serde(rename = "NOT_FOUND_USER")]
    NotFoundUser,
    ///积分变更事件不存在
    #[serde(rename = "NOT_FOUND_EVENT")]
    NotFoundEvent,
    ///已领取过签到赠送积分
    #[serde(rename = "REPEATED_GIFT")]
    RepeatedGift,
    ///积分不足
    #[serde(rename = "NOT_ENOUGH_POINTS")]
    NotEnoughPoints,
    ///积分模式未开启
    #[serde(rename = "NOT_ENABLED")]
    NotEnabled,
}

#[allow(unused_variables)]
impl IntegralManager {
    pub async fn deduct(&self, event: IntegralEvent) -> error::Result<()> {
        if !self.calculate_integral {
            return Ok(());
        }

        tracing::debug!("IntegralActor handle: {:?}", event);

        match event {
            IntegralEvent::CalculateIntegral {
                conversation_id,
                user_name,
                tenant_id,
                integral_type,
            } => {
                if tenant_id.is_empty() {
                    return Ok(());
                }

                let request = IntegralRequest {
                    data: IntegralRequsetData {
                        duraction: 10,
                        points_event: integral_type,
                        user_name,
                        tenant_id,
                    },
                    base: IntegralRequestBase {
                        page: "1-10".to_string(),
                        sort: "-createTime".to_string(),
                    },
                };

                let client = reqwest::Client::builder()
                    .danger_accept_invalid_certs(true)
                    .build()?;
                // let response = client
                //     .post(&self.integral_api)
                //     .json(&request)
                //     .send()
                //     .await?
                //     .json::<IntegralResponse>()
                //     .await?;
                let resp = match client.post(&self.integral_api).json(&request).send().await {
                    Ok(resp) => resp,
                    Err(e) => {
                        tracing::error!("扣除积分失败: {:?}", e);
                        return Err(error::IntegralError::Deduct(Box::new(e.into())).into());
                    }
                };
                let response = match resp.json::<IntegralResponse>().await {
                    Ok(resp) => resp,
                    Err(e) => {
                        tracing::error!("扣除积分失败: {:?}", e);
                        return Err(error::IntegralError::Deduct(Box::new(e.into())).into());
                    }
                };

                if response.is_success {
                    match response.content.code {
                        IntegralResponseCode::Success => {}
                        IntegralResponseCode::NotFoundUser => {
                            return Err(error::IntegralError::UserNotFound.into());
                        }
                        IntegralResponseCode::NotFoundEvent => {
                            return Err(error::IntegralError::EventNotFound.into());
                        }
                        IntegralResponseCode::RepeatedGift => {
                            return Err(error::IntegralError::RepeatedGift.into());
                        }
                        IntegralResponseCode::NotEnoughPoints => {
                            return Err(error::IntegralError::NotEnoughPoints.into());
                        }
                        IntegralResponseCode::NotEnabled => {
                            return Err(error::IntegralError::NotEnabled.into());
                        }
                    }
                } else {
                    tracing::error!("扣除积分失败: {:?}", response);
                    return Err(error::IntegralError::ServerError.into());
                }

                tracing::debug!("扣除积分成功: {:?}", response);
            }
            IntegralEvent::ExecutionError {
                conversation_id,
                reason,
            } => {
                tracing::error!("ExecutionError: {} {}", conversation_id, reason);
            }
        };
        Ok(())
    }
}
