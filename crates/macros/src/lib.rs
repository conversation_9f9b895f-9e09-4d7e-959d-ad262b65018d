use proc_macro::TokenStream;

mod chat_delta;
mod field_name;

#[proc_macro_derive(<PERSON><PERSON><PERSON><PERSON><PERSON>, attributes(tag))]
pub fn derive_chat_delta(input: TokenStream) -> TokenStream {
    chat_delta::derive_chat_delta(input)
}

#[proc_macro_derive(DeriveFieldNames)]
pub fn derive_field_names(input: TokenStream) -> TokenStream {
    field_name::derive_field_names(input)
}

#[proc_macro_derive(FieldEnum, attributes(serde))]
pub fn derive_field_enum(input: TokenStream) -> TokenStream {
    field_name::derive_field_enum(input)
}
