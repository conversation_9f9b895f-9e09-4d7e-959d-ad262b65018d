extern crate proc_macro;

use proc_macro::TokenStream;
use quote::quote;
use syn::{Attribute, Data, DeriveInput, Fields, Ident, Lit, Meta, Variant, parse_macro_input};

/// 一个简化的 snake_case 转换函数，零依赖
fn to_snake_case(s: &str) -> String {
    let mut snake = String::new();
    for (i, ch) in s.char_indices() {
        if i > 0 && ch.is_uppercase() {
            snake.push('_');
        }
        snake.push(ch.to_ascii_lowercase());
    }
    snake
}

pub fn derive_field_names(input: TokenStream) -> TokenStream {
    // 1. 解析输入
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    // 2. 根据是 struct 还是 enum，提取名称列表
    let identifiers: Vec<Ident> = match &input.data {
        // 如果是 Struct
        Data::Struct(data_struct) => match &data_struct.fields {
            Fields::Named(fields) => fields
                .named
                .iter()
                .map(|f| f.ident.as_ref().unwrap().clone())
                .collect(),
            _ => panic!("DeriveFieldNames 只支持有命名字段的 struct"),
        },
        // 如果是 Enum
        Data::Enum(data_enum) => data_enum
            .variants
            .iter()
            .map(|v: &Variant| v.ident.clone())
            .collect(),
        // 其他情况（比如 Union）不支持
        _ => panic!("DeriveFieldNames 只能用于 struct 或 enum"),
    };

    // 3. 将所有提取出的名称（Ident）转换为 snake_case 字符串
    let snake_case_names: Vec<String> = identifiers
        .iter()
        .map(|ident| to_snake_case(&ident.to_string()))
        .collect();

    // 4. 生成代码
    let r#gen = quote! {
        impl #name {
            /// 一个包含所有字段/变体名称 (snake_case) 的静态数组
            pub const FIELD_NAMES: &'static [&'static str] = &[
                #(#snake_case_names),*
            ];

            /// 一个返回所有字段/变体名称 (snake_case) 的方法
            pub fn field_names() -> &'static [&'static str] {
                Self::FIELD_NAMES
            }
        }
    };

    r#gen.into()
}

fn to_pascal_case(s: &str) -> String {
    let mut pascal = String::new();
    let mut capitalize = true;
    for ch in s.chars() {
        if ch == '_' {
            capitalize = true;
        } else if capitalize {
            pascal.push(ch.to_ascii_uppercase());
            capitalize = false;
        } else {
            pascal.push(ch);
        }
    }
    pascal
}

fn get_rename_value(attrs: &[Attribute]) -> Option<String> {
    for attr in attrs {
        if attr.path().is_ident("serde") {
            // 正确的访问方式：直接访问字段 attr.meta
            if let Meta::List(list) = &attr.meta {
                if let Ok(Meta::NameValue(nv)) = list.parse_args::<Meta>() {
                    if nv.path.is_ident("rename") {
                        if let syn::Expr::Lit(lit) = nv.value {
                            if let Lit::Str(lit_str) = lit.lit {
                                return Some(lit_str.value());
                            }
                        }
                    }
                }
            }
        }
    }
    None
}

/// 从 syn::Ident 中获取其“语义名称”，即去除 `r#` 前缀。
fn get_semantic_name(ident: &Ident) -> String {
    ident.to_string().trim_start_matches("r#").to_owned()
}

pub fn derive_field_enum(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;
    let enum_name = Ident::new(&format!("{name}Field"), name.span());

    let (identifiers, attributes): (Vec<Ident>, Vec<Vec<Attribute>>) = match &input.data {
        Data::Struct(s) => match &s.fields {
            Fields::Named(fields) => fields
                .named
                .iter()
                .map(|f| (f.ident.as_ref().unwrap().clone(), f.attrs.clone()))
                .unzip(),
            _ => panic!("FieldEnum 只支持有命名字段的 struct"),
        },
        Data::Enum(e) => e
            .variants
            .iter()
            .map(|v: &Variant| (v.ident.clone(), v.attrs.clone()))
            .unzip(),
        _ => panic!("FieldEnum 只能用于 struct 或 enum"),
    };

    if identifiers.is_empty() {
        return quote! {
            #[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
            pub enum #enum_name {}

            impl #enum_name {
                pub fn as_str(&self) -> &'static str {
                    match *self {}
                }
            }

            impl std::fmt::Display for #enum_name {
                 fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                    write!(f, "{}", self.as_str())
                }
            }
        }
        .into();
    }

    let enum_variants = identifiers.iter().map(|ident| {
        // 使用去除了 r# 的语义名称来生成 PascalCase 变体名
        let semantic_name = get_semantic_name(ident);
        let variant_name_str = to_pascal_case(&semantic_name);
        Ident::new(&variant_name_str, ident.span())
    });

    let match_arms = identifiers
        .iter()
        .zip(attributes.iter())
        .map(|(ident, attrs)| {
            let semantic_name = get_semantic_name(ident);
            let variant_name_str = to_pascal_case(&semantic_name);
            let variant_name = Ident::new(&variant_name_str, ident.span());
            // 最终的字符串输出，优先使用 rename，否则回退到语义名称
            let field_name_str = get_rename_value(attrs).unwrap_or(semantic_name);

            quote! { Self::#variant_name => #field_name_str }
        });

    let generated_code = quote! {
        #[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
        pub enum #enum_name {
            #(#enum_variants),*
        }

        impl #enum_name {
            pub fn as_str(&self) -> &'static str {
                match self {
                    #(#match_arms),*
                }
            }
        }

        impl std::fmt::Display for #enum_name {
            fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                write!(f, "{}", self.as_str())
            }
        }
    };

    generated_code.into()
}
