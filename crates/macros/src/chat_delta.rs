use heck::{ToShoutySnakeCase, ToSnakeCase};
use proc_macro::TokenStream;
use quote::quote;
use syn::{Data, DataEnum, DeriveInput, Expr, ExprLit, Ident, Lit, Meta, parse_macro_input};

pub fn derive_chat_delta(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = input.ident;

    let variants = match input.data {
        Data::Enum(DataEnum { variants, .. }) => variants,
        _ => panic!("ChatDelta 只能用于枚举类型"),
    };

    let variant_names: Vec<Ident> = variants.iter().map(|v| v.ident.clone()).collect();
    let const_names: Vec<Ident> = variant_names
        .iter()
        .map(|v| {
            let name = v.to_string().to_shouty_snake_case();
            syn::parse_str::<Ident>(&name).unwrap()
        })
        .collect();

    let struct_fields: Vec<Ident> = variant_names
        .iter()
        .map(|v| {
            let name = v.to_string().to_snake_case();
            syn::parse_str::<Ident>(&name).unwrap()
        })
        .collect();

    // 从属性中获取标签值，如果没有则使用 snake_case 作为默认值
    let const_values: Vec<String> = variants
        .iter()
        .map(|v| {
            v.attrs
                .iter()
                .find(|attr| attr.path().is_ident("tag"))
                .and_then(|attr| {
                    if let Meta::NameValue(meta) = &attr.meta {
                        if let Expr::Lit(ExprLit {
                            lit: Lit::Str(lit), ..
                        }) = &meta.value
                        {
                            return Some(lit.value());
                        }
                    }
                    None
                })
                .unwrap_or_else(|| v.ident.to_string().to_snake_case())
        })
        .collect();

    let const_bytes: Vec<proc_macro2::TokenStream> = const_values
        .iter()
        .map(|s| {
            let bytes = s.as_bytes();
            let byte_tokens = bytes.iter().map(|b| quote! { #b }).collect::<Vec<_>>();
            quote! { &[ #(#byte_tokens),* ] }
        })
        .collect();

    let expanded = quote! {
        impl #name {
            #(
                pub const #const_names: &'static [u8] = #const_bytes;
            )*

            pub const TAGS: &'static [&'static [u8]] = &[
                #(Self::#const_names),*
            ];

            pub fn append_or_new(&mut self, other: Self) -> Option<Self> {
                match (self, other) {
                    #(
                        (#name::#variant_names(a), #name::#variant_names(b)) => {
                            a.push_str(&b);
                            None
                        }
                    )*
                    (_, other) => Some(other),
                }
            }

            pub fn split_into(self) -> (&'static [u8], String) {
                match self {
                    #(
                        #name::#variant_names(text) => {
                            (Self::#const_names, text)
                        }
                    )*
                }
            }

            pub fn split(&self) -> (&'static [u8], &str) {
                match &self {
                    #(
                        #name::#variant_names(text) => {
                            (Self::#const_names, text)
                        }
                    )*
                }
            }

            pub const fn get_builder(tag: &'static [u8]) -> fn(String) -> #name {
                match tag {
                    #(
                        Self::#const_names => #name::#variant_names,
                    )*
                    _ => unreachable!(),
                }
            }
        }

        #[derive(Default, Debug, Clone)]
        pub struct CombinedDeltas {
            #(
                pub #struct_fields: String,
            )*
        }

        impl CombinedDeltas {
            pub fn from_deltas(deltas: &[#name]) -> Self {
                let mut combined = CombinedDeltas::default();
                for delta in deltas {
                    match &delta {
                        #(
                            #name::#variant_names(text) => combined.#struct_fields.push_str(text),
                        )*
                    }
                }
                combined
            }
        }
    };

    TokenStream::from(expanded)
}
