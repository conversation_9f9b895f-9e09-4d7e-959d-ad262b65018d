[package]
name = "error"
version = "0.1.0"
edition = "2024"
publish = false

[lib]
name = "error"
path = "src/lib.rs"

[dependencies]
anyhow = { workspace = true }
async-openai = { workspace = true }
bson = { workspace = true }
config = { workspace = true }
jsonschema = { workspace = true }
minio = { workspace = true }
mongodb = { workspace = true }
nacos-sdk = { workspace = true }
pyo3 = { workspace = true }
pythonize = { workspace = true }
redis = { workspace = true }
regex = { workspace = true }
reqwest = { workspace = true }
reqwest-eventsource = { workspace = true }
reqwest-websocket = { workspace = true }
rmcp = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
uuid = { workspace = true }