#![allow(dead_code)]

use bson::doc;
use futures::TryStreamExt;
use macros::FieldEnum;
use mongodb::options::{ReplaceOptions, UpdateModifications, WriteModel};
use mongodb::{Client, Collection, bson};
use serde::de::DeserializeOwned;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::migration::exec_up;

pub mod document;
mod helpers;
mod migration;

pub const CONTENT_LIMIT_SIZE: usize = 1024 * 1024;

pub const ID: &str = "_id";
pub const SET: &str = "$set";
pub const CONCAT: &str = "$concat";
pub const MATCH: &str = "$match";
pub const LOOKUP: &str = "$lookup";
pub const UNWIND: &str = "$unwind";
pub const FACET: &str = "$facet";
pub const PROJECT: &str = "$project";
pub const REGEX: &str = "$regex";
pub const EXPR: &str = "$expr";
pub const EQ: &str = "$eq";
pub const NE: &str = "$ne";
pub const SORT: &str = "$sort";
pub const SKIP: &str = "$skip";
pub const LIMIT: &str = "$limit";
pub const COUNT: &str = "$count";
pub const IN: &str = "$in";
pub const GROUP: &str = "$group";
pub const ADD_TO_SET: &str = "$addToSet";
pub const PUSH: &str = "$push";
pub const OPTIONS: &str = "$options";

#[derive(Debug, Clone)]
pub struct DocumentRepository {
    database: mongodb::Database,
    client: mongodb::Client,
}

impl DocumentRepository {
    pub async fn init(mongodb_url: String) -> error::Result<DocumentRepository> {
        let client = match Client::with_uri_str(mongodb_url.clone()).await {
            Ok(client) => client,
            Err(e) => return Err(error::MongoError::SeverError(e).into()),
        };
        let database = client.database("agent_runner");
        tracing::info!("connect to {mongodb_url:?}");

        let repository = DocumentRepository { database, client };
        exec_up(&repository).await?;

        Ok(repository)
    }

    pub async fn client(&self) -> Client {
        self.client.clone()
    }

    pub async fn collection<T>(&self) -> Collection<T>
    where
        T: Document,
    {
        self.database.collection(T::COLLECTION_NAME)
    }

    pub async fn insert_one<D>(&self, document: D) -> error::Result<()>
    where
        D: Document,
    {
        match self.collection::<D>().await.insert_one(document).await {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("insert_one failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn insert_many<D>(&self, documents: impl IntoIterator<Item = D>) -> error::Result<()>
    where
        D: Document,
    {
        match self.collection::<D>().await.insert_many(documents).await {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("insert_many failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn find_one<D>(&self, filter: bson::Document) -> error::Result<Option<D>>
    where
        D: Document,
    {
        match self.collection::<D>().await.find_one(filter).await {
            Ok(result) => Ok(result),
            Err(e) => {
                tracing::error!("find_one failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn find<D>(&self, filter: bson::Document) -> error::Result<Vec<D>>
    where
        D: Document,
    {
        match self.collection::<D>().await.find(filter).await {
            Ok(result) => match result.try_collect().await {
                Ok(result) => Ok(result),
                Err(e) => Err(error::MongoError::SeverError(e).into()),
            },
            Err(e) => {
                tracing::error!("find failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn update_one<D>(
        &self,
        filter: bson::Document,
        update: bson::Document,
    ) -> error::Result<()>
    where
        D: Document,
    {
        match self
            .collection::<D>()
            .await
            .update_one(filter, update)
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("update_one failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn update_many<D>(
        &self,
        query: bson::Document,
        update: impl Into<UpdateModifications>,
    ) -> error::Result<()>
    where
        D: Document,
    {
        match self
            .collection::<D>()
            .await
            .update_many(query, update)
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("update_many failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn bulk_write(
        &self,
        models: impl IntoIterator<Item = impl Into<WriteModel>>,
    ) -> error::Result<()> {
        match self.client.bulk_write(models).await {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("bulk write failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn replace_one<D>(&self, filter: bson::Document, replacement: D) -> error::Result<()>
    where
        D: Document,
    {
        match self
            .collection::<D>()
            .await
            .replace_one(filter, replacement)
            .with_options(ReplaceOptions::builder().upsert(true).build())
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("replace_one failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn delete_by_id<D>(&self, id: Uuid) -> error::Result<()>
    where
        D: Document,
    {
        match self
            .collection::<D>()
            .await
            .delete_one(doc! {
                ID: id
            })
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("delete by id failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn delete<D>(&self, query: bson::Document) -> error::Result<()>
    where
        D: Document,
    {
        match self.collection::<D>().await.delete_many(query).await {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("delete failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn find_one_and_delete<D>(&self, filter: bson::Document) -> error::Result<Option<D>>
    where
        D: Document,
    {
        match self
            .collection::<D>()
            .await
            .find_one_and_delete(filter)
            .await
        {
            Ok(d) => Ok(d),
            Err(e) => {
                tracing::error!("find one and delete failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn append<D>(
        &self,
        id: Uuid,
        field: String,
        value_to_append: String,
    ) -> error::Result<()>
    where
        D: Document,
    {
        let filter = doc! {
            ID: id
        };
        let update_pipeline = vec![doc! {
            SET: {
                field.clone(): {
                    CONCAT: [ format!("${field}"), value_to_append ]
                }
            }
        }];

        match self
            .collection::<D>()
            .await
            .update_one(filter, update_pipeline)
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                tracing::error!("append failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn aggregate<D>(
        &self,
        pipeline: impl IntoIterator<Item = bson::Document>,
    ) -> error::Result<Vec<bson::Document>>
    where
        D: Document,
    {
        let pipeline = pipeline.into_iter().collect::<Vec<_>>();
        tracing::debug!("aggregate pipeline: {:?}", pipeline);
        let result = match self.collection::<D>().await.aggregate(pipeline).await {
            Ok(result) => result,
            Err(e) => {
                tracing::error!("aggregate failed: {e:?}");
                return Err(error::MongoError::SeverError(e).into());
            }
        };
        // let result: Vec<D> = result.into_iter().map(|d| bson::from_document(d)).collect();
        match result.try_collect().await {
            Ok(result) => Ok(result),
            Err(e) => {
                tracing::error!("try collect failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn count<D>(&self, filter: bson::Document) -> error::Result<u64>
    where
        D: Document,
    {
        match self.collection::<D>().await.count_documents(filter).await {
            Ok(count) => Ok(count),
            Err(e) => {
                tracing::error!("count failed: {e:?}");
                Err(error::MongoError::SeverError(e).into())
            }
        }
    }

    pub async fn load_conversation_by_id(
        &self,
        conversation_id: uuid::Uuid,
    ) -> error::Result<document::Conversation> {
        let r#match = doc! {
            MATCH:{
                ID:conversation_id
            }
        };

        let lookup = doc! {
            LOOKUP: {
              "from": "message",
              "let": {
                  "conversation_id": "$_id"
              },
              "pipeline": [
                {
                  MATCH: {
                    EXPR: {
                      EQ: [
                        "$conversation_id",
                        "$$conversation_id"
                      ]
                    }
                  }
                },
                {
                  LOOKUP: {
                    "from": "content",
                    "let": {
                      "message_id": "$_id"
                    },
                    "pipeline": [
                      {
                        MATCH: {
                          EXPR: {
                            EQ: [
                              "$message_id",
                              "$$message_id"
                            ]
                          }
                        }
                      }
                    ],
                    "as": "content_datas"
                  }
                },
                {
                    "$addFields": {
                        "content_ids": "$content_datas._id"
                    }
                },
                {
                    PROJECT: {
                        "content_datas": 0
                    }
                }
              ],
              "as": "messages"
            }
        };

        let pipeline = [r#match, lookup];

        let result = match self.aggregate::<document::Conversation>(pipeline).await {
            Ok(result) => result,
            Err(e) => return Err(e),
        };

        let mut conversation: document::Conversation = match result.first() {
            Some(document) => bson::from_document(document.clone()).map_err(|e| {
                tracing::error!("{e:?}");
                error::MongoError::SeverError(e.into())
            })?,
            None => {
                return {
                    tracing::error!("conversation {conversation_id} not found");
                    Err(error::ConversationError::NotFound(conversation_id).into())
                };
            }
        };

        for message in &mut conversation.messages {
            let content_ids = message.content_ids.clone();
            if !content_ids.is_empty() {
                let contents = self
                    .find::<document::Content>(doc! {
                        ID: {
                            IN: content_ids,
                        }
                    })
                    .await?;
                message.contents = contents;
            }
        }

        Ok(conversation)
    }

    pub async fn update_full_conversation(
        &self,
        documents: (
            document::Conversation,
            Vec<(document::Message, Vec<document::Content>)>,
        ),
    ) -> error::Result<()> {
        // let conversation = documents.0;
        // self.replace_one(doc!{
        //     ID: conversation.id,
        // }, conversation)
        // .await?;

        for (_message, contents) in documents.1 {
            for content in contents {
                self.replace_one(
                    doc! {
                        ID: content.id,
                    },
                    content,
                )
                .await?;
            }
        }

        Ok(())
    }

    pub async fn find_all_id_by_condition(
        &self,
        filter: bson::Document,
    ) -> error::Result<(Vec<Uuid>, Vec<Uuid>, Vec<Uuid>)> {
        let r#match = doc! {
            MATCH: filter
        };

        let lookup_message = doc! {
            LOOKUP: {
                "from": "message",
                "localField": "_id",
                "foreignField": "conversation_id",
                "as": "messages",
            }
        };

        let unwind_message = doc! {
            UNWIND: "$messages"
        };

        let lookup_content = doc! {
            LOOKUP: {
                "from": "content",
                "localField": "messages._id",
                "foreignField": "message_id",
                "as": "contents",
            }
        };

        let unwind_content = doc! {
            UNWIND: "$contents"
        };

        // let project = doc! {
        //     PROJECT: {
        //         ID: 0,
        //         "conversation_id": "$_id",
        //         "message_id": "$messages._id",
        //         "content_id": "$contents._id",
        //     }
        // };

        let group = doc! {
            GROUP: {
                ID: bson::Bson::Null,
                  "conversation_ids": {
                      ADD_TO_SET: "$_id"
                  },
                  "message_ids": {
                      ADD_TO_SET: "$messages._id"
                  },
                  "content_ids": {
                      ADD_TO_SET: "$contents._id"
                  }
            }
        };

        let pipeline = vec![
            r#match,
            lookup_message,
            unwind_message,
            lookup_content,
            unwind_content,
            // project,
            group,
        ];

        #[derive(Clone, Debug, Serialize, Deserialize, FieldEnum)]
        struct Ids {
            #[serde(with = "crate::helpers::uuid_vec")]
            pub conversation_ids: Vec<Uuid>,
            #[serde(with = "crate::helpers::uuid_vec")]
            pub message_ids: Vec<Uuid>,
            #[serde(with = "crate::helpers::uuid_vec")]
            pub content_ids: Vec<Uuid>,
        }

        let mut cursor = self
            .collection::<document::Conversation>()
            .await
            .aggregate(pipeline)
            .await
            .map_err(error::MongoError::SeverError)?;

        if let Some(document) = cursor
            .try_next()
            .await
            .map_err(error::MongoError::SeverError)?
        {
            let ids: Ids = bson::from_document(document).map_err(error::MongoError::BsonDeError)?;
            Ok((ids.conversation_ids, ids.message_ids, ids.content_ids))
        } else {
            Err(error::MongoError::RecordNotFound_.into())
        }
    }
}

pub async fn check_document_size<T>(document: &T) -> error::Result<()>
where
    T: Serialize,
{
    let vec = bson::to_vec(document).map_err(error::MongoError::BsonSerError)?;
    if vec.len() > CONTENT_LIMIT_SIZE {
        return Err(error::MongoError::SizeExceededError(vec.len()).into());
    }
    Ok(())
}

pub trait Document: Serialize + DeserializeOwned + Send + Sync {
    const COLLECTION_NAME: &'static str;

    fn to_document(&self) -> error::Result<bson::Document> {
        match bson::to_document(self) {
            Ok(document) => Ok(document),
            Err(e) => Err(error::MongoError::BsonSerError(e).into()),
        }
    }
}

#[macro_export]
macro_rules! from_bson {
    ($value:expr) => {
        bson::from_bson($value).map_err(error::MongoError::BsonDeError)?
    };
}

#[macro_export]
macro_rules! to_bson {
    ($value:expr) => {
        bson::to_bson($value).map_err(error::MongoError::BsonSerError)?
    };
}
