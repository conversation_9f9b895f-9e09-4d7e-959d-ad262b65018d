use std::{
    pin::Pin,
    task::{Context, Poll},
};

use bytes::{Buf, BufMut, BytesMut};
use futures::{Stream, StreamExt as _};
use macros::ChatDelta;
use pin_project_lite::pin_project;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>)]
pub enum ChatDelta {
    #[tag = "text"]
    Text(String),
    #[tag = "think"]
    Think(String),
    #[tag = "json"]
    Json(String),
    #[tag = "code"]
    Code(String),
    #[tag = "branch"]
    Branch(String),
    #[tag = "verify"]
    Verify(String),
    #[tag = "verify_think"]
    VerifyThink(String),
    #[tag = "confidence"]
    Confidence(String),
    #[tag = "require-confirm"]
    RequireConfirm(String),
    #[tag = "title"]
    Title(String),
    #[tag = "details"]
    Details(String),
    #[tag = "metadata"]
    Metadata(String),
}

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize, Default)]
pub struct ChatTextMetadata {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub workflow: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub r#type: Option<String>,
}

const TAG_START: u8 = b'<';
const TAG_END: u8 = b'>';
const TAG_CLOSE_HINT: u8 = b'/';

pub enum ParseState {
    Outside {
        cache: Option<BytesMut>,
    },
    TagOpen {
        candidate_tags: Vec<&'static [u8]>,
        checked_bytes_length: usize,
    },
    TagContent {
        tag: &'static [u8],
        delta_builder: fn(String) -> ChatDelta,
    },
    TagClose {
        tag: &'static [u8],
        delta_builder: fn(String) -> ChatDelta,
        checked_bytes_length: usize,
        has_close_hint: bool,
    },
}

pin_project! {
    pub struct ChatParser<S> {
        state: ParseState,
        #[pin]
        input_stream: S,
        pending_deltas: Vec<ChatDelta>,
    }
}

impl<S> ChatParser<S>
where
    S: Stream<Item = error::Result<String>>,
{
    pub fn new(input_stream: S) -> Self {
        Self {
            state: ParseState::Outside { cache: None },
            input_stream,
            pending_deltas: Vec::new(),
        }
    }

    pub fn new_with_state(input_stream: S, state: ParseState) -> Self {
        Self {
            state,
            input_stream,
            pending_deltas: Vec::new(),
        }
    }

    fn process(state: &mut ParseState, chunk: String) -> Vec<ChatDelta> {
        let mut deltas = Vec::new();
        let mut chunk = chunk.as_bytes();

        'l: loop {
            if chunk.is_empty() {
                break;
            }

            match state {
                ParseState::Outside { cache } => {
                    // 查找下一个标签的开始位置
                    if let Some(start) = chunk.iter().position(|&b| b == TAG_START) {
                        // 处理标签前的文本
                        if start > 0 {
                            let bytes = if let Some(bytes) = cache {
                                bytes.put_slice(&chunk[..start]);
                                bytes
                            } else {
                                let mut bytes = BytesMut::new();
                                bytes.put_slice(&chunk[..start]);
                                cache.insert(bytes)
                            };

                            let text = String::from_utf8_lossy(bytes);
                            deltas.push(ChatDelta::Text(text.to_string()));

                            *cache = None;
                        }

                        chunk.advance(start + 1); // 跳过 '<'

                        *state = ParseState::TagOpen {
                            candidate_tags: ChatDelta::TAGS.to_vec(),
                            checked_bytes_length: 0,
                        };
                    } else {
                        // 没有找到标签，直接返回剩余的文本
                        let bytes = if let Some(bytes) = cache {
                            bytes.put_slice(chunk);
                            bytes
                        } else {
                            let mut bytes = BytesMut::new();
                            bytes.put_slice(chunk);
                            cache.insert(bytes)
                        };

                        let text = String::from_utf8_lossy(bytes);
                        deltas.push(ChatDelta::Text(text.to_string()));

                        *cache = None;

                        chunk.advance(chunk.len());
                        break 'l;
                    }
                }

                // 已经检查到标签开始, 开始逐字节检查可能的标签
                // 已经检查过的字节长度为 checked_bytes_length 并且从 chunk 中移除
                // candidate_tags 是一个包含所有可能标签的向量
                // checked_bytes_length 表示所有候选标签检查到第几个字节, 不是 chunk 的长度
                // 继续检查当前 chunk 中的字节, 从 0 开始检查
                ParseState::TagOpen {
                    candidate_tags,
                    checked_bytes_length,
                } => {
                    if candidate_tags.is_empty() {
                        break 'l;
                    }

                    let next_byte = *chunk.first().unwrap();

                    chunk.advance(1);

                    match next_byte {
                        TAG_END => {
                            let tag = candidate_tags
                                .iter()
                                .find(|&&tag| tag.len() == *checked_bytes_length);
                            if let Some(tag) = tag {
                                let delta_builder = ChatDelta::get_builder(tag);
                                *state = ParseState::TagContent { tag, delta_builder }
                            } else {
                                let mut bytes = BytesMut::new();
                                bytes.put_u8(TAG_START);
                                bytes.put_slice(&candidate_tags[0][..*checked_bytes_length]);
                                bytes.put_u8(TAG_END);
                                deltas.push(ChatDelta::Text(
                                    String::from_utf8_lossy(&bytes).to_string(),
                                ));

                                *state = ParseState::Outside { cache: None };
                            }
                        }
                        _ => {
                            let next_candidate_tags = candidate_tags
                                .iter()
                                .filter(|&&tag| {
                                    // 检查当前字节是否与候选标签的下一个字节匹配
                                    if tag.len() > *checked_bytes_length {
                                        tag[*checked_bytes_length] == next_byte
                                    } else {
                                        false
                                    }
                                })
                                .cloned()
                                .collect::<Vec<_>>();

                            if next_candidate_tags.is_empty() {
                                // 没有匹配的候选标签，返回到外部状态
                                let mut bytes = BytesMut::new();
                                bytes.put_u8(TAG_START);
                                bytes.put_slice(&candidate_tags[0][..*checked_bytes_length]);
                                bytes.put_u8(next_byte);
                                *state = ParseState::Outside { cache: Some(bytes) };
                            } else {
                                // 更新候选标签和检查字节长度
                                *candidate_tags = next_candidate_tags;
                                *checked_bytes_length += 1;
                            }
                        }
                    }
                }

                ParseState::TagContent { tag, delta_builder } => {
                    if let Some(start) = chunk.iter().position(|&b| b == TAG_START) {
                        // 处理标签内容前的文本
                        if start > 0 {
                            let text = String::from_utf8_lossy(&chunk[..start]);
                            deltas.push(delta_builder(text.to_string()));
                        }

                        chunk.advance(start + 1); // 跳过 '<'

                        *state = ParseState::TagClose {
                            tag: core::mem::take(tag),
                            delta_builder: *delta_builder,
                            has_close_hint: false,
                            checked_bytes_length: 0,
                        };
                    } else {
                        // 没有找到下一个标签，直接返回剩余的文本
                        let text = String::from_utf8_lossy(chunk);
                        deltas.push(delta_builder(text.to_string()));
                        break 'l;
                    }
                }
                ParseState::TagClose {
                    tag,
                    delta_builder,
                    has_close_hint,
                    checked_bytes_length,
                } => {
                    if !*has_close_hint {
                        // next byte must be TAG_CLOSE_HINT
                        if *chunk.first().unwrap() == TAG_CLOSE_HINT {
                            chunk.advance(1); // 跳过 '/'
                            *has_close_hint = true;
                        } else {
                            // 不是关闭标签，直接返回到外部状态
                            deltas.push(delta_builder(
                                String::from_utf8_lossy(&[TAG_START]).to_string(),
                            ));
                            *state = ParseState::TagContent {
                                tag: core::mem::take(tag),
                                delta_builder: *delta_builder,
                            };
                        }
                    } else {
                        let unchecked_bytes_length = tag.len() - *checked_bytes_length;

                        if unchecked_bytes_length == 0 {
                            // 检查到完整的关闭标签
                            let next_byte = *chunk.first().unwrap();
                            if next_byte == TAG_END {
                                // 找到标签结束符，返回到外部状态
                                chunk.advance(1); // 跳过 '>'
                                *state = ParseState::Outside { cache: None };
                            } else {
                                // 不是标签结束符，直接返回到外部状态
                                deltas.push(delta_builder(
                                    String::from_utf8_lossy(
                                        &[&[TAG_START, TAG_CLOSE_HINT], *tag].concat(),
                                    )
                                    .to_string(),
                                ));
                                *state = ParseState::TagContent {
                                    tag: core::mem::take(tag),
                                    delta_builder: *delta_builder,
                                };
                            }
                        } else {
                            // 检查下一个字节是否与关闭标签匹配
                            let next_byte = *chunk.first().unwrap();
                            if tag[*checked_bytes_length] == next_byte {
                                *checked_bytes_length += 1;
                                chunk.advance(1); // 跳过下一个字节
                            } else {
                                // 找到不匹配的字节，直接返回到外部状态
                                deltas.push(delta_builder(
                                    String::from_utf8_lossy(&[TAG_START]).to_string(),
                                ));
                                *state = ParseState::TagContent {
                                    tag: core::mem::take(tag),
                                    delta_builder: *delta_builder,
                                };
                            }
                        }
                    }
                }
            }
        }

        deltas
    }
}

impl<S> Stream for ChatParser<S>
where
    S: Stream<Item = error::Result<String>>,
{
    type Item = error::Result<ChatDelta>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        // 使用 pin_project 生成的投影访问字段
        let mut this = self.project();

        // 先检查是否有待处理的 deltas
        if !this.pending_deltas.is_empty() {
            return Poll::Ready(Some(Ok(this.pending_deltas.remove(0))));
        }

        Poll::Ready(loop {
            match futures::ready!(this.input_stream.as_mut().poll_next(cx)) {
                Some(Ok(chunk)) => {
                    let mut deltas = Self::process(this.state, chunk);

                    if !deltas.is_empty() {
                        // 保存剩余的deltas供下次返回
                        if deltas.len() > 1 {
                            this.pending_deltas.extend(deltas.drain(1..));
                        }
                        break Some(Ok(deltas.remove(0)));
                    }
                }
                Some(Err(e)) => return Poll::Ready(Some(Err(e))),
                None => break None,
            }
        })
    }
}

pub fn parse_stream_simple(input: impl Stream<Item = String>) -> impl Stream<Item = ChatDelta> {
    ChatParser::new(input.map(Ok)).map(|s| s.unwrap())
}

pub fn parse_stream(
    input: impl Stream<Item = error::Result<String>>,
) -> impl Stream<Item = error::Result<ChatDelta>> {
    ChatParser::new(input)
}

pub fn parse_stream_with_state(
    input: impl Stream<Item = error::Result<String>>,
    state: ParseState,
) -> impl Stream<Item = error::Result<ChatDelta>> {
    ChatParser::new_with_state(input, state)
}

#[cfg(test)]
mod tests {
    use super::*;
    use futures::StreamExt;
    use futures::stream;

    async fn collect_deltas_by_char(input: &str) -> Vec<ChatDelta> {
        let stream = stream::iter(input.chars().map(|c| Ok(c.to_string())));
        let parser = ChatParser::new(stream);
        tokio::pin!(parser);
        let mut deltas = Vec::new();
        while let Some(Ok(delta)) = parser.next().await {
            deltas.push(delta);
        }
        deltas
    }

    async fn collect_deltas_by_tag(input: Vec<String>) -> Vec<ChatDelta> {
        let stream = stream::iter(input);
        let parser = ChatParser::new(stream.map(Ok));
        tokio::pin!(parser);
        let mut deltas = Vec::new();
        while let Some(Ok(delta)) = parser.next().await {
            deltas.push(delta);
        }
        deltas
    }

    async fn collect_deltas_complete(input: &str) -> Vec<ChatDelta> {
        let stream = stream::once(async { input.to_string() });
        let parser = ChatParser::new(stream.map(Ok));
        tokio::pin!(parser);
        let mut deltas = Vec::new();
        while let Some(Ok(delta)) = parser.next().await {
            deltas.push(delta);
        }
        deltas
    }

    fn combine_deltas(deltas: &[ChatDelta]) -> CombinedDeltas {
        CombinedDeltas::from_deltas(deltas)
    }

    #[tokio::test]
    async fn test_parse_think() {
        let input = "<think>hello</think>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);

        println!("Combined content: {combined:?}");

        // 验证组合后的内容
        assert_eq!(combined.think, "hello");

        // 确保至少有一个Think类型的delta
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Think(_))));
    }

    #[tokio::test]
    async fn test_parse_json() {
        let input = "<json>{\"key\":\"value\"}</json>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.json, "{\"key\":\"value\"}");
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Json(_))));
    }

    #[tokio::test]
    async fn test_parse_branch() {
        let input = "<branch>main</branch>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.branch, "main");
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Branch(_))));
    }

    #[tokio::test]
    async fn test_parse_verify() {
        let input = "<verify>data validation check</verify>";
        // 使用完整输入测试
        let deltas = collect_deltas_complete(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.verify, "data validation check");
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Verify(_))));
    }

    #[tokio::test]
    async fn test_parse_confidence() {
        let input = "<confidence>0.95</confidence>";
        // 使用标签块输入测试
        let deltas = collect_deltas_by_tag(vec![input.to_string()]).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.confidence, "0.95");
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Confidence(_))));
    }

    #[tokio::test]
    async fn test_parse_require_confirm() {
        let input = "<require-confirm>Are you sure?</require-confirm>";
        // 使用标签块输入测试
        let deltas = collect_deltas_by_tag(vec![input.to_string()]).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.require_confirm, "Are you sure?");
        assert!(
            deltas
                .iter()
                .any(|d| matches!(d, ChatDelta::RequireConfirm(_)))
        );
    }

    #[tokio::test]
    async fn test_parse_title() {
        let input = "<title>My Title</title>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.title, "My Title");
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Title(_))));
    }

    #[tokio::test]
    async fn test_parse_details() {
        let input = "<details>Some detailed information</details>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.details, "Some detailed information");
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Details(_))));
    }

    #[tokio::test]
    async fn test_parse_all_tags() {
        let input = vec![
            "<think>thought</think>".to_string(),
            "<json>{\"key\":\"value\"}</json>".to_string(),
            "<branch>main</branch>".to_string(),
            "<verify>check</verify>".to_string(),
            "<confidence>0.98</confidence>".to_string(),
            "<require-confirm>confirm</require-confirm>".to_string(),
            "<title>My Title</title>".to_string(),
            "<details>Some detailed information</details>".to_string(),
        ];
        // 使用标签块输入测试
        let deltas = collect_deltas_by_tag(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.think, "thought");
        assert_eq!(combined.json, "{\"key\":\"value\"}");
        assert_eq!(combined.branch, "main");
        assert_eq!(combined.verify, "check");
        assert_eq!(combined.confidence, "0.98");
        assert_eq!(combined.require_confirm, "confirm");
        assert_eq!(combined.title, "My Title");
        assert_eq!(combined.details, "Some detailed information");
    }

    #[tokio::test]
    async fn test_parse_multiple() {
        let input = "<think>hello</think><json>{\"key\":\"value\"}</json><branch>main</branch>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);
        // 验证各部分组合后的内容
        assert_eq!(combined.think, "hello");
        assert_eq!(combined.json, "{\"key\":\"value\"}");
        assert_eq!(combined.branch, "main");

        // 确保有所有类型的delta
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Think(_))));
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Json(_))));
        assert!(deltas.iter().any(|d| matches!(d, ChatDelta::Branch(_))));
    }

    #[tokio::test]
    async fn test_parse_mixed_input_methods() {
        // 测试三种不同的输入方式是否产生一致的结果
        let input = "<think>Hello</think><json>{\"test\":true}</json>";

        // 字符流输入
        let char_result = collect_deltas_by_char(input).await;

        // 标签块输入
        let tag_result = collect_deltas_by_tag(vec![
            "<think>Hello</think>".to_string(),
            "<json>{\"test\":true}</json>".to_string(),
        ])
        .await;

        // 完整输入
        let complete_result = collect_deltas_complete(input).await;

        // 验证三种方式的结果一致
        let char_combined = combine_deltas(&char_result);
        let tag_combined = combine_deltas(&tag_result);
        let complete_combined = combine_deltas(&complete_result);

        assert_eq!(char_combined.think, "Hello");
        assert_eq!(tag_combined.think, "Hello");
        assert_eq!(complete_combined.think, "Hello");

        assert_eq!(char_combined.json, "{\"test\":true}");
        assert_eq!(tag_combined.json, "{\"test\":true}");
        assert_eq!(complete_combined.json, "{\"test\":true}");
    }

    // 测试大块文本的完整输入处理
    #[tokio::test]
    async fn test_large_content_complete() {
        let input = r#"<think>这是一个很长的文本内容
包含多行
可能还有特殊字符：!@#$%^&*()
</think>
<json>{"key": "value", "nested": {"array": [1,2,3]}}</json>"#;

        let deltas = collect_deltas_complete(input).await;
        let combined = combine_deltas(&deltas);

        assert!(combined.think.contains("这是一个很长的文本内容"));
        assert!(combined.think.contains("包含多行"));
        assert!(combined.json.contains("nested"));
    }

    // 测试标签块的错误处理
    #[tokio::test]
    async fn test_malformed_tag_blocks() {
        let input = vec![
            "<think>unfinished".to_string(),
            "<json>invalid>tag</json>".to_string(),
            "plain text".to_string(),
        ];

        let deltas = collect_deltas_by_tag(input).await;
        let combined = combine_deltas(&deltas);

        assert!(combined.think.contains("plain text"));
        assert!(!combined.think.is_empty());
        assert!(combined.text.is_empty());
        assert!(combined.json.is_empty());
    }

    // 测试不存在的标签
    #[tokio::test]
    async fn test_non_existent_tags() {
        let input = vec!["<non_existent_tag>content</non_existent_tag>".to_string()];

        let deltas = collect_deltas_by_tag(input).await;
        let combined = combine_deltas(&deltas);

        println!("{deltas:?}");
        println!("{combined:?}");

        assert_eq!(
            combined.text.trim(),
            "<non_existent_tag>content</non_existent_tag>"
        );
    }

    // 测试不存在的中文标签
    #[tokio::test]
    async fn test_chinese_tags() {
        let input = "<中文标签>内容</中文标签>";
        let deltas = collect_deltas_by_char(input).await;
        let combined = combine_deltas(&deltas);

        assert_eq!(combined.text, input);
    }
}
