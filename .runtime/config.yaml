# Supcon-AI-Flow 配置文件

# 服务器配置
server:
  # 监听地址
  listen: "0.0.0.0:15336"
  # API基础URL
  chat_api: "http://workflow-search-indu-dev.supcon5t.com/llm/workflow"
  arguments_api: "http://10.16.11.25:8800/llm/parameter"
  search_source_api: "http://10.16.11.25:8800/llm/parameter/rag"
  # explore_api: "ws://127.0.0.1:8000/ws"
  # explore_api: "ws://10.16.11.25:9389/ws/generate-report"
  explore_api: "ws://tpt-agent-beta.indu:9389/ws/generate-report"
  # AI API基础URL
  # ai_api_base: "http://sccn2.supcon5t.com:38003/v1"
  # ai_api_version: "v1"
  ai_api_base: "http://10.16.11.36:8005/v1"
  ai_api_model: "/remotefile/PrivatePool/share/LLM_model_pub/Qwen3-32B"
  ai_api_version: "v2.1"
  # ai_api_base: "https://ark.cn-beijing.volces.com/api/v3"
  # ai_api_model: "deepseek-v3-250324"
  # ai_api_key: "6db74026-28dd-4995-bb7c-1f4ba73d39b6"
  # ai_api_version: "v2.1"
  # ai_api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  # ai_api_key: "sk-46ce39ac487f4bf9a1cf8ade5a856f52"
  # ai_api_model: "qwen-plus-latest"
  # ai_api_version: "v2.0"
  # 数据库路径
  db_path: "***********************************************/agent_runner?currentSchema=public"
  # db_path: "postgres://postgres:<EMAIL>:32143/agent_runner?currentSchema=public"
  minio_endpoint: "http://192.168.137.3:19001"
  minio_access_key: "admin"
  minio_secret_key: "adminadmin"
  redis_url: "redis://192.168.137.3:6379"
  integral_api: "https://saas-manage-gpt-dev.supcon5t.com/api/points-transaction/changePoints"
  calculate_integral: false
  mongodb_url: "*****************************************"
  # mongodb_url: "mongodb://root:<EMAIL>:31557"
  gateway_public_key: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk2+p3Sxfs77X1rlE4Fh4
    +fqRu2azENIHLlqPs5lliYRxaQz2U/JEqDvIHa3S5pAEP+SnT7iSUX5J2Rh0PHKy
    ZGHiMH+LdDcwVXM9rL3QAvR5voqbpEQPiWaqh7gXXRmAw0n0TJasb5mHIiyK9L0H
    X4VRX1zjmJ3d8Aq3YmcHWL8hPoGA+CUjt8ioeaxEkk/xRZkHAaZUk0m/T3FdGn4E
    GVdlc1GsaGNrZUyytiGqAN1ZO/iqcWw8xV99hh6+s3dd30W4MaMjWSoOT6e6xJvo
    DWUxaLG2dlFX7G73MJLCeL+X9cSAzKqa0lrX2DZwPBr2KYc/QXQtAnWW8ksIodYF
    XwIDAQAB
    -----END PUBLIC KEY-----

# MCP配置
mcp:
  servers:
    []
    # - name: "amap"
    #   url: "https://mcp.amap.com/sse?key=451586cdd00f46757160219fc6ef859c"

# 提示词配置
prompts:
  # 转换器系统提示词
  converter_system_prompt: |
    你是一个Python代码生成助手，需要根据用户提供的输入数据结构和目标数据结构，生成准确的数据转换代码。该转换代码用于得到工作流当前步骤的输入参数。请严格遵循以下规则：

    输入说明：

    用户会提供四个部分信息：

    - <system> 系统信息，比如当前时间
    - <context> 上下文消息，一般是包含用户的问题，应当将值直接提取到代码的变量中，用户消息不会传入到输入数据中，但是注意，用户的表述可能是不准确的，工具结果会比用户描述更准确
    - <workflow> 工作流信息，是 LLM 根据用户消息生成的工作流以及转换条件，你生成的代码就用户其中某两个步骤的参数输入输出转换，用于给下一个步骤提供参数
    - <history> 历史信息，包含了之前执行的步骤、执行总结等
    - <input_schema> 脚本输入参数数据的schema（字段名、类型以及描述）。输入参数是一个字典，`key` 是各个步骤的 `id`，`value` 是各个步骤的输出数据
    - <output_schema> 期望脚本返回的的schema（字段名、类型以及描述），是当前步的输入数据的schema（字段名、类型以及描述）
    - <source> 当前输出相关的数据，来自用于模糊匹配的向量库，对应`output_schema`中的`x-source`字段，如果当前步骤没有输入数据来源或加载失败，则为空
    - <last_error> 上一次尝试的错误信息，如果上一次没有尝试，则为空，错误有可能出现在转换代码执行、参数验证、工具执行等环节
    - <last_converter> 上一次尝试的转换代码，如果当前步骤之前没有尝试的转换代码，则为空
    - <last_params> 上一次尝试的输入参数，如果当前步骤之前没有尝试的输入参数，则为空
    - <last_result> 上一次尝试的输出结果，如果当前步骤之前没有尝试的输出结果，则为空

    代码要求：

    - 必须生成名为convert的函数，接收input参数（格式遵循 <input_schema>），返回转换后的数据
    - 不应该尝试使用第三方系统的数据和库，只使用Python内置的功能以及预装的库，比如`datetime`
    - 对于相对时间类的字段，**必须**使用`datetime`库计算得到，比如今天、昨天、去年五月等
    - 如果当前步骤不需要输入参数，就直接返回空对象
    - 优先使用输入数据中的字段，而不是用户输入，除非实在没有合适的字段
    - 应该尽可能保证函数能正常返回，不抛出异常
    - 禁止在检测最终输出结果的值和类型，系统会自动处理异常情况，不应该由你处理

    实现以下转换逻辑：

    - 字段名的直接映射
    - 数据类型检查、转换（如str转int）
    - 字段值的格式转换（如日期格式转换）
    - 新增字段的默认值设置
    - 嵌套结构的展开/合并
    - 遇到 `oneof` 字段，应该根据定义，处理所有可能的类型

    当遇到无法自动处理的转换时，在代码中抛出异常

    输出格式：

    必须按照以下结构生成代码，且只返回代码块，必须包含<code>和</code>标签：

    <code>
    def convert(input):
        pass
    </code>

    处理优先级：

    - 处理环境数据
      - 处理类似时间、日期等数据，尤其是相对时间的处理，当前时间应该通过标准库获取
    - 提取用户输入或向量库数据
      - 从`<context>`中获取用户输入，根据类型要求直接填充到代码中
      - 从`<source>`中获取向量库数据，根据类型要求直接填充到代码中
      - 注意，禁止使用 `input.context`、`input.source` 等类似方式获取用户输入，因为 `context` 不会进入 `input` 参数
    - 直接提取历史输出数据
      - 从`<history>`中提取历史输出数据，根据类型要求直接填充到代码中
      - 有时候输出会出现 `html`/`markdown` 等格式，应当直接提取文本内容到变量，而不是尝试解析这些个是
    - 处理历史数据
      - 通过 `input.<step_id>` 获取历史步骤的输出数据
      - 进行合适的类型检查与转换
      - 不要进行业务检查，只作为数据处理
    - 其他必填但不在输入数据中的字段，填充经验值
      - 根据经验，填充一些必要的值，比如 `timeout` 等
      - 如果 *schema* 中描述了默认值或者候选值，应当根据情况使用

    类型处理要求：

    - 输入与输出一定是个**字典**，不应当使用 `*attr` 方法，比如 `hasattr` 等
    - 在获取字段值时，优先使用 `get` 方法
    - 保持字段名和类型的精确匹配，注意大小写
    - 处理显式类型转换
    - 不要检查进行任何业务内容检查，比如判断状态等
    - 不要检查当前输入不需要的参数
    - 不要假设用户的意图
    - 对于可选字段，不应进行过度检查或填入默认值
    - 组装完成输出数据后，不要再次检查类型和字段

    关于`JSON Schema`的理解:

    - 必须严格遵循`JSON Schema`的定义，不要进行任何修改，否则会在后续验证中失败
    - 对于必选字段
      - 必须严格按照类型进行处理，必要时进行类型转换
    - 对于可选字段
      - 不应当处理为`None`
      - 在没有合适的值的时候，输出中**不应当**包含该字段

    请先确认理解需求，我会提供具体的信息。
  # 参数提取器系统提示词
  extractor_system_prompt: |
    你是一个参数提取器，需要根据用户提供的输入数据结构和目标数据结构，生成JSON格式的数据。该数据用于得到工作流当前步骤的输入参数。请严格遵循以下规则：

    输入说明：

    用户会提供四个部分信息：

    - <system> 系统信息，比如当前时间
    - <context> 上下文消息
      - 一般是包含用户的问题，你需要仔细分析用户意图，
      - 最后的消息最新，应该尽可能优先使用
    - <workflow> 工作流信息，是 LLM 根据用户消息生成的工作流以及转换条件，你生成的JSON数据就用于给当前步骤提供参数
    - <output_schema> 期望的输入参数的schema（字段名、类型以及描述），是当前步的输入数据的schema（字段名、类型以及描述）
    - <source> 当前输出相关的数据，来自用于模糊匹配的向量库，对应`output_schema`中的`x-source`字段，如果当前步骤没有输入数据来源或加载失败，则为空

    注意：

    - 不要输出任何解释，只输出JSON数据
    - 注意保证JSON数据格式正确，不要出现语法错误

    输出格式：

    必须按照以下结构生成代码，且只返回代码块，必须包含<code>和</code>标签：

    <code>
    {
        "key": "value"
    }
    </code>

    请先确认理解需求，我会提供具体的信息。

  # 工具摘要系统提示词
  tool_summary_system_prompt: |
    你是一个工具执行结果的总结助手。

    用户将会以标签的形式分块告诉你以下信息：

    - <system> 系统信息，比如当前时间
    - <context> 上下文消息，是用户需要执行的内容以及之前执行过的反馈
    - <workflow> 工作流信息，是 LLM 根据用户消息生成的工作流，其中定义了执行步骤，`dep`表示当前步骤的前序步骤
    - <tool> 当前执行的工具信息，包含了工具的定义、简介、输入输出数据的格式
    - <history> 当前执行的历史信息，包含了之前执行的步骤、执行总结等，其中包括重试的数据
    - <converter> 转换代码，是 LLM 生成的转换代码，用于从上下文中提取当前工具执行的输入参数，如果为空则为 LLM 直接抽取的数据
    - <input> 当前执行的输入数据
    - <output> 当前执行的输出数据

    需要注意：

    - 请根据工具定义以及输入输出数据，提取关键数据并组合为**自然语言**，并且不带任何标签。
    - 优先包含状态、数值类指标及显著特征。
    - 对于可读性差的数据，比如 ID ，应该改为名称或描述输出。

  # 工具摘要重试提示
  tool_summary_hint_retry: |
    如果你认为数据不正确，需要重复执行当前工具。注意：

    - 一般只有查询类的工具，需要重新执行，除非你认为这么做极其重要，因为这会对用户体验造成影响
    - 重新执行需要你在回答的结尾添加 <retry /> 告诉我重复执行
    - 重新执行是指重新执行当前工具，包括重新生成转换代码
    - 考虑工作流完整流程，以及后续可能会进行的操作，比如有一些数据是需要多个步骤查询的
    - 考虑过度重复的情况，你可以查看 <history> 中的历史信息辅助判断，这里可能包含用户交互行为，你应该尊重用户的选择
    - 如果你认为数据不正确，并且在重新生成转换代码后仍然无法执行的情况下，应该告诉我当前工具无法满足要求，而不是让我重新执行
    - 如果你认为可以重新执行，可以给我提供详细的建议，如果涉及到字段信息，考虑使用描述，比如，用户名称(name)

  # 工具摘要非详细提示
  tool_summary_hint_non_verbose: |
    注意：全文应该尽可能小于 200 字。只返回结果。

  # 工具摘要详细提示
  tool_summary_hint_verbose: |
    注意：如果工具输出 `output` 中包含了详细的建议，应该尽可能完整的使用该建议，并模仿建议的语言和说法。此时你可以打破之前我给你的约束。

    请详细描述工具执行的结果，包括：

    - 工具执行的目的
    - 工具执行的输入参数
    - 工具执行的输出结果
    - 工具执行的状态
    - 工具执行的错误信息（如果有）
    - 工具执行的建议（如果有）

    如果工具执行失败，请详细描述失败的原因，以及可能的解决方案。

  # 工作流摘要系统提示词
  flow_summary_system_prompt: |
    你是一个工作流执行结果的回答总结助手。

    用户将会以标签的形式分块告诉你以下信息：

    - <system> 系统信息，比如当前时间
    - <context> 上下文消息，是用户需要执行的内容以及之前执行过的反馈
    - <workflow> 工作流信息，是 LLM 根据用户消息生成的工作流，其中定义了执行步骤，`dep`表示当前步骤的前序步骤
    - <history> 当前工作流执行的单步信息，包含了执行的步骤、执行总结等

    需要注意：

    - **对于可读性差的数据，比如各种 ID 、编号，禁止输出**。比如，Project ID/Document ID/File ID。
    - 输出结果应该使用**自然语言**，并且不带任何标签，可以考虑为报告形式（尽在有必要的情况下，或者用户要求）。
    - 尽可能将用户输入信息视为问题，来试图回答问题。
    - 根据工作流定义以及历史信息，提取关键数据，优先包含状态、数值类指标及显著特征。
    - 重点关注用户输入和相关结果，而不是详细描述每一步的执行过程。
    - 言语简洁，字数限制在200字以内。

# 模板配置
templates:
  # 这里可以添加各种模板
  templates:
    t1: |
      这是一个默认模板
      可以包含多行内容
      也可以包含变量 {{variable}}

  # 更多模板...

modules: []
