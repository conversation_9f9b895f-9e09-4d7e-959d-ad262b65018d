{"$schema": "./schema/modules.schema.json", "modules": [{"name": "DEMO", "base": "demo", "alias": ["demo"], "description": "描述用途", "version": "1.0.0", "config": {"base_url": "http://********:9097/"}}, {"name": "PID", "base": "pid", "alias": ["pid"], "description": "pid", "version": "1.0.0", "config": {"installed": false, "inner_token": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "config_host": "", "eval_host": "", "core_frontend_host": "", "agent_frontend_host": "", "minimal_host": "https://obp-test.supcon5t.com/xpt-tpt-pid/api"}}, {"name": "APC", "base": "apc", "alias": ["apc"], "description": "apc", "version": "1.0.0", "config": {"APC_inner_token": "Bearer 0cc24e64-9491-4565-97a1-edb4a7a44334", "APC_URL": "https://obp-test.supcon5t.com/xpt-tpt-apc/model-ident-api", "LLM_URL": "http://apc-business.indu:10028", "LLM_API_URL": "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004", "APC_AGENT_URL": "http://***********:10677", "APC_AGENT_TPT_TOKEN": "dda20fc9-94d1-4452-f29a-ca88e0b7260b", "APC_AGENT_TOKEN": "Bearer f4590963-4376-4550-83e1-44406ae107d2"}}, {"name": "OTS", "base": "ots", "alias": ["ots"], "description": "ots", "version": "1.0.0", "config": {"coreserver_host": "otscore.gpt", "httpserver_port": "9999", "webserver_port": "8121", "webclient_port": "8801", "agentserver_host": "", "trainserver_port": "1920"}}, {"name": "IDX", "base": "idx", "alias": ["idx"], "description": "idx", "version": "1.0.0", "config": {"IDX_URL": "http://tpt-index-v1.gpt:8080", "WEB_URL": "", "DATA_VEC_URL": "http://milvus.milvus:19530", "LLM_URL": "http://workflow-search.indu:8800", "VEC_TABLE_NAME": "milvus_m3_indicator"}}, {"name": "RTO", "base": "rto", "alias": ["rto"], "description": "rto", "version": "1.0.0", "config": {"autonomous_opt_url": "http://autonomous-opt.gpt:8080", "ts_data_eval_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=ts_data_eval_v2_py&built_in=0&time_out=600", "ts_data_process_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=ts_data_process_v4_py&built_in=0&time_out=600", "opt_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=TPT_optimize_2_py&built_in=0&time_out=600", "llm_api_url": "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004/v1/chat/completions", "llm_model_file": "qwen3", "llm_agent_opt_generate_vals_url": "http://rto.indu:9387/generate_optimization_params", "llm_agent_recommend_params": "http://rto.indu:9396/recommend_parameters", "llm_agent_auto_model_rec": "http://rto.indu:9396/auto_modeling_rec", "redis_host": "redis-master.devops", "redis_port": 6379, "redis_cluster": 0, "sse_model_train_log_url": "http://parameter-identification-tpt.obp:80/stream_train_logs/", "dissconn_sse_model_train_log_url": "http://parameter-identification-tpt.obp:80/disconnect_train_logs/"}}, {"name": "SPC", "base": "spc", "alias": ["spc"], "description": "spc", "version": "1.0.0", "config": {"spc_dataprocess_url": "http://parameter-identification-tpt.obp:80/call/app?name=data_process_and_data_split_api_online_py&built_in=1&time_out=6000", "spc_dataprocess_pid_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_process_data_PID_py&built_in=1&time_out=6000", "spc_forecast_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_forecast_py&built_in=1&time_out=6000", "spc_quantation_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_quantation_py&built_in=1&time_out=6000", "spc_identification_analysis_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_identification_analysis_py&built_in=1&time_out=6000", "spc_pid_identification_analysis_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_pid_identification_analysis_py&built_in=1&time_out=6000", "spc_classification_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_classification_py&built_in=1&time_out=6000", "spc_long_control_api_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_long_control_api_py&built_in=1&time_out=6000", "spc_para_check_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_para_check_py&built_in=1&time_out=6000", "spc_PID_auto_tune_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_PID_auto_tune_py&built_in=1&time_out=6000", "spc_select_feature_url": "http://parameter-identification-tpt.obp:80/call/app?name=spc_select_feature_py&built_in=1&time_out=6000", "tenant_id": "0", "redis_host": "redis-master.devops", "redis_port": 6379, "redis_db": 2}}, {"name": "Redesign", "base": "redesign", "alias": ["redesign"], "description": "redesign", "version": "1.0.0", "config": {"base_url": "http://parameter-identification-tpt.obp:80/call/app"}}, {"name": "ALARM_PRED", "base": "alarm_pred", "alias": ["alarm_pred"], "description": "alarm prediction", "version": "1.0.0", "config": {"ts_data_eval_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=ts_data_eval_v2_py&built_in=0&time_out=600", "ts_data_process_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=ts_data_process_v4_py&built_in=0&time_out=600", "ap_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=process_anomaly_warning_offline_py&&built_in=0&time_out=600", "llm_api_url": "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004/v1/chat/completions", "llm_model_file": "qwen3", "llm_agent_recommend_params": "http://rto.indu:9387/recommend_parameters", "llm_agent_auto_model_rec": "http://rto.indu:9387/auto_modeling_rec", "redis_host": "redis-master.devops", "redis_port": 6379, "redis_cluster": 0, "sse_model_train_log_url": "http://parameter-identification-tpt.obp:80/stream_train_logs/", "dissconn_sse_model_train_log_url": "http://parameter-identification-tpt.obp:80/disconnect_train_logs/"}}, {"name": "AUTOML", "base": "automl", "alias": ["automl"], "description": "auto model", "version": "1.0.0", "config": {"ts_data_eval_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=ts_data_eval_v2_py&built_in=0&time_out=600", "ts_data_process_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=ts_data_process_v4_py&built_in=0&time_out=600", "tpt_automl_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=tpt1x_inference_py&built_in=0&time_out=600", "automl_alg_exec_runtime_url": "http://parameter-identification-tpt.obp:80/call/app?name=AutoMLTimeSeriesPredictor_inference_py&built_in=0&time_out=600", "llm_api_url": "http://nlb-2weu6cb4a97uoz9cqu39kvmj.nlb.cn-beijing.volces.com:32004/v1/chat/completions", "llm_model_file": "qwen3", "llm_agent_recommend_params": "http://rto.indu:9387/recommend_parameters", "llm_agent_auto_model_rec": "http://rto.indu:9387/auto_modeling_rec", "redis_host": "redis-master.devops", "redis_port": 6379, "redis_cluster": 0, "sse_model_train_log_url": "http://parameter-identification-tpt.obp:80/stream_train_logs/", "dissconn_sse_model_train_log_url": "http://parameter-identification-tpt.obp:80/disconnect_train_logs/"}}, {"name": "EXAM", "base": "exam", "alias": ["exam"], "description": "exam", "version": "1.0.0", "config": {"exam_form_filling_url": "http://supcon-exam-indu.supcon5t.com/api/exam/get_exam_paper", "exam_process_received_form_url": "http://supcon-exam-indu.supcon5t.com/api/exam/get_task_status"}}, {"name": "LLM", "base": "llm", "alias": ["llm"], "description": "llm", "version": "1.0.0", "config": {}}, {"name": "BASIC", "base": "basic", "alias": ["basic"], "description": "basic", "version": "1.0.0", "config": {"web_base": "http://https-obp-web.frontend", "screenshot_api": "http://screenshot-service.indu:8000/screenshot"}}]}