{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "get_index_properties_by_names", "related_app": [{"name": "idx_query", "alias": "指标查询"}], "description": "获取系统中定义的全部指标", "params": {"type": "object", "description": "指标名称列表", "properties": {"indexNames": {"type": "array", "description": "指标名称", "x-source": ["milvus_m3_indicator"], "items": {"type": "string", "description": "单个指标名称"}}, "userIndexNames": {"type": "array", "description": "用户问题中的指标名称", "items": {"type": "string", "description": "用户问题中的单个指标名称"}}, "userinput": {"type": "string", "description": "用户输入信息"}}}, "result": {"type": "object", "description": "", "properties": {"valid": {"type": "boolean", "description": "检测结果，valid字段为True时表示数据有效，否则表示数据无效"}, "indexNames": {"type": "array", "description": "指标名称", "items": {"type": "string", "description": "单个指标名称"}}, "userIndexNames": {"type": "array", "description": "用户问题中的指标名称", "items": {"type": "string", "description": "用户问题中的单个指标名称"}}, "userinput": {"type": "string", "description": "用户输入信息"}}}, "alias": ["指标信息查询"], "catalog": "statistics"}, {"name": "data_report", "related_app": [{"name": "idx_query", "alias": "指标查询"}], "dependencies": {"file": [{"category": {"name": "f_idx_report", "alias": "报表文件"}, "required": true}]}, "description": "报表数据解析", "params": {"type": "object", "description": "报表EXCEL文件数据解析", "properties": {"userIndexNames": {"type": "array", "description": "用户问题中的指标名称", "items": {"type": "string", "description": "用户问题中的单个指标名称"}}, "userinput": {"type": "string", "description": "用户输入信息"}}}, "result": {"type": "object", "description": "报表数据解析", "properties": {"taskId": {"type": "string", "description": "任务id"}, "fileId": {"type": "string", "description": "文件id"}, "userIndexNames": {"type": "array", "description": "用户问题中的指标名称", "items": {"type": "string", "description": "用户问题中的单个指标名称"}}, "userinput": {"type": "string", "description": "用户输入信息"}}}, "alias": ["报表数据解析"], "catalog": "statistics"}, {"name": "index_data_confirm", "related_app": [{"name": "idx_query", "alias": "指标查询"}], "description": "打开报表数据确认页面", "params": {"userinput": {"type": "string", "description": "用户输入信息"}, "taskId": {"type": "string", "description": "文件解析任务id"}, "fileId": {"type": "string", "description": "文件id"}, "userIndexNames": {"type": "array", "description": "用户问题中的指标名称", "items": {"type": "string", "description": "用户问题中的单个指标名称"}}}, "result": {"type": "object", "description": "报表数据确认", "properties": {"valid": {"type": "boolean", "description": "检测结果，valid字段为True时表示数据有效，否则表示数据无效"}, "userinput": {"type": "string", "description": "用户输入信息"}, "indexNames": {"type": "array", "description": "指标名称", "items": {"type": "string", "description": "单个指标名称"}}}}, "alias": ["报表数据确认"], "catalog": "statistics"}, {"name": "get_multiple_index_values", "related_app": [{"name": "idx_query", "alias": "指标查询"}], "ignore_converter_cache": true, "description": "获取指标值", "params": {"type": "object", "description": "", "properties": {"indexQueries": {"type": "array", "description": "指标值查询参数", "items": {"type": "object", "description": "指标值查询条件项", "properties": {"dateWindow": {"type": "string", "description": "时间窗口，用于指标值查询的时间条件。用户问题中未指定时间，使用默认值\"\"。 \n ### 时间窗口格式说明\n\n#### 1. 单点时间格式\n- **年**：`yyyy`（示例：`2025`）\n- **月**：`yyyyMM`（示例：`202505`）\n- **日**：`yyyyMMdd`（示例：`20250529`）\n- **小时**：`yyyyMMddhh`（示例：`2025052914`，24小时制）\n- **分钟**：`yyyyMMddhhmm`（示例：`202505291430`）\n\n#### 2. 时间范围格式（起始与结束时间粒度一致）\n- **年范围**：`yyyy-yyyy`（示例：`2024-2025`）\n- **月范围**：`yyyyMM-yyyyMM`（示例：`202501-202512`）\n- **日范围**：`yyyyMMdd-yyyyMMdd`（示例：`20250501-20250531`）\n- **小时范围**：`yyyyMMddhh-yyyyMMddhh`（示例：`2025052908-2025052918`）\n- **分钟范围**：`yyyyMMddhhmm-yyyyMMddhhmm`（示例：`202505290830-202505290930`）\n\n### 特殊时间表达计算说明（基于当前系统时间动态计算）\n\n- **今天/本周第一天等相对当日的描述**：转换为单日格式，格式`yyyyMMdd`\n- **本月/上月等相对当月的描述**：转换为月第一天，格式`yyyyMM01`，示例：`20250501`\n- **今年/去年等相对当年的描述**：转换为年第一天，格式`yyyy0101`，示例：`20250101`\n- **最近1天/过去两天/最近两周等的描述**：转换为日范围格式\n- **最近1月/过去两月等的描述**：转换为月范围格式\n- **最近1小时/过去两小时等的描述**：转换为小时范围格式\n- **最近5分钟/过去5分钟等的描述**：转换为分钟范围格式\n- **工作日/周末**：转换为日范围格式。\n\n### 关键约束说明\n\n- 班次查询场景,日期部分必须使用单点格式（如`20250529`），班次信息通过`teamTime`参数单独传递  \n- 约定工作日为周一到周五，周末为周六周日\n- 时间范围起止时间粒度须保持一致，不能混用不同粒度\n- 所有特殊时间表达均基于系统当前时间动态计算\n- 仅输出时间或时间范围，不输出额外信息或与日期无关内容。", "oneOf": [{"title": "单点时间格式", "description": "精确到年/月/日/小时/分钟的具体时间点，格式为纯数字字符串", "pattern": "^(\\d{4}|\\d{6}|\\d{8}|\\d{10}|\\d{12})$"}, {"title": "时间范围格式", "description": "相同时间粒度的区间表达，格式为`起始-结束`，两端需保持粒度一致", "pattern": "^(\\d{4}-\\d{4}|\\d{6}-\\d{6}|\\d{8}-\\d{8}|\\d{10}-\\d{10}|\\d{12}-\\d{12})$"}, {"title": "特殊时间表达", "description": "基于当前时间的动态时间范围或时间点，支持13种预设关键词，自动转换为标准时间格式", "enum": ["", "今天", "今日", "昨天", "昨日", "前一日", "前日", "本周第一天", "本周最后一天", "本月", "上月", "今年", "去年", "工作日", "周末", "最近60分钟", "最近120分钟"]}]}, "indexNames": {"type": "array", "description": "指标名称，查询单个指标：传入指标名，例如 [\"6#汽机发电量\"]。查询多个指标：使用英文逗号,分隔多个指标名，例如 [\"6#汽机发电量\",\"6#汽机用电量\"]", "items": {"type": "string", "description": "单个指标名称"}}, "groupName": {"type": "string", "description": "分组名称，用于生成图表图例的唯一标识，需基于用户问题中的分组依据生成，总长度不超过10个汉字。具体规则如下：\n\n### 分组生成规则\n1. **按指标分组**\n   - 适用场景：用户问题中明确包含不同指标的对比（如`'6#汽机和7#汽机发电量对比'`）\n   - 生成逻辑：提取指标名称的关键特征部分（如`'6#汽机'`、`'7#汽机'`）\n\n2. **按时间分组**\n   - 适用场景：问题中涉及不同时间范围的对比（如`'本月与上月产量对比'`）\n   - 生成逻辑：直接使用时间范围描述（如`'本月'`、`'上月'`）\n\n3. **按班次分组**\n   - 适用场景：问题中明确提到班次信息（如`'早班和中班效率对比'`）\n   - 生成逻辑：采用班次类型名称（如`'早班'`、`'中班'`）\n\n4. **按值类型分组**\n   - 适用场景：问题中对比不同性质的值（如`'实测值与目标值对比'`）\n   - 生成逻辑：使用值类型关键词（如`'实测值'`、`'目标值'`）\n\n### 示例说明\n- 问题场景：`'6#汽机和7#汽机本周发电量对比'`\n- 分组结果：`indexQueries`中两个对象的`groupName`分别为`'6#汽机'`和`'7#汽机'`\n\n### 关键约束\n- 当`outputFormat`参数为`CHART`时，必须传入此参数\n- 分组名称需确保在同一次查询中具有唯一性，避免图例重复", "maxLength": 20}, "indexValueType": {"type": "string", "description": "指标值类型，用于匹配工业统计规则，需根据问题关键词选择，默认值为实测值。具体说明如下：\n\n### 可选值及适用场景\n- **MEA（实测值）**：默认选项，当问题未提及目标/当量/平衡等关键词时使用。\n  示例：\n  - '查询2025年5月发电量'\n  - '获取当前压力数据'\n\n- **TAR（目标值）**：当问题包含'目标'、'计划'等关键词时使用。\n  示例：\n  - '对比本月目标产量'\n  - '7#机组负荷计划值'\n\n- **EQU（当量值）**：当问题包含'当量'、'换算'等关键词时使用。\n  示例：\n  - '折算标准煤当量'\n  - '蒸汽能量换算值'\n\n- **BAL（平衡值）**：当问题包含'平衡'、'物料平衡'、'能量平衡'等关键词时使用。\n  示例：\n  - '蒸汽管网平衡数据'\n  - '物料出入库平衡统计'\n\n### 选择规则\n1. **优先匹配关键词**：若问题中出现'目标'、'计划'等关键词，选择TAR；出现'当量'、'换算'等关键词，选择EQU；出现'平衡'关键词，选择BAL。\n2. **无关键词用默认**：若问题未提及上述关键词，一律使用MEA。\n3. **禁止推测**：不能凭业务知识猜测值类型，例如'能耗数据'未包含关键词时仍应使用MEA。\n\n### 简单示例\n- 问题：'查询目标蒸发量' → 应选值：TAR → 原因：问题中包含'目标'关键词。\n- 问题：'计算天然气热值当量' → 应选值：EQU → 原因：问题中包含'当量'和'热值'关键词。\n- 问题：'物料平衡分析' → 应选值：BAL → 原因：问题中包含'平衡'关键词。\n- 问题：'获取6月实时产量' → 应选值：MEA → 原因：问题中未包含特征关键词。", "enum": ["MEA", "TAR", "EQU", "BAL"], "default": "MEA"}, "teamTime": {"type": "string", "description": "班次名称,问题中没有包含班次相关信息时，不需要传入此参数,该值不能为null，也不能为NULL,也不能为Null,可以为\"\" ", "enum": ["早班", "中班", "晚班", "白班", "夜班", "长白班", "长夜班", ""]}, "queryType": {"type": "string", "description": "查询类型标识，用于区分数据查询的结果形态，可选值为 `SINGLE`（单一统计值）和 `DETAIL`（明细/多数据点）。具体规则如下：\n\n### 一、取值规则\n- **SINGLE**：当用户问题包含以下关键词时使用：\n  - 统计类：总和、总计、合计、平均、最大、最小、累计、总量\n  - 结果类：数值、值、汇总、单一结果、是多少、分别是多少\n  - 示例：'6月发电量总和是多少'、'各车间产量分别是多少'\n\n- **DETAIL**：当用户问题包含以下关键词时使用：\n  - 明细类：明细、记录、日志、条目、详情\n  - 分析类：趋势、走势、变化、波动、分析、对比\n  - 比较类：TOP N、排名、排序、各车间对比\n  - 示例：'设备运行明细数据'、'近一周压力趋势变化'\n\n### 二、特殊场景处理\n1. **默认规则**：无明确关键词时，默认取值 `SINGLE`\n2. **时间范围处理**：\n   - 单一值需求（如'6月日均产量'）→ SINGLE\n   - 序列值需求（如'6月每日产量'）→ DETAIL\n3. **冲突处理**：\n   - 同时包含两类关键词（如'每日明细及月平均值'）：\n     - 以明细数据为基础需求 → 取值 DETAIL\n     - 以汇总值为核心需求 → 取值 SINGLE\n     - 无法判断时 → 取值 SINGLE", "enum": ["SINGLE", "DETAIL"], "default": "SINGLE"}, "period": {"type": "string", "description": "时间周期参数，用于指定数据查询的时间间隔周期，仅在同时满足查询指标趋势和要求以折线图显示时生效，非此类场景时该参数可为空。若涉及多个指标，需分别传入周期参数。具体规则如下：\n\n### 一、参数基本说明\n- **作用**：定义数据查询的时间间隔（如'1h'表示每小时获取一次数据）\n- **格式**：数字+单位组合（如`1m`、`3h`），非指定场景时可不传\n- **生效条件**：用户问题同时包含以下两类关键词时需指定该参数：\n  - 趋势类：趋势、走势、变化、波动、动态\n  - 图表类：折线图、趋势图、变化曲线\n  - 示例：'以折线图显示近30分钟压力趋势'、'用折线图呈现能耗变化走势'\n\n### 二、支持的时间单位\n支持以下单位（数字+单位）：\n- `y`（年，如`1y`）、`M`（月，如`3M`）、`w`（周，如`2w`）\n- `d`（天，如`7d`）、`h`（小时，如`3h`）、`m`（分钟，如`15m`）\n\n### 三、默认规则\n1. **生效场景未指定周期时**：\n   - 按问题隐含时间维度自动生成小一个时间维度的周期：\n     - 隐含'年'→默认`1M`（月），隐含'月'→默认`1w`（周）\n     - 隐含'周'→默认`1d`（天），隐含'天'→默认`1h`（小时）\n     - 隐含'小时'→默认`1m`（分钟），隐含'分钟'→默认`1m`（分钟）\n   - 示例1：'以折线图显示近2年销售额趋势'→隐含'年'→默认`1M`\n   - 示例2：'用折线图展示上月能耗变化'→隐含'月'→默认`1w`\n\n2. **生效场景未指定单位时**：\n   - 统一按分钟（m）处理，如'以折线图显示最近30趋势'→解析为`30m`\n\n3. **非生效场景时**：\n   - 参数可为空，无需传入，如'查询当前压力值'或'显示压力折线图'→忽略period\n\n### 四、多指标处理规则\n查询多个指标趋势并要求折线图时，按`指标:周期`格式分别指定：\n- 示例1：`temperature:1h,pressure:30m`（温度按小时、压力按30分钟）\n- 示例2：`power:1d,water:1w`（电量按天、水量按周）\n\n### 五、关键约束\n1. **生效条件**：必须同时存在趋势类和图表类关键词，缺一不可\n2. **格式校验**：生效场景下必须为`数字+单位`（y/M/w/d/h/m），如`5d`有效\n3. **冲突处理**：\n   - 明确指定周期（如'每2小时'）优先于隐含周期（如'近一周'）", "pattern": "^(?:\\d+(?:y|M|w|d|h|m)?)?$", "default": ""}}, "required": ["indexNames", "indexValueType", "queryType"]}}, "outputFormat": {"type": "string", "description": "这个参数用于指定返回数据的呈现形式。可以选项有CHART、RAW。 如下情况下任一情况选CHART,1）用户问题中包含`趋势`、`走势`、`变化`、`波动`、`曲线`、`图表`、`图形`、`分析`等可视化或分析相关关键词 \n 2） 直接要求以`柱状图`、`折线图`、`饼图`等具体图表形式展示数据。\n 3) 涉及`对比分析`、`趋势分析`、`时间序列分析`、`可视化`、`规律`、`波动情况`等需要图形化展示的分析场景（如“近一周产量趋势”“不同车间对比”）。\n **默认规则**：未明确符合CHART条件时，默认使用RAW。", "enum": ["RAW", "CHART"], "default": "RAW"}, "userinput": {"type": "string", "description": "用户输入信息"}, "question": {"type": "string", "description": "用户问题的语义摘要。如果用户想让我们返回图表信息，则需要设置这个参数的值。需以自然语言简洁描述查询意图，例如：用户问题是 \"2024年4月10日-20日，二期干燥工序_聚氯乙烯单产电力消耗实测值和3月同期对比，柱状图显示\"，提炼成  \"二期干燥工序聚氯乙烯电耗同比柱状图（4月10-20 vs 3月）\"。"}, "questionType": {"type": "string", "description": "问题类型,如果是图表输出,即outputFormat 为CHART时，此参数必须传入。有如下类型：如果是预计算问数，值为preCalc;如果是灵活计算，值为flexCalc;如果是问趋势，值为trend;如果是问累计，值为cumulate;如果是问对比，值为compare;如果是问明细，值为detail;如果是问TOP，值为top;"}, "chartType": {"type": "string", "description": "这个参数表示用户想要的图表类型，如果用户想要看的是图表，这个参数则是必填，有如下图表类型，英文是具体的值，括号里的中文只是值描述,只需要传具体值：bar(柱状图)、line(折线图)、pie(饼图)、gauge(仪表盘)、radar(雷达图)、scatter(散点图)", "enum": ["bar", "line", "pie", "gauge", "radar", "scatter"]}, "chartName": {"type": "string", "description": "这个参数标识图表名称标识符，用于指定可视化图表的具体名称。需要根据用户想要的图标类型选择具体的值，这个参数不是必填项。有如下选项: 1. **柱状图**：适用于分类数据对比，对应值为 `multi-bar`（如多指标对比）。 2. **折线图**：适用于展示数据趋势，对应值为 `stacked-line`（如指标数据变化趋势）。3. **饼图**：适用于展示占比关系，对应值为 `pie`（如日期分布）。 #### **自动匹配机制** 当用户未显式指定 `chartName` 时，系统将根据以下规则自动推断：a) 包含`趋势`、`走势`等关键词 → 优先匹配 `stacked-line` b) 包含`对比`、`排名`等关键词 → 优先匹配 `multi-bar`c) 包含`占比`、`分布`等关键词 → 优先匹配 `pie`", "enum": ["multi-bar", "stacked-line", "pie"]}, "groupDimension": {"type": "string", "description": "图表分组维度，当outputFormat=CHART时：若用户问题中分组依据为时间（包括不同时间范围对比、时间序列趋势分析等情况），则设置为TIME_AXIS；仅当用户问题明确指定按非时间维度（如按指标、按类型、按班组等）进行分组时，设置为CATEGORY_AXIS；默认值为TIME_AXIS", "enum": ["TIME_AXIS", "CATEGORY_AXIS"], "default": "TIME_AXIS"}}, "required": ["indexQueries", "outputFormat"]}, "result": {"type": "object", "description": "指标值相关信息，如果指标取不到值，有以下几种情况：1、调用指标查询接口时，请求参数中指定的指标名称（如 indexCodes）未在指标服务的元数据中注册 / 定义，导致服务无法识别该指标  2、调用指标查询接口时，请求中指定的查询时间范围（如 dateWindow）早于系统当前时间，导致无法获取数据  3、请求的指标在指标服务中已找到，但在指定时间窗口范围内（如 dateWindow）无有效数据记录  4、指标定义有效、时间参数合法，但在查询执行过程中因技术故障导致请求失败", "properties": {"result_data": {"type": "array", "description": "指标值信息", "items": {"type": "object", "properties": {"dateWindow": {"type": "string", "description": "时间窗口"}, "dateWindowStr": {"type": "string", "description": "时间窗口字符串"}, "name": {"type": "string", "description": "指标名称"}, "dateType": {"type": "string", "description": "时间类型"}, "teamTime": {"type": "string", "description": "班次名称"}, "value": {"type": "number", "description": "指标值"}, "quality": {"type": "number", "description": "质量码，如果质量码是192代表数据可信， 非192 代表数据异常。"}, "unit": {"type": "string", "description": "单位"}, "desc": {"type": "string", "description": "指标数据描述"}}}}}}, "alias": ["指标数据查询"], "catalog": "statistics"}]}