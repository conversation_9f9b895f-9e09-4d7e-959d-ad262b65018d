from __runner__ import tool, Context
import requests
import json
from datetime import datetime

from enum import IntEnum
from typing import Dict, Any, Optional
from dataclasses import dataclass
import aiohttp
import asyncio

@tool(private=True)
def get_ots_http_server_config(context: Context, params: any):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成工艺模型失败1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成工艺模型失败2")
        except ValueError:
            raise ValueError("生成工艺模型异常")
            
        return server_ip, server_port

    except Exception as e:
        return None

@tool(private=True)
def get_ots_web_server_config(context:Context, params: any):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['webserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("装置资源组装失败，未找到装置资源1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("装置资源组装失败，未找到装置资源2")
        except ValueError:
            raise ValueError("装置资源组装异常，未找到装置资源")
            
        return server_ip, server_port

    except Exception as e:
        return None

@tool(private=True)
def get_ots_web_client_config(context:Context, params: any):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['webclient_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("装置资源组装失败，未找到装置位置1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("装置资源组装失败，未找到装置位置2")
        except ValueError:
            raise ValueError("装置资源组装异常，未找到装置位置")
            
        return server_ip, server_port

    except Exception as e:
        return None

class SVRHostConfigType(IntEnum):
    """客户端获取服务端host配置类型枚举"""
    CORESVR     = 0
    AGENTSVR    = 1
    GATEWAYSVR  = 2

class SVRPortConfigType(IntEnum):
    """客户端获取服务端配置类型枚举"""
    HTTPSVR     = 0
    WEBSVR      = 1
    WEBCLI      = 2
    TRAINSVR    = 3
    GATEWAY     = 4

# http消息组装
'''
针对原先http消息交互，对经过gateway中间处理要携带特殊信息时，
外部根据类型判断是否携带特殊信息
'''
# @dataclass
# class HttpMessage:
#     """HTTP消息容器"""
#     url: str
#     headers: Dict[str, str]
#     payload: Dict[str, Any]

def get_svraddress(context:Context, 
                  host_type: SVRHostConfigType, 
                  port_type: SVRPortConfigType) -> str:
    """安全获取host和port组合"""
    host_map = {
        SVRHostConfigType.CORESVR: context.config.get('coreserver_host', ''), 
        SVRHostConfigType.AGENTSVR: context.config.get('agentserver_host', ''),
        SVRHostConfigType.GATEWAYSVR: context.config.get('gatewayserver_host', '')
    }
    port_map = {
        SVRPortConfigType.HTTPSVR: context.config.get('httpserver_port', ''),
        SVRPortConfigType.WEBSVR: context.config.get('webserver_port', ''),
        SVRPortConfigType.WEBCLI: context.config.get('webclient_port', ''),
        SVRPortConfigType.TRAINSVR: context.config.get('trainserver_port', ''),
        SVRPortConfigType.GATEWAY: context.config.get('gateway_port', '')
    }
    
    host = host_map.get(host_type, '')
    port = port_map.get(port_type, '')
    return f"{host}:{port}" if host and port else ""

def build_base_url(context: Context,
                   params: any, 
                   force_gw: bool = False) -> Optional[str]:
    """统一构建URL基础路径"""
    try:
        host_type = SVRHostConfigType.GATEWAYSVR if force_gw else params.get("host_type")
        port_type = params.get("port_type")    
        endpoint = get_svraddress(context, SVRHostConfigType(host_type), SVRPortConfigType(port_type))
        return f"http://{endpoint}" if endpoint else None
    except ValueError:
        return None

#组装http消息
@tool(private=True)
def package_http_message(context:Context, params: any)->dict[str, Any]:
    http_url = params.get("http_url", "")
    if not http_url:
        return {}

    # 网关开关
    IsOpenGW = bool(context.config.get('gateway_onoff', False))
    base_url = build_base_url(context,params, IsOpenGW)
    if not base_url:
        return {}
    
    full_url = f"{base_url}/{http_url.lstrip('/')}"

    message = {
        "url":full_url,
        "headers":{
            "Content-Type": "application/json",
            # 使用三元判断和字典解包合并
            **({"X-Routing-Header": str(context.session_id)} 
               if IsOpenGW else {})
        },
        "payload":{}
    }

    return message

#初始化ots实例
@tool(private=True)
async def initotsInstance(context: Context, params: any):    
    base_url = build_base_url(context, params, True)

    if not base_url:
        return {
                "result":-3,
                "full_url":"",
                "headers":"",
                "payload":""
                }
    
    full_url = f"{base_url}/initOTSInstance"
    headers = {
        "Content-Type": "application/json",
         }
    payload = {"sessionId": str(context.session_id)}
    await context.add_view({
                "format": "debug",
                "content": {
                    "init_begin": datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 添加格式化
                }
            })
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url=full_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=5*60
            ) as response:
                response.raise_for_status()
                result = await response.json()
                await context.add_view({
                "format": "debug",
                "content": {
                    "init_end": datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 添加格式化
                }
            })
                return {
                    "result":result,
                    "full_url":full_url,
                    "headers":headers,
                    "payload":payload
                    }

        except aiohttp.ClientError as e:
                return {
                    "result":-1,
                    "full_url":full_url,
                    "headers":headers,
                    "payload":payload
                    }
        except asyncio.TimeoutError:
                return {
                    "result":-2,
                    "full_url":full_url,
                    "headers":headers,
                    "payload":payload
                    }


#反初始化ots实例
@tool(private=True)
async def uninitotsInstance(context: Context, params: any):
    base_url = build_base_url(context,params, True)

    if not base_url:
        return {
                "result":-3,
                "full_url":"",
                "headers":"",
                "payload":""
                }
    
    full_url = f"{base_url}/unInitOTSInstance"
    headers = {
        "Content-Type": "application/json",
         }
    payload = {"sessionId": str(context.session_id)}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url=full_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=5*60
            ) as response:
                response.raise_for_status()
                result = await response.json()
                return {
                "result":result,
                "full_url":"",
                "headers":"",
                "payload":""
                }

        except aiohttp.ClientError as e:
                return {
                    "result":-1,
                    "full_url":full_url,
                    "headers":headers,
                    "payload":payload
                    }
        except asyncio.TimeoutError:
                return {
                    "result":-2,
                    "full_url":full_url,
                    "headers":headers,
                    "payload":payload
                    }
    
    