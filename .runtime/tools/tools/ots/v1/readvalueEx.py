from __runner__ import tool, Context
import aiohttp
import json
import asyncio
import logging
import xml.etree.ElementTree as ET
import os
import ipaddress
import datetime


from typing import Dict, Any, List, Literal, Union, Optional, Tuple

from dataclasses import field, dataclass

from enum import IntEnum


def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("模拟生成设备数据失败，未找到工艺模型1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("模拟生成设备数据失败，未找到工艺模型2")
        except ValueError:
            raise ValueError("模拟生成设备数据异常，未找到工艺模型")
            
        return server_ip, server_port

    except Exception as e:
        return None

class DataType(IntEnum):
    """设备参数数据类型枚举"""
    BOOL = 0
    INT = 1
    DOUBLE = 2
    STRING = 3

@dataclass
class TagDefinition:
    """设备参数基础定义"""
    tagName: str
    unit: Optional[str] = None

@dataclass
class TagConfigDefinition:
    """设备参数配置定义"""
    name: str
    datatype: int
    desc: Optional[str] = None
    unit: Optional[str] = None

@dataclass
class TagWithValue:
    """带值的设备参数定义"""
    tagName: str
    dataType: int
    value: Union[int, bool, float, str]
    unit: Optional[str] = None  # 新增单位字段
    timestamp: Optional[str] = None

def convert_value(data_type: int, value: Any) -> Any:
    """将值转换为更具体的Python类型"""
    if isinstance(value, str):
        try:
            if data_type == 0:  # bool
                return value.lower() in ('true', '1', 't', 'y', 'yes')
            elif data_type == 1:  # int
                return int(value)
            elif data_type == 2:  # double
                return float(value)
        except ValueError:
            pass  # 转换失败则返回原值
    
    return value

def data_type_to_str(data_type: int) -> str:
    """将数据类型转换为可读字符串"""
    return {
        0: "布尔",
        1: "整数",
        2: "浮点数",
        3: "字符串"
    }.get(data_type, f"未知({data_type})")


'''
需求：
1、加载工况前读取一次值，加载工况后读取一次值，最后通过表格对比前后值；
2、加载工况后持续读取一分钟值，最后通过趋势图展示一分钟内的各个位号值；
设计：
1、如果loaded_snapshot为空，user_duration为0，说明是加载工况前读值，读一次值；
2、如果loaded_snapshot为空，user_duration不为0，将user_duration值传到服务端，读取指定时长的所有值；
3、如果loaded_snapshot不为空，说明是加载工况后的读值，如果此时user_duration为0，则默认指定时长为1分钟，通知服务端读取1分钟时长的值；
4、如果loaded_snapshot不为空，user_duration不为0，将user_duration值传到服务端，读取指定时长的所有值；
5、如果loaded_snapshot不为空，说明是加载工况后的读值，如果pre_tagsvalue不为空，则取加载工况后读值时长的最后一个值，构建表格对比加载工况前和加载工况后的相同位号的值；
6、如果loaded_snapshot不为空，说明是加载工况后的读值，获取一段时长的值后，展示所有位号参数值曲线视图，以位号值为y轴，时间戳为x轴，以颜色区分位号；
7、服务端返回的一段时长的数据带有位号名：name，数据类型：datatype，时间戳：timestamp，位号值：datavalue；
8、如果user_tagnames不为空，以该位号名为准，说明文本中包含位号名，需要弹出动态表单让用户确认位号名是否正确；
9、如果user_tagnames为空，running_model_tags中items不为空，则以running_model_tags中指定的位号名为准，不用弹出表单确认位号，直接向服务请求参数值；
10、如果user_tagnames为空，running_model_tags中items为空，则弹出表单让用户填写位号名；
11、异常情况处理；
'''

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card" and card_type != "chart":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

async def get_model_info(context: Context, serveraddr: str) -> Tuple[int, str]:
    """获取模型信息"""
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/getmodelinfo",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return -9, ""

    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })

    payload = {**(message["payload"]),"timeout": 5}
    headers = message["headers"]
    getmodelinfo_url = message["url"]

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=getmodelinfo_url,
                headers=headers,
                json=payload,
                timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()
                return result.get("state", -1), result.get("modelname", "")
    
    except aiohttp.ClientError as e:
        logging.error(f"工艺模型位置定位失败，请重新生成工艺模型")
        return -1, ""
    except asyncio.TimeoutError:
        logging.error("查找工艺模型位置定位超时，请重新生成工艺模型")
        return -2, ""

FORM_INTERACTION_ID = "readvalue_form"

# 抽象的公共函数，获取指定模型的设备参数配置表
async def get_model_config(
    context: Context, 
    params: Dict, 
    model_name: str
) -> Optional[Dict]:
    """公共函数：获取指定模型的设备参数配置"""
    running_model_tags = await context.get_cache("running_model_tags") or params.get("running_model_tags", [])
    # 添加辅助日志
    # await context.log_info(f"get running_model_tags from cache results={running_model_tags}， imput_model={model_name}")
    # await context.add_view({
    #     "format": "debug",
    #     "content": {
    #         "model_name": model_name,
    #         "running_model_tags": running_model_tags
    #     }
    # })

    for model in running_model_tags:
        if model.get("running_model") == model_name:
            return model
    return None

# 抽象的公共函数，获取指定模型的设备参数配置表，根据设备参数名绑定
async def build_config_map(
    context: Context,
    params: Dict,
    model_name: str
) -> Dict[str, Dict]:
    """公共函数：构建标签配置映射表"""
    model = await get_model_config(context, params, model_name)
    if not model or not model.get("tagsconfig"):
        return {}
    return {tag["name"]: tag for tag in model["tagsconfig"] if "name" in tag}

async def determine_read_duration(context: Context, params: Dict) -> int:
    """确定持续模型计算时长逻辑"""
    # 直接从缓存中获取，不从参数获取，参数有可能大模型理解错误随意组
    loaded_snapshot = await context.get_cache("loaded_snapshot")
    # 用于第二次模拟设备参数值要判断是否已发生过模拟读值的标志
    # second_readvalue =  await context.get_cache("first_readvalue") or False
    user_duration = params.get("user_duration", 0)
    
    # 调试日志（正式环境可移除）
    await context.add_view({
        "format": "debug",
        "content": {
            "user_duration": user_duration,
            "loaded_snapshot": loaded_snapshot
        }
    })

    if loaded_snapshot and user_duration == 0:
        return 30  # 默认持续模拟30秒
    return user_duration

async def get_tag_definitions(context: Context, params: Dict, model_state:int, model_name:str) -> List[TagDefinition]:
    """获取设备参数定义列表"""
    config_map = await build_config_map(context, params, model_name)
    # 优先使用用户输入的设备参数，这里的设备参数要求经过向量库后是准确的，如果不确定是否准确，需要弹出动态表单
    if params.get("user_tagnames"):
        return [
            TagDefinition(tag["tagName"], config_map.get(tag["tagName"], {}).get("unit"))
            for tag in params["user_tagnames"]
        ]
    
    # 检查模型信息并获取匹配的tags组态
    model = await get_model_config(context, params, model_name)
    
    # 调试日志（正式环境可移除）
    # await context.add_view({
    #     "format": "debug",
    #     "content": {
    #         "model_state": model_state,
    #         "model_name": model_name,
    #         "has_running_tags": bool(model["tagsconfig"]),
    #         "first_model_name": model["running_model"] if model else None
    #     }
    # })

    result_tags = []
    if model and model.get("tagsconfig") and model_state == 1:
        display_config_tags = model["tagsconfig"]
        result_tags = [
            TagDefinition(
                tag["name"],
                tag.get("unit")
            ) for tag in display_config_tags if isinstance(tag, dict) and tag.get("name")
        ]

    if not await context.get_interaction(FORM_INTERACTION_ID):
        promptmsg = "成功生成模型设备参数" if result_tags else f"模型设备参数生成失败,{model}:{model_state}"
        await add_card(context, "tip", f"{promptmsg}", "")  # 仅首次展示提示

    if result_tags:
        # 有默认位号组态
        return result_tags
    # 这段逻辑先注掉，没有默认组态就直接失败了，不用让用户操作，因为用户不关心也不知道怎么配置组态    
    # # 都没有获取到组态，最后弹出表单让用户输入
    # form_result = await prompt_tag_selection(context,params)
    # if form_result:
    #     return [TagDefinition(tag["tagName"], config_map.get(tag["tagName"], {}).get("unit")) 
    #                     for tag in form_result["tagnames"]]
    return []

async def get_unit_from_config(context: Context, params: Dict, tag_name: str, model_name: str) -> Optional[str]:
    """从配置信息中获取单位信息"""
    config_map = await build_config_map(context, params, model_name)
    if not config_map:
        return None
    return config_map.get(tag_name, {}).get("unit")

async def get_config_by_name(context: Context, params: Dict, tags: List[TagDefinition], runmodel: str) -> List[TagConfigDefinition]:
    """根据设备参数名获取设备参数配置信息"""
    config_map = await build_config_map(context, params, runmodel)
    # (cfg := config_map.get(tag.tagName)) 是 Python 3.8+ 的海象运算符, 
    # 同时完成两个操作：赋值给 cfg + 返回赋值结果
    # cfg 的作用域是整个列表推导式
    return [
        TagConfigDefinition(
            name=tag.tagName,
            datatype=cfg["datatype"] if (cfg := config_map.get(tag.tagName)) and "datatype" in cfg else -1,
            desc=cfg.get("desc", "N/A") if cfg else "N/A",
            unit=cfg.get("unit", tag.unit) if cfg else tag.unit
        )
        for tag in tags
    ]

async def prompt_tag_selection(context: Context, params: Dict) -> List[TagDefinition]:
    """显示设备参数选择表单"""
    form_result = await context.get_interaction(FORM_INTERACTION_ID)
    if form_result:
        return form_result
    
    await context.require_interaction({
        "id": FORM_INTERACTION_ID,
        "title": "选择设备参数",
        "type": "form",
        "form": {
            "schema": {
                "type": "object",
                "properties": {
                    "tagnames": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "tagName": {"type": "string"}
                            }
                        }
                    }
                }
            }
        }
    })
    return {} # 等待用户确认位号

async def display_config(context:Context, runmodel:str, config_tags:List[TagConfigDefinition]):
    """显示设备参数配置视图"""
    # 表格视图
    table_content = f"### 关键设备参数\n| 参数描述 | 参数名称 | 数据类型 | 单位 |\n|---|---|---|---|\n"
    for tag in config_tags:
        tag_name = tag.name if hasattr(tag, 'name') else "未知"
        unit = tag.unit if hasattr(tag, 'unit') else "N/A"
        data_type = tag.datatype if hasattr(tag, 'datatype') else -1
        data_type_name = DataType(data_type).name if data_type in DataType._value2member_map_ else "未知"
        tag_desc = tag.desc if hasattr(tag, 'desc') else "N/A"
        table_content += f"| {tag_desc} | {tag_name} | {data_type_name} | {unit} |\n"
    
    await add_card(context, "card", table_content, "设备参数信息表")

async def display_results(context: Context, 
                        tags: List[TagWithValue], 
                        lastest_tags: List[Dict],
                        pre_tags: List[Dict] = None,
                        duration: int = 0):
    await add_card(
        context, 
        "summary", 
        "成功模拟设备仿真计算，模拟数据详情 请点击 设备模拟仿真数据表 进行查看。", 
        "成功模拟设备仿真计算"
        )

    # 创建一个 tagName 到单位的映射
    tag_unit_map = {tag.tagName: tag.unit for tag in tags}

    # 格式化浮点数，保留6位小数
    def format_value(value):
        if isinstance(value, (float, int)):
            return f"{value:.6f}"
        return value
    
    """显示结果视图"""
    # 表格视图
    table_content = "### 设备最新模拟仿真数据\n| 参数名称 | 数据类型 | 值 | 单位 |\n|---|---|---|---|\n"
    for tag in lastest_tags:
        tag_name = tag.get("tagName", "未知参数")
        unit = tag_unit_map.get(tag_name, "N/A")
        data_type = tag.get("dataType", -1)
        data_type_name = DataType(data_type).name if data_type in DataType._value2member_map_ else "未知"
        value = format_value(tag.get("value", "N/A"))
        table_content += f"| {tag_name} | {data_type_name} | {value} | {unit} |\n"
    
    await add_card(context, "card", table_content, "设备模拟仿真数据表")
    # await context.add_view({
    #     "format": "markdown",
    #     "content": table_content
    # })
    
    # 调试日志（正式环境可移除）
    await context.add_view({
        "format": "debug",
        "content": {
            "duration": duration,
            "has_pre_tags": bool(pre_tags)
        }
    })

    # 如果有前后对比数据
    if pre_tags:
        await add_card(
        context, 
        "summary", 
        "设备模拟仿真已成功计算多次，仿真数据前后变化详情 请点击 加载工况前后设备仿真数据对比 进行查看。", 
        "设备模拟仿真已成功计算多次，查看加载工况前后设备仿真数据变化"
        )
        comparison = "### 仿真设备数据对比\n| 参数名称 | 前值 | 后值 | 是否变化 | 单位 |\n|---|---|---|---|---|\n"
        pre_values = {tag["tagName"]: tag for tag in pre_tags}
        for tag in lastest_tags:
            tag_name = tag.get("tagName", "未知设备参数")
            pre_tag = pre_values.get(tag_name, {})
            pre_val = format_value(pre_tag.get("value", "N/A"))
            unit = tag_unit_map.get(tag_name, "N/A")
            value = format_value(tag.get("value", "N/A"))
            comparison += f"| {tag_name} | {pre_val} | {value} | {'是' if str(pre_val) != str(value) else '否'} | {unit} |\n"
        
        await add_card(context, "card", comparison, "加载工况前后设备仿真数据对比")
        # await context.add_view({
        #     "format": "markdown",
        #     "content": comparison
        # })
    
    # 如果是持续读取，显示趋势图
    if duration > 0:
       await add_card(
        context, 
        "summary", 
        f"按指定时长：{duration}秒 持续模拟设备仿真计算，模拟数据详情 请点击 仿真设备模拟数据趋势图 进行查看。", 
        "仿真设备指定时长持续模拟计算"
        )
       await display_trend_chart(context, tags, duration)
       await add_card(
        context, 
        "summary", 
        f"展示此次持续模拟仿真设备计算的所有数据， 请点击 仿真设备模拟数据表 进行查看。", 
        "已生成仿真设备模拟数据表"
        )
       await display_all_tagsvalue(context, tags)

async def display_trend_chart(context: Context, tags: List[TagWithValue], duration:int):
    """显示趋势图视图"""
    # 按设备参数名分组数据
    tag_groups: Dict[str, Dict] = {}
    for tag in tags:
        try:
            if not tag.timestamp:
                continue
                
            if tag.tagName not in tag_groups:
                tag_groups[tag.tagName] = {
                    "label": f"{tag.tagName} ({tag.unit})" if tag.unit else tag.tagName,
                    "data": [],
                    "unit": tag.unit or "",
                    "dataType": tag.dataType
                }
            
            tag_groups[tag.tagName]["data"].append({
                "x": tag.timestamp*1000, # 转换为毫秒（Chart.js要求）
                "y": tag.value
            })
        except (TypeError, ValueError) as e:
            # print(f"Invalid tag data: {e}")
            continue
        
    # 2. 构建ECharts配置（带数据校验）
    series = []
    for i, (name, info) in enumerate(tag_groups.items()):
        try:
            # 清理和排序数据
            clean_data = []
            for point in info["data"]:
                if isinstance(point, dict):
                    clean_data.append([point["x"], point["y"]])
                elif isinstance(point, (list, tuple)) and len(point) >= 2:
                    clean_data.append([point[0], point[1]])
            
            if not clean_data:
                continue
                
            sorted_data = sorted(clean_data, key=lambda x: x[0])
            
            # 生成颜色（限制在12色内）
            color_index = i % 12
            hue = color_index * 30
            
            series.append({
                "name": f"{name} ({info['unit']})",
                "type": "line",
                "showSymbol": True,
                "data": sorted_data,
                "itemStyle": {"color": f"hsl({hue}, 75%, 50%)"},
                "lineStyle": {"width": 2},
                "symbolSize": 6
            })
        except Exception as e:
            # print(f"Error processing series {name}: {e}")
            pass

        if not series:
            await context.add_view({
                "format": "text",
                "content": "错误：没有有效的数据可生成图表"
            })
            return
        
    # 构建图表配置
    # 计算需要的左边距（基于最长的y轴标签）
    max_label_length = max(
        len(str(max(p[1] for p in s["data"]))) 
        for s in series
    ) if series else 0

    # 2. 构建 ECharts 配置
    echart_options = {
        "title": {
            "text": f"持续{duration}秒的仿真设备模拟数据趋势",
            "left": "center",
            "top": 10,
            "padding":[0,0,30,0]
        },
        "tooltip": {
            "trigger": "axis",
            "formatter": """function(params) {
                return params.map(p => [
                    p.marker + p.seriesName,
                    '值: ' + p.value[1] + (p.seriesUnit || ''),
                    '时间: ' + new Date(p.value[0]).toLocaleString()
                ].join('<br/>')).join('<br/>');
            }"""
        },
        "legend": {
            "data": [s["name"] for s in series], 
            "top": 40
        },
        "grid": {
            "left": "10%",      #左侧边距（为Y轴标签留空间）
            "right": "5%",      #右侧边距
            "bottom": "20%",    #底部边距（为X轴标签留空间）
            "top": "25%",       #顶部边距（防止标题与Y轴重叠） 
            "containLabel": True
        },
        "xAxis": {
            "type": "time",
            "axisLabel": {
                "formatter": """function(value) {
                    return new Date(value).toLocaleTimeString();
                }""",
                "rotate": 45,  # 标签旋转防止重叠
                "margin": 10,  # 减少标签间距
                "interval": "auto" # 自动隐藏重叠标签 
            },
            "name": "时间轴",
            "nameLocation": "middle",
            "nameGap": 50
        },
        "yAxis": {
            "type": "value",
            "name": "设备仿真数据",
            "nameLocation": "end",
            "nameGap": max_label_length/2,  # 动态调整间距
            "nameRotate": 0,    # 名称旋转角度
            "offset": 0,        # 轴向偏移
            "axisLabel": {
                "formatter": """function(value) {
                    return value;
                }""",
                "margin": max_label_length * 2
            },
            "axisLine": {
                "show": True,
                "lineStyle": {
                    "width": 2
                }
            },
            "splitLine": {
                "show": True,
                "lineStyle": {
                    "type": "dashed"
                }
            }
        },
        "series": series
    }
    dark_options = {
        "chartTheme": 'dark',
        "chartConfig": {
            **echart_options,
            "title": {
                **echart_options["title"],
                "textStyle": {
                    "color": '#FFF'  # 深色模式标题颜色
                }
            },
            "legend": {
                **echart_options["legend"],
                "textStyle": {
                    "color": '#FFF'  # 深色模式图例颜色
                }
            },
            "xAxis": {
                **echart_options["xAxis"],
                "axisLabel": {
                    **echart_options["xAxis"]["axisLabel"],
                    "color": '#AAA'  # 深色模式轴标签颜色
                },
                "nameTextStyle": {
                    "color": '#FFF'  # 深色模式轴名称颜色
                }
            },
            "yAxis": {
                **echart_options["yAxis"],
                "axisLabel": {
                    **echart_options["yAxis"]["axisLabel"],
                    "color": '#AAA'  # 深色模式轴标签颜色
                },
                "nameTextStyle": {
                    "color": '#FFF'  # 深色模式轴名称颜色
                }
            },
            "textStyle": {
                "color": '#FFF'  # 深色模式全局文本颜色
            }
        }
    }

    # 3. 构建返回结构（与outputFormat='CHART'格式一致）
    res = [
        {
            "format": "text",
            "content": f"> 基于{len(series)}个设备在{duration}秒内的模拟仿真数据生成"
        },
        {
            "format": "echarts",
            "content": [
                {
                    "chartTheme": 'light',
                    "chartConfig": echart_options,
                },
                dark_options
            ]  # 注意保持数组格式
        },
        {
            "format": "text",
            "content": f"> 数据来源工艺模型仿真设备模拟计算实时值，更新延迟≤1秒"
        }
    ]

    # 4. 发送到前端
    await context.add_view({
        "hide_all": False,
        "type": "view",
        "format": "card",
        "content": {
            "details": res,
            "hide_details": False,
            "title": f"仿真设备模拟数据趋势图 ({duration}秒)",
            "type": "markdown"
        }
    })

    # 5. 返回原始数据（可选）
    # return {
    #     "state": 1,
    #     "raw_data": [tag.dict() for tag in tags],
    #     "echart_options": echart_options
    # }
    
    # await add_card(context, "chart", chart_config, "位号值趋势图")

# 表格展示所有位号值，等支持趋势后该表格不展示
async def display_all_tagsvalue(context: Context, tags: List[TagWithValue]):
    # 格式化浮点数，保留6位小数
    def format_value(value):
        if isinstance(value, (float, int)):
            return f"{value:.6f}"
        return value
    
    # 格式化时间戳为可读时间
    def format_timestamp(timestamp):
        if isinstance(timestamp, (int, float)) and timestamp > 0:
            return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        return "N/A"

    comparison = "### 仿真设备所有模拟计算数据\n| 参数名称 | 数据类型 | 参数值 | 时间戳 |\n|----------|----------|----------|----------|\n"
    for tag in tags:
        tag_name = tag.tagName
        data_type = tag.dataType
        data_type_name = DataType(data_type).name if data_type in DataType._value2member_map_ else "未知"
        value = format_value(tag.value)
        # timestamp = tag.timestamp if tag.timestamp else "N/A"
        timestamp = format_timestamp(tag.timestamp)
        comparison += f"| {tag_name} | {data_type_name} | {value} | {timestamp} |\n"
    
    await add_card(context, "card", comparison, "仿真设备模拟数据表")

# 获取时段中每个位号list值中时间戳最大的位号值
async def get_latest_values(tags_data: List[Dict]) -> List[Dict]:
    """从时段数据中提取每个设备参数的最新值"""
    latest_values = []
    for tag_data in tags_data:
        if not isinstance(tag_data, dict) or not tag_data.get("values"):
            continue
        # if "values" in tag_data and len(tag_data["values"]) > 0:
        # 找到时间戳最大的值
        latest_value = max(tag_data["values"], key=lambda x: x["timestamp"])
        latest_values.append({
            "tagName": tag_data["tagName"],
            "dataType": tag_data["dataType"],
            "value": latest_value["value"],
            "timestamp": latest_value["timestamp"]
        })
    return latest_values

@tool(version="*")
async def simureadvalue(context: Context, params: Dict[str, Any]) -> Dict[str, Any]:
    # 1. 参数处理
    params = params or {}
    time_duration = await determine_read_duration(context, params)
    server_address = "localhost:9999"
    try:
        config = get_ots_http_server_config(context)

        # 检查 config 是否为 None
        if config is not None:
            # 确保 config 是一个包含两个元素的元组
            if isinstance(config, tuple) and len(config) == 2:
                host, port = config
                server_address = f"{host}:{port}"
            else:
                raise ValueError("模拟生成设备数据失败，未找到工艺模型位置")
    except ValueError as ve:
         raise ValueError("模拟生成设备数据失败，工艺模型位置坐标解析失败")
    except Exception as e:
         raise ValueError("模拟生成设备数据异常，寻找工艺模型位置发生错误")

    model_state, model_name = await get_model_info(context,server_address)

    if not model_name or model_state != 1:
        await add_card(
            context, 
            "summary", 
            f"模拟工艺模型仿真计算状态不正确{model_state}，请重试", 
            "工艺模型仿真计算状态不正确"
            )
        return {"state": -6, "running_model": model_name, "tagsvalue": [], "error": "工艺模型仿真计算状态不正确，请重试"}
    # 2、获取位号组态定义,只包含要使用的元素
    # model_state = await context.get_cache("running_state")
    # model_name  = await context.get_cache("running_model")
    tags = await get_tag_definitions(context, params, model_state, model_name)

    if not tags:
        await add_card(
            context, 
            "summary", 
            "未正确生成模型设备，无法模拟仿真", 
            "未正确生成模型设备"
            )
        raise ValueError(-5, "未正确生成模型设备，无法模拟仿真")
    
    # 有组态，展示一下，展示组态所有属性
    await add_card(
            context, 
            "tip", 
            "已成功生成模型设备参数信息，参数详情 请点击 设备参数信息表 进行查看。", 
            ""
            )
    display_tagsconfig = await get_config_by_name(context, params, tags, model_name)
    await display_config(context, model_name, display_tagsconfig)

    running_model = model_name

    pre_tags = params.get("pre_tagsvalue", [])
    if not pre_tags:
        last_tagsvalue = await context.get_cache("lastest_tagsvalue") or []
        pre_tags = [{
            "tagName": tag["tagName"],
            "dataType": tag["dataType"],
            "value": tag["value"]
        } for tag in last_tagsvalue] if last_tagsvalue else []
        
    # 设置一下缓存，输出报告要用
    if pre_tags:
        await context.set_cache("pre_tagsvalue", pre_tags)

    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/readvalue",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return{
                "state": -7,
                "running_model": running_model,
                "tagsvalue":[],
                "error":"模拟仿真设备计算失败"
            } 

    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })

    # 2. 准备请求数据
    payload = {**(message["payload"]),
               "tagnames": [{"tagName": tag.tagName} for tag in tags],
               "duration": time_duration
    }
    headers = message["headers"]
    url = message["url"]
    
    # 3. 发送HTTP请求
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url=url,
                json=payload,
                headers=headers,
                timeout=2*time_duration+15
            ) as response:
                response.raise_for_status()
                response_data = await response.json()
                result_state = response_data.get("state")
                if result_state != 0:
                    return {"state": -4, "running_model": running_model, "tagsvalue": [], "error": f"模拟仿真设备计算出错{result_state}"}
                
                # 返回的结果 为 {tags: {tagName:xxx, dataType:xxx, values:{{value:"1",timestamp:"xxx"},{value:"2",timestamp:"xxx"},{value:"2",timestamp:"xxx"}}}}

                # 处理返回的数据
                result_tags = response_data.get("tags", [])
                
                # 解析获取每个位号的最新值
                lastest_values = await get_latest_values(result_tags)
                # 设置到缓存中
                if lastest_values:
                    await context.set_cache("lastest_tagsvalue", lastest_values)
                    # 用于第二次读值要判断是否已发生过读值的标志
                    # await context.set_cache("first_readvalue", True)

                # 视图展示
                display_tags = []
                for tag in result_tags:
                    # 获取对应的单位信息
                    unit = next((t.unit for t in tags if t.tagName == tag["tagName"]), None)
                    if not tag.get("values"):
                        continue
                    for value_data in tag["values"]:
                        display_tags.append(TagWithValue(
                            tagName=tag["tagName"],
                            dataType=tag["dataType"],
                            value=convert_value(tag["dataType"], value_data["value"]),
                            unit=unit,
                            timestamp=value_data["timestamp"]
                        ))

                await display_results(
                    context, 
                    display_tags,
                    lastest_values,
                    pre_tags,
                    time_duration
                )
                # 返回结果
                return {
                    "state": result_state,
                    "running_model": running_model,
                    "tagsvalue": [{
                        "tagName": tag["tagName"],
                        "dataType": tag["dataType"],
                        "value": tag["value"]
                    } for tag in lastest_values]
                }       
    
        except asyncio.TimeoutError:
            return{
                "state": -2,
                "running_model": running_model,
                "tagsvalue":[],
                "error":"模拟仿真设备计算超时"
            }            
        except aiohttp.ClientError as e:
            return {
                "state": -1,
                "running_model": running_model,
                "tagsvalue":[],
                "error":f"模拟仿真设备计算失败{str(e)}"
            }
        except Exception as e:
            # raise ValueError(f"url:{url}, headers:{headers}, payload:{json.dumps(payload, indent=2, ensure_ascii=False)}, timeout:{2*time_duration+5}")
            return {"state": -3, "running_model": running_model,"tagsvalue": [], "error":f"未知错误{str(e)}"}
