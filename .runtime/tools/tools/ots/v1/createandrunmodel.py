from __runner__ import tool, Context

import aiohttp
import json
import asyncio
import xml.etree.ElementTree as ET
import os
import ipaddress
from pydantic import BaseModel
from typing import List, Dict, Union, Tuple

# 默认参数
STATIC_QIHUALU_PARAMS = {
  "running_model_tags": [
    {
      "running_model": "qhl",
      "tagsconfig": [
        {
          "datatype": 2,
          "desc": "进气化炉氧气流量",
          "name": "703FI007A.PV",
          "unit": "Nm3/h"
        },
        {
          "datatype": 2,
          "desc": "进气化炉煤浆流量",
          "name": "703FZT002A.PV",
          "unit": "m3/h"
        },
        {
          "datatype": 2,
          "desc": "氧煤比",
          "name": "YMB.C1",
          "unit": "%"
        },
        {
          "datatype": 2,
          "desc": "喷嘴温度",
          "name": "703TI005A.PV",
          "unit": "℃"
        },
        {
          "datatype": 2,
          "desc": "喷嘴压力",
          "name": "PZT004A.PV",
          "unit": "MPaG"
        },
        {
          "datatype": 2,
          "desc": "激冷室液位",
          "name": "703LI003A.PV",
          "unit": "%"
        },
        {
          "datatype": 2,
          "desc": "水洗塔液位",
          "name": "703LI009A.PV",
          "unit": "%"
        },
        {
          "datatype": 2,
          "desc": "急冷水流量",
          "name": "703FI010A1.PV",
          "unit": "m3/h"
        },
        {
          "datatype": 2,
          "desc": "水洗塔塔顶压力",
          "name": "703PZT055A.PV",
          "unit": "MPaG"
        },
        {
          "datatype": 2,
          "desc": "水洗塔进料流量",
          "name": "703FI017A.PV",
          "unit": "m3/h"
        }
      ]
    }
  ],
  "user_model": [
    {
      "likemodel": "qhl"
    }
  ],
  "user_snapshot": [
    {
      "likesnapshot": "稳态"
    },
    {
      "likesnapshot": "0926稳态"
    },
    {
      "likesnapshot": "稳态1031"
    }
  ]
}

STATIC_JIAYAJINGLIU_PARAMS = {
  "running_model_tags": [
    {
      "running_model": "jyjl",
      "tagsconfig": [
        {
          "datatype": 2,
          "desc": "脱乙烷塔塔顶温度",
          "name": "TI14125.PV",
          "unit": "℃"
        },
        {
          "datatype": 2,
          "desc": "脱乙烷塔塔釜温度",
          "name": "TI14120.PV",
          "unit": "℃"
        },
        {
          "datatype": 2,
          "desc": "脱乙烷塔塔釜乙烷浓度",
          "name": "AI140172.PV",
          "unit": "ppm"
        },
        {
          "datatype": 2,
          "desc": "回流量",
          "name": "FI14036.PV",
          "unit": "kg/h"
        },
        {
          "datatype": 2,
          "desc": "回流比",
          "name": "ZDY.REALHLB",
          "unit": "%"
        },
        {
          "datatype": 2,
          "desc": "脱乙烷塔塔顶压力",
          "name": "PI14037.PV",
          "unit": "MPa"
        }
      ]
    }
  ],
  "user_model": [
    {
      "likemodel": "jyjl"
    }
  ],
  "user_snapshot": [
    {
      "likesnapshot": "稳态0526"
    },
    {
      "likesnapshot": "稳态0530"
    }
  ]
}

L_INIT_MODELNAME_QIHUALU = [
    "qhl",
    "qihualu",
    "气化炉",
    "气化炉模型"
]
L_INIT_MODELNAME_JIAYAJINGLIU = [
    "jyjl",
    "jiayajingliu",
    "加压精馏",
    "加压精馏模型",
    "加压精馏通用模型"
]

# 预计算小写别名集合
_QIHUALU_ALIASES = {alias.lower() for alias in L_INIT_MODELNAME_QIHUALU}
_JYJL_ALIASES = {alias.lower() for alias in L_INIT_MODELNAME_JIAYAJINGLIU}

async def updateparams(context: Context, params: dict)->dict:
    user_model = params.get("user_model", [])
    
    # 检查 user_model 是否存在
    if not user_model:
        raise ValueError("-9，未识别模型信息，请重试")
    
    # 获取所有可能的 likemodel 值
    like_models = []
    if isinstance(user_model, list):
        for item in user_model:
            if isinstance(item, dict) and "likemodel" in item:
                model = item["likemodel"]
                if isinstance(model, str) and model.strip():
                    like_models.append(model)
    elif isinstance(user_model, dict) and "likemodel" in user_model:
        model = user_model["likemodel"]
        if isinstance(model, str) and model.strip():
            like_models.append(model)
    
    # 如果没有找到有效的 likemodel，返回原参数
    if not like_models:
        return params

    for like_model in like_models:
        lower_model = like_model.lower() 
        if lower_model in _QIHUALU_ALIASES:
            await context.add_view({
                "format": "debug",
                "content": {
                    "user_modelname": lower_model
                }
            })
            return STATIC_QIHUALU_PARAMS.copy()
        if lower_model in _JYJL_ALIASES:
            await context.add_view({
                "format": "debug",
                "content": {
                    "user_modelname": lower_model
                }
            }) 
            return STATIC_JIAYAJINGLIU_PARAMS.copy()

    return params

# 视图展示字段
L_TYPE_MAP = {
    0: "模型未启动",
    1: "模型正在运行",
    2: "模型暂停仿真",
    3: "模型终止仿真",
    4: "正在加载快照",
    5: "正在重演"
}

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
        # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成仿真工艺模型失败1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成仿真工艺模型失败2")
        except ValueError:
            raise ValueError("生成仿真模工艺模型异常")
            
        return server_ip, server_port

    except Exception as e:
        # print("工艺模型位置解析异常")
        return None

# 临时方案，现在去查x-source时，必须是工作流第一个节点，所有需要从向量库查询的东西，都先放到第一个节点
# 从向量库查询出来的数据先不要用，直接放到缓存里
async def cache_user_inputs(context: Context, params: dict)->dict:
    new_params = await updateparams(context, params)
    """缓存用户输入参数"""
    # 缓存用户模型
    user_model = new_params.get("user_model", [])
    if user_model:
        await context.set_cache("user_model", user_model)
    
    # 缓存用户工况
    user_snapshot = new_params.get("user_snapshot", [])
    if user_snapshot:
        await context.set_cache("user_snapshot", user_snapshot)
    
    # 缓存运行模型标签
    running_model_tags = new_params.get("running_model_tags", [])
    if running_model_tags:
        await context.set_cache("running_model_tags", running_model_tags)
    
    return new_params

class modelItem(BaseModel):
    modelname: str

class modellistResponse(BaseModel):
    state: int
    modellist: List[modelItem]

async def get_modellist_from_server(context: Context, serveraddr: str)->list:
    # 1. 获取工艺模型列表
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/getmodellist",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return -4
    
    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })
    # 默认的 URL
    list_url = message["url"]
    payload = {**(message["payload"]),"timeout": 60}
    headers = message["headers"]
    try:
        async with aiohttp.ClientSession() as session:
            list_response = await session.post(
                url=list_url,
                headers=headers,
                json=payload,
                timeout=60+5
            )
            list_response.raise_for_status()
            list_data = await list_response.json()
            model_list = list_data.get("modellist", [])
            
            # 数据验证与转换
            validated_data = [modelItem(**item).model_dump() for item in model_list if isinstance(item, dict)]
            
            return validated_data

    except aiohttp.ClientError as e:
        return []
    except asyncio.TimeoutError:
        return []

async def get_model_info(context: Context, serveraddr: str) ->int:
    """获取模型信息"""
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/getmodelinfo",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return -4
    
    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })

    payload = {**(message["payload"]),"timeout": 5}
    headers = message["headers"]
    getmodelinfo_url = message["url"]

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=getmodelinfo_url,
                headers=headers,
                json=payload,
                timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()
                return result.get("state", -3)
    
    except aiohttp.ClientError as e:
        return -1
    except asyncio.TimeoutError:
        return -2

# 参数转换
def switch_to_model_names(model_list: List[Dict], key: str = "modelname") -> List[str]:
    """从模型列表中提取名称"""
    return [
        item[key] 
        for item in model_list 
        if isinstance(item, dict) and key in item
    ]

# 动态表单的默认值和下拉列表
async def choice_model_selection(
    context: Context,
    user_model_key: str,
    user_models: List[Dict], 
    server_model_key: str,
    server_models: List[Dict]
) -> Tuple[List[str], str]:
    """
    确定模型选择列表和默认值
    返回: (可选模型列表, 默认选中模型)
    """
    server_model_names = switch_to_model_names(server_models, server_model_key)

    # user_model中元素必须要在server_models中存在
    new_user_models = []
    if user_models and server_model_names:
        for user_item in user_models:
            if user_item[user_model_key] in server_model_names:
                new_user_models.append(user_item)

    user_model_names = switch_to_model_names(new_user_models, user_model_key)
    
    # 确定候选列表（优先用户模型）
    candidate_model_names = user_model_names if user_model_names else server_model_names
    
    # 处理一下去重
    seen_names = set()
    unique_candidates = [x for x in candidate_model_names if not (x in seen_names or seen_names.add(x))]

    # 设置默认选中第一个
    default_name = unique_candidates[0] if unique_candidates else ""
    
    return unique_candidates, default_name

# 展示动态表单
async def show_model_selection_form(
    context,
    model_options: List[str],
    default_model: str,
    interaction_id: str = "loadnewmodel_form"
) -> Dict:
    """展示模型选择表单"""
    form_schema = {
        "type": "object",
        "properties": {
            "modelname": {
                "type": "string",
                "title": "工艺模型",
                "description": "请选择要加载的工艺模型",
                "enum": model_options,
                "enumNames": model_options
            }
        },
        "required": ["modelname"]
    }
    
    form_result = await context.get_interaction(interaction_id)
    if form_result:
        return form_result

    await context.require_interaction({
        "id": interaction_id,
        "title": "加载模型配置",
        "type": "form",
        "form": {
            "schema": form_schema,
            "default": {"modelname": default_model}
        }
    })
    return {}

async def select_model(context: Context, params: any, serveraddr: str)->str:
    user_modellist = params.get("user_model", [])
    user_model_key = "likemodel"
    
    server_modellist = await get_modellist_from_server(context, serveraddr)
    server_model_key = "modelname"

    # 2. 确定模型选项列表和默认值
    model_options  = []
    default_model  = ""
    selected_model = ""
    model_options, default_model = await choice_model_selection(context, user_model_key, user_modellist, server_model_key, server_modellist)
    
    if not model_options:
        await add_card(
            context, 
            "summary", 
            "加载工艺模型失败", 
            "无可用工艺模型"
            )
        raise ValueError(f"state:{-3}, 需要加载工艺模型未明确, 加载工艺模型失败")

    # 如果返回的model_options只有一个，则直接执行，不用用户确认
    if len(model_options) == 1:
        selected_model = default_model
    else:
        # 在调用 show_selection_form 前检查是否已有交互结果, 仅首次展示提示
        if not await context.get_interaction("loadnewmodel_form"):
            await add_card(
                context, 
                "tip", 
                "已生成多个可用模型，请从中选择一个模型加载", 
                ""
                )

        # 4. 构造动态表单schema
        form_result  = await show_model_selection_form(context, model_options, default_model)
        if not form_result:
            await add_card(
            context, 
            "summary", 
            "用户未选择工艺模型，加载模型失败", 
            "未选择模型"
            )
            raise ValueError(f"state:{-4}, 需要加载工艺模型未明确, 用户未选择工艺模型，加载模型失败")
         
        # 处理表单数据
        selected_model = form_result["modelname"]
    return selected_model

async def loadmodel(context: Context, params: any, serveraddr: str)->tuple[int, str]:
    await add_card(
            context, 
            "tip", 
            "正在加载模型，请稍后。。。", 
            "正在加载模型"
            )
    selected_model = await select_model(context, params, serveraddr)
    if not selected_model:
        return -5, ""
    
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/loadmodel",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return -6
    
    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })
    # 默认的 URL
    url = message["url"]
    payload = {**(message["payload"]),"modelname": selected_model}
    headers = {
        **message["headers"],  # 解包原headers,覆盖Content-Type
        "Content-Type": f"{message['headers']['Content-Type']}; charset=utf-8"
    }
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=30
            ) as response:
                response.raise_for_status()
                result = await response.json()
                resultstat = result.get("state")
                if resultstat is None or resultstat != 0:
                    error_code = resultstat if resultstat is not None else -6
                    return error_code, ""
                
                # 防止加载过程未完成就设置启动状态导致失败，这里等一会儿
                await asyncio.sleep(15)
                # hmi工程切换延后，移到流程图节点

                return resultstat, selected_model

        except aiohttp.ClientError as e:
            return -1, ""
        except asyncio.TimeoutError:
            return -2, ""

async def runmodel(context: Context, params: any, serveraddr: str, loaded_model: str)->tuple[int, str]:
    await add_card(
        context, 
        "tip", 
        "正在运行模型，请稍后。。。", 
        "正在运行模型"
        )
    
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/startmodel",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return -3
    
    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })
    # 默认的 URL
    url = message["url"]
    payload = {**(message["payload"]), "runstate": 1}# 数值 1 表示通知服务端根据已加载的工艺模型开始运行仿真计算
    headers = message["headers"]

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()

                result_state = result.get("state")
                if result_state is None or result_state != 0:
                    error_code = result_state if result_state is not None else -3
                    return error_code, ""
                
                # 服务端异步通知状态变化，要等一下
                await asyncio.sleep(10)

                return result_state, loaded_model

        except aiohttp.ClientError as e:
           return -1, ""
        except asyncio.TimeoutError:
           return -2, ""

@tool(version="*")
async def createandrunmodel(context: Context, params: any):
    # 网关开关
    IsOpenGW = bool(context.config.get('gateway_onoff', False))
    if IsOpenGW:
        await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": "请稍作等待，正在创建工艺模型",
                    "description": "",
                    "details": ""
                }
            })
        result = await context.call_tool("initotsInstance", context=context, params={"host_type":2,"port_type":4})
        if not result or result.get("result", -1) < 0:
            raise ValueError(f"生成工艺模型失败，系统资源不足，请稍后再试{result}")
    
    await context.add_view({
                "format": "debug",
                "content": {
                    "init_result": result
                }
            })

    await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "请稍作等待，后续需要您进行相关信息确认",
                "description": "",
                "details": ""
            }
        })
    # 1. 缓存所有用户输入参数, 临时方案
    new_params = await cache_user_inputs(context, params)
    # 默认的 URL
    server_address = "localhost:9999"
    try:
        config = get_ots_http_server_config(context)
        if config is None:
            raise ValueError("生成工艺模型失败，未找到工艺模型位置")
        # 确保 config 是一个包含两个元素的元组
        if isinstance(config, tuple) and len(config) == 2:
            host, port = config
            server_address = f"{host}:{port}"
        else:
            raise ValueError("生成工艺模型失败，未拿到足够的模型位置信息")    
    except ValueError as ve:
        raise ValueError(f"生成工艺模型失败，工艺模型位置坐标解析失败: {ve}")
    except Exception as e:
        raise ValueError(f"生成工艺模型失败，寻找工艺模型位置发生错误: {e}")
    # 1、检测服务状态
    result_state = await get_model_info(context, server_address)

    # state为[0,5]表示服务端状态正常，参照schema定义
    # 服务端是否已加载工艺模型，根据modelname是否为空判断，modelname为空时表示为服务端尚未加载工艺模型
    # user_model 为用户文本提取模型名称，这里返回是作为工作流上下文参数传递
    if result_state < 0 or result_state > 5:
        raise ValueError(f"生成工艺模型状态异常: {result_state}")

    await add_card(
    context, 
    "tip", 
    "工艺模型已生成，状态检测正常，准备加载模型。", 
    "已生成工艺模型，状态检测正常"
    )

    load_result, loaded_model = await loadmodel(context, new_params, server_address)
    if load_result != 0:
        raise ValueError(f"工艺模型加载异常: {load_result}")
    
    if loaded_model:
        await context.set_cache("loaded_model", loaded_model)

    running_result, running_model = await runmodel(context, new_params, server_address, loaded_model)

    if running_result != 0:
        raise ValueError(f"工艺模型运行异常: {running_result}")

    if running_model:
        await context.set_cache("running_model", running_model)

    await add_card(
    context, 
    "summary", 
    "模型运行正常，现在可以模拟和观察仿真设备状态变化了。", 
    "模型运行正常"
    )

    return {
        "state": running_result,
        "running_model" : running_model
    }