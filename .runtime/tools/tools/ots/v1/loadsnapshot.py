from __runner__ import tool, Context

import aiohttp
import json
import asyncio
from pydantic import BaseModel, Field
from typing import Dict, List, Optional
import xml.etree.ElementTree as ET
import os
import ipaddress

def get_ots_http_server_config(context:Context):
    """
    读取指定XML文件中的HttpServer的IP和端口配置。

    :param file_path: XML文件的路径，默认为当前目录下的HttpServerConfig.xml
    :return: 包含IP和端口的元组 (ip, port)，如果未找到则返回 None
    """
    try:
       # 获取属性值
        server_ip = context.config['coreserver_host'] or ""
        server_port = context.config['httpserver_port'] or ""

        # 验证host（允许域名或IP）
        if not server_ip or not isinstance(server_ip, str):
            raise ValueError("生成工况失败，未找到工艺模型1")
        try:
            # 尝试将 port 转换为整数
            server_port = int(server_port)
            # 确保 host 和 port 都是有效的
            if not (0 <= server_port <= 65535):
                raise ValueError("生成工况失败，未找到工艺模型2")
        except ValueError:
            raise ValueError("生成工况异常，未找到工艺模型")
            
        return server_ip, server_port

    except Exception as e:
        # print(f"工艺模型位置解析异常")
        return None

# 定义数据模型（根据实际接口返回结构调整）
class UserSnapshotItem(BaseModel):
    likesnapshot: str

class ServerSnapshotItem(BaseModel):
    class Config:
        extra = 'ignore' # 忽略多余字段
    id: str
    name: str
    ltype: int = Field(..., ge=2, le=4)  # 2-手动,3-自动,4-标准

"""生成唯一且可读的工况展示名称"""  
# 下拉列表选项展示[类型]_[名称]#[id后4位]
def generate_display_name(item: ServerSnapshotItem) -> str:
    type_map = {
        2:"[手动]",
        3:"[自动]",
        4:"[标准]"
    }
    type_suffix = type_map.get(item.ltype, f"[类型{item.ltype}]")
    base_name = item.name or f'未命名工况'
    # 添加ID后缀防重复（取最后4位）
    id_suffix = f"#{item.id[-4:]}" if len(item.id) >=4 else f"#{item.id}"
    # 构造基础显示名
    display_name = f"{type_suffix}_{base_name}{id_suffix}"
    
    return f"{display_name}"

async def show_selection_form(context: Context, options: List[Dict], default_id: str = "") -> Optional[Dict]:
    """展示选择表单"""
    # 生成唯一选项列表（格式：显示名 -> ID）
    unique_options = {}
    for item in options:
        base_name = generate_display_name(item)
        display_name = base_name
        suffix = 1
        # 确保显示名称唯一性
        while display_name in unique_options:
            display_name = f"{base_name}({suffix})"
            suffix += 1
        
        unique_options[display_name] = item.id

    form_schema = {
        "type": "object",
        "properties": {
            "selected_id": {
                "type": "string",
                "title": "选择工况",
                "enum": list(unique_options.values()),
                "enumNames": list(unique_options.keys())
            }
        },
        "required": ["selected_id"]
    }
    
    interaction_id = "loadsnapshot_form"
    form_result = await context.get_interaction(interaction_id)
    if form_result:
        return form_result
    
    await context.require_interaction({
        "id": interaction_id,
        "title": "选择加载工况",
        "type": "form",
        "form": {
            "schema": form_schema,
            "default": {"selected_id": default_id}
        }
    })
    return {}

def select_candidate_snapshots(
    user_snapshots: List[UserSnapshotItem],
    server_snapshots: List[ServerSnapshotItem]
) -> tuple[List, str]:
    """
    选择候选工况列表
    返回: {
        "candidates": List[ServerSnapshotItem],  # 候选列表
        "default_id": str,                       # 默认选中ID
        "error": Optional[Dict]                  # 错误信息(如果有)
    }
    """
    candidate_snapshots = []
    default_id = ""
    
    # 规则1: user_snapshot不为空
    if user_snapshots and server_snapshots:
        # 从server_snapshots中匹配likename
        for user_item in user_snapshots:
            for server_item in server_snapshots:
                if user_item.likesnapshot.lower() in server_item.name.lower():
                    candidate_snapshots.append(server_item)
        
        # 如果没有匹配项，使用server_snapshots的全部
        if not candidate_snapshots:
            candidate_snapshots = server_snapshots
    
    # 规则2: user_snapshot为空但server_snapshotlist不为空
    elif server_snapshots:
        candidate_snapshots = server_snapshots
    
    # 新增：对候选工况进行去重（基于ID）
    seen_ids = set()
    unique_candidates = []
    for item in candidate_snapshots:
        if item.id not in seen_ids:
            seen_ids.add(item.id)
            unique_candidates.append(item)
    
    candidate_snapshots = unique_candidates
    
    # 设置默认选中第一个
    default_id = candidate_snapshots[0].id if candidate_snapshots else ""
    
    return candidate_snapshots, default_id

async def get_snapshotlist_from_server(context: Context)->list:
    # 1. 获取工况列表
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/getsnapshotlist",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return []
    
    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })

    payload = {**(message["payload"]),"snapshottype": 4}
    headers = message["headers"]
    list_url = message["url"]

    try:
        async with aiohttp.ClientSession() as session:
            list_response = await session.post(
                url=list_url,
                headers=headers,
                json=payload,
                timeout=5
            )
            list_response.raise_for_status()
            list_data = await list_response.json()
            snapshot_list = list_data.get("snapshotlist", [])
            
            # 数据验证与转换
            # 返回的数据不止id, name, ltype三个字段，这样的验证是否会出问题
            validated_data = [ServerSnapshotItem(**item) for item in snapshot_list]
            
            return validated_data

    except aiohttp.ClientError as e:
        return []

# 创建卡片按钮
async def add_card(context, card_type, content, title=None):
    if title is None:
        title = ""
    if card_type != "file" and card_type != "card":
        await context.add_view({
            "format": "markdown",
            "content": "#### " + title + "\r\n\r\n" + content
        })
    else:
        await context.add_view({
            "format": "card",
            "content": {
                "type": card_type,
                "title": title,
                "details": content
            }
        })

@tool(version="*")
async def loadsnapshot(context: Context, params: any):
    # 初始化参数
    params = params or {}
    # 正在运行的工艺模型
    running_model = await context.get_cache("running_model") or params.get("running_model", "")
    if not running_model:
        raise ValueError(f"state:{-5}, 工艺模型未处于仿真计算状态，无法模拟生成工况，加载工况")

    user_snapshotlist = await context.get_cache("user_snapshot") or params.get("user_snapshot", [])
    user_snapshots = [UserSnapshotItem(**item) for item in user_snapshotlist]

    # 没传入工况列表，直接向服务端请求
    # if not server_snapshots:
    server_snapshots = await get_snapshotlist_from_server(context)

    candidate_snapshots, default_id = select_candidate_snapshots(user_snapshots, server_snapshots)

    if not default_id:
        await add_card(
            context, 
            "summary", 
            "生成工况失败，无法加载工况", 
            "无可用工况"
            )
        raise ValueError(f"state:{-3}, 工艺模型无法生成工况")
    
    # 如果返回的model_options只有一个，则直接执行，不用用户确认
    if len(candidate_snapshots) == 1:
        snapshot_index  = default_id
    else:
        # 在调用 show_selection_form 前检查是否已有交互结果
        if not await context.get_interaction("loadsnapshot_form"):
            # 要弹出动态表单需要用户确认前，提示用户需要确认的原因
            promptmsg = "已模拟生成多个可用工况"
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": f"{promptmsg}，请选择一个工况加载",
                    "description": "",
                    "details": ""
                }
            })
            await add_card(context, "tip", f"{promptmsg}，请选择一个工况加载", "")  # 仅首次展示提示
            
        form_result = await show_selection_form(context, candidate_snapshots, default_id)
        if not form_result:
            await add_card(
            context, 
            "summary", 
            "未选择工况，无法加载工况", 
            "未选择工况"
            )
            raise ValueError(f"state:{-6}, 用户未选择工况，无法加载工况")
        
        await context.add_view({
                    "format": "tip",
                    "content": {
                        "type": 'default',
                        "title": '',
                        "content": "请稍作等待，后续需要您进行相关信息确认",
                        "description": "",
                        "details": ""
                    }
                })
        # 处理表单数据
        snapshot_index = form_result["selected_id"]
 
    snapshot_name = next(item.name for item in candidate_snapshots if snapshot_index == item.id)
    
    message = context.call_tool("package_http_message",context=context,params={
        "http_url":"/api/loadsnapshot",
        "host_type":0,
        "port_type":0
    })
    if not message:
        return {
                "state": -6,
                "loaded_snapshot": "",
                "running_model": ""
            }

    await context.add_view({
                "format": "debug",
                "content": {
                    "http_message": message
                }
            })

    payload = {**(message["payload"]),"snapshot_index":snapshot_index}
    headers = message["headers"]
    url = message["url"]

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                url=url,
                headers=headers,
                json=payload,
                timeout=5
            ) as response:
                response.raise_for_status()
                result = await response.json()
                result_state = result.get("state")
                if result_state != 0:
                    raise ValueError(f"state:{-4}, 模型加载工况失败，模型当前状态：{result_state}")
                # 添加总结性描述
                await add_card(
                    context, 
                    "tip", 
                    "正在加载工况，请稍后。。。", 
                    "正在加载工况"
                    )
                await asyncio.sleep(10)
                # 添加总结性描述
                await add_card(
                    context, 
                    "summary", 
                    "工艺模型成功加载工况，现在尝试观察设备加载工况后变化。", 
                    "工况加载成功"
                    )
                
                if snapshot_name:
                    await context.set_cache("loaded_snapshot", snapshot_name)

                return {
                    "state": result_state,
                    "loaded_snapshot": snapshot_name,
                    "running_model": running_model
                }
            
        except aiohttp.ClientError as e:
            raise ValueError(f"state:{-1}, 工艺模型加载工况失败")
        except asyncio.TimeoutError:
            raise ValueError(f"state:{-2}, 工艺模型加载工况超时")
