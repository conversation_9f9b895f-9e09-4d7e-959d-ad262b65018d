from __runner__ import tool, Context, validator
import json
import os
from typing import Any, Dict, Optional

@tool(private=True)
async def user_form_filling(
    context: Context,
    params: Any= ""
):

    form_result = await context.get_interaction("exam_form")
    # await context.log_info("准备提交表单")
    if form_result is None:
        context.require_interaction({
            "id": "exam_form",
            "title": "请输入以下内容",
            "type": "form",
            "form": {
                "form_type": "",
                "schema": {
                    "description": "",
                    "type": "object",
                    "order": ["file","Selection","TrueOrFalse","BlankFilling","Subjective"],
                    "properties": {
                                "file": {
                                    "title": "生成试题依赖文件",
                                    "type": "string",
                                    "format": "file-object",
                                    "description": '',
                                    "widget": "tptfile",
                                    "x-validator": "data_check_file",
                                    "file_size": 1024 * 1024 *5
                                },
                                "Selection": {
                                    "type": "integer",
                                    "title": "选择题数目",
                                    "default": 2,
                                },
                                "TrueOrFalse": {
                                    "type": "integer",
                                    "title": "判断题数目",
                                    "default": 0
                                },
                                "BlankFilling": {
                                    "type": "integer",
                                    "title": "填空题数目",
                                    "default": 0
                                },
                                "Subjective": {
                                    "type": "integer",
                                    "title": "主观题数目",
                                    "default": 0
                                },
                            },
                            "required":[
                                "Selection",
                                "TrueOrFalse",
                                "BlankFilling",
                                "Subjective",
                                "file"
                            ]
                        }
                    }
                }
            )
        
    return {
        "result": form_result,
    }


@validator(version="*")
async def data_check_file(context: Context, target: any, payload: any = None):
    if target is None or target == "":
        return
    file_info = json.loads(target)
    object_path = file_info["object"]
    _, extension = os.path.splitext(object_path)
    # 清洗扩展名：移除点号 + 转小写
    clean_extension = extension.lstrip('.').lower() if extension else None
    if clean_extension != "docx" and clean_extension != "txt" and clean_extension != "pdf":
        raise ValueError("文件格式仅支持.docx/.txt/.pdf，请重新上传文件！")
    # 直接返回即为成功
    return
