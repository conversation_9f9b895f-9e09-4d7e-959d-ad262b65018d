_MC_TEMPLATE_EN = """
Below is the textual knowledge, please generate {question_num} multiple choice questions based on the following content and output the parsed results as required.
#Background knowledge.    
{context}

The generated questions are populated into the "question" field in the JSON.
The generated multiple choice question contains a list of four options, which are populated into the "answer_optional" field in the JSON.
The correct answer to the generated multiple choice question is entered into the JSON "answer" field.
A detailed explanation of the correct answer to the generated multiple choice question, filled into the "explain" field in the JSON.
Generated questions and answers must be faithful to the original text.
If the knowledge contains references, tables of contents, etc. that are not relevant, please ignore them.
Make sure the questions are multiple choice with only one answer.

Please think step-by-step and make sure to answer only in JSON format as follows:
    [{{
            "question": Generated question,
            "answer_optional": ["option1", "option2", "option3", "option4"],
            "answer": ["option1"],
            "explain": Explanation of the correct answer
    }}]
"""

_MC_TEMPLATE_ZH = """
你是一个内容评估和出卷专家，可以评估用户给出的上下文,如果上下文中只包含目录，作者等无用信息，请直接输出"failed"即未通过审核,
如果审核通过，生成{question_num}个选择题，并按要求输出结果,

#上下文:    
{context}

#内容评估：
    1.审核未通过直接输出"failed",不要做出解释
    2.输出的"failed" 是string不是列表,json或者其他数据形式

#题目要求:
    1.生成的问题填入JSON中的"question"字段,必须是string
    2.生成的选择题包含四个选项列表,四个选项的列表填入JSON中的"answer_optional"字段.
    3.生成的选择题正确答案,填入JSON中的"answer"字段,并保证填入了列表中即answer是一个有一个元素的列表。
    4.生成的选择题正确答案的详细解释,填入JSON中的"explain"字段.
    5.生成问题与答案必须忠于原文.
    7.请确保问题是只有一个正确选项的单项选择题.
    8.请生成中文问题，即便背景知识不是中文也请生成中文试题。

    请一步一步思考,确保只以JSON格式回答,具体格式如下：
        [{{
                "question": 生成的题目,
                "answer_optional": ["选项1","选项2","选项3","选项4"],
                "answer": ["选项1"],
                "explain": 正确答案的解释
        }}]
"""

_TF_TEMPLATE_EN = """
Below is the textual knowledge, please generate {question_num} True-or-False  questions based on the following content, and output the parsed results as required.

#Background knowledge.    
{context}

The generated questions are populated into the "question" field in the JSON.
The generated question contains a list of options: "True" and "False", the list is filled into the "answer_optional" field in JSON.
The correct answer to the generated question is entered into the "answer" field in the JSON.
A detailed explanation of the correct answer to the generated  question, filled into the "explain" field in the JSON.
Generated questions and answers must be faithful to the original text.
If the knowledge contains references, tables of contents, or other irrelevant content, please ignore them.

Please think step by step and make sure to answer only in JSON format, as follows:
    [{{
            "question": Generated question,
            "answer_optional": ["True", "False"],
            "answer": ["True"]
            "explain": Explanation of the correct answer
    }}]
"""

_TF_TEMPLATE_ZH = """
你是一个内容评估和出卷专家，可以评估用户给出的上下文,如果上下文中只包含目录，作者等无用信息，请直接输出"failed"即未通过审核,
如果审核通过，生成{question_num}个判断题，并按要求输出结果,

#上下文:    
{context}

#内容评估：
    1.审核未通过直接输出"failed",不要做出解释
    2.输出的"failed" 是string不是列表,json或者其他数据形式

#题目要求：

    1.生成的问题填入JSON中的"question"字段，即string
    2.生成的判断题包含两个选项列表:"正确"和"错误",两个选项列表填入JSON中的"answer_optional"字段.
    3.生成的判断题正确答案,填入JSON中的"answer"字段.即"answer"是一个有一个元素的列表。
    4.生成的判断题正确答案的详细解释,填入JSON中的"explain"字段.
    5.生成问题与答案必须忠于原文.
    6.请生成中文问题，即便背景知识不是中文也请生成中文试题。

    请一步一步思考,确保只以JSON格式回答,具体格式如下：
        [{{
                "question": 生成的题目,
                "answer_optional": ["正确","错误"],
                "answer": ["正确"],
                "explain": 正确答案的解释
        }}]
"""

_BF_TEMPLATE_EN = """
As an Educational Content Specialist, generate {question_num} fill-in-the-blank questions based on the given knowledge of industry-related text. 
Ensure that each question has only one clear blank bit denoted by "__" for fill-in-the-blank.

#context_knowledge.    
{context}  

Please generate questions and answers in the following format, and ensure that the answers are accurate and the explanations are detailed:  

    [{{  
        "question": "Generated fill-in-the-blank question title, e.g., In industrial production, __ is a common processing method." ,  
        "answer_optional": [],  
        "answer": ["Machining"],
        "explain": "Machining is a widely used processing method in industrial production that utilizes mechanical equipment to make changes in the shape, size, and properties of a workpiece."  
    }}]    

Please complete this task carefully and step by step to make sure we get the correct answer:. 
1. The questions and answers generated must be faithful to the original text and relevant to industrial knowledge.  
2. please make sure that each question has a clear blank space, indicated by a "__"
3. make sure that [] is entered in the "answer_optional" field. 4. make sure that only one correct answer is entered.
4. make sure that only one correct answer is entered in the "answer" field. 5.   
5. Ignore references, tables of contents, etc., if they are not relevant to this knowledge.
"""

_BF_TEMPLATE_ZH = """  
你是一个内容评估和出卷专家，可以评估用户给出的上下文,如果上下文中只包含目录，作者等无用信息，请直接输出"failed"即未通过审核,
如果审核通过，生成{question_num}个填空题，并按要求输出结果,

#上下文:    
{context}

#内容评估：
    1.审核未通过直接输出"failed",不要做出解释
    2.输出的"failed" 是string不是列表,json或者其他数据形式

#题目要求：

    请按照以下格式生成问题和答案，并确保答案的准确性和解释的详细性：  

    [{{  
        "question": "生成的填空题题目,如:在工业生产中,__是一种常用的加工方法。",  
        "answer_optional": [],  
        "answer": ["机械加工"],  
        "explain": "机械加工在工业生产中是一种广泛使用的加工方法，它利用机械设备对工件进行形状、尺寸和性能的改变。"  
    }}]    

    请仔细地一步步完成这一任务，以确保我们得到正确的答案: 
    1. 生成的问题和答案必须忠于原文，并与工业知识相关。  
    2. 请保证每个题目都有一个明确的空白位,用"__"表示
    3. 确保将[]填入"answer_optional"字段。
    4. 确保只有一个正确答案填入"answer"字段,即"answer"是个有一个元素的列表
    5. 请生成中文问题，即便背景知识不是中文也请生成中文试题。
"""

_SUB_TEMPLATE_EN = """
Below is the textual knowledge, please generate {question_num} quiz questions based on the following content and output the parsed results as required.

#Background knowledge.    
{context}  

The answers to the generated questions should be no less than 15 words, and the answers should be able to be divided into points, so that 3 answers are appropriate.
The generated question is populated into the "question" field of the JSON.
The correct answer to the generated question is entered into the JSON "answer" field.
The generated question contains a list of keywords for evaluating the correct or incorrect answer points. As long as the user's answer hits these keywords, it is considered correct or partially correct, and the list of keywords for the answer points is populated into the "answer_optional" field in the JSON.
The detailed explanation of the correct answer to the generated quiz question is filled into the "explain" field in the JSON.
Generated questions and answers must be faithful to the original text.
If the knowledge contains references, tables of contents, etc. that are not relevant, please ignore them.

Please think step by step and make sure to answer only in JSON format, as follows:
    [{{
            "question": Generated question,
            "answer_optional": [correct_keyword1, correct_keyword2, correct_keyword3],
            "answer": Generate the correct answer to the question,
            "explain": Explanation of the correct answer,
    }}]
"""

_SUB_TEMPLATE_ZH = """
你是一个内容评估和出卷专家，可以评估用户给出的上下文,如果上下文中只包含目录，作者等无用信息，请直接输出"failed"即未通过审核,
如果审核通过，生成{question_num}个选择题，并按要求输出结果,

#上下文:    
{context}

#内容评估：
    1.审核未通过直接输出"failed",不要做出解释
    2.输出的"failed" 是string不是列表,json或者其他数据形式

#题目要求：
    1.生成的问答题的答案不少于15个字,答案能进行分点回答以3个为适宜.
    2.生成的问题填入JSON中的"question"字段.
    3.生成的问答题正确答案,填入JSON中的"answer"字段.
    4.生成的问答题包含一个用于评价正确与否的答案要点的关键词列，只要用户回答命中这些关键字就认为正确或者部分正确,把这个答案要点的关键词列表填入JSON中的"answer_optional"字段.
    5.生成的问答题正确答案的详细解释,填入JSON中的"explain"字段.
    6.生成问题与答案必须忠于原文.
    7.请生成中文问题，即便背景知识不是中文也请生成中文试题。
    8.不要生成关于作者的问题

    请一步一步思考,确保只以JSON格式回答,具体格式如下：
        [{{
                "question": 生成的题目,
                "answer_optional": [正确关键词1,正确关键词2,正确关键词3],
                "answer": 生成题目的正确答案,
                "explain": 正确答案的解释
        }}]
    """

_marking_rubric_en = """
As an impartial and unbiased examiner, your task is to calculate the score of the user's answer based on the information provided.

Given the info below.
{exam_items}

## explanation of info:
    exam_item_x: x answer
        total_mark: total mark
        question: question
        answer: reference answer
        user_answer: user's answer
        
Please use the following steps to calculate the user's total score along with the judging criteria:
    Steps
    - Evaluate the user's answer based on the reference answer to the question, and if there is ambiguity, evaluate it in conjunction with the question.
    - Make sure that the your final score does not exceed the total score of the question.
    - Convert the calculated score to an integer.
    - Enter the score in the following JSON format:
    - If the user's answer exactly matches the reference answer, please give hime full mark.
    
    Judging criteria
    - 1. Completeness, score = 0.7 * total score. Score for key points = score / number of key points. You need to determine which key points are involved.
        - Judging criteria: Does the answer include all the key points in the reference answer?
        - Score allocation: Full inclusion of key points scores full marks, each included key point scores the score for each key point, and each key point not included scores 0.
    
    - 2. Accuracy, score for this item = 0.1 * total score
    - Scoring criteria: Whether the calculation method mentioned in the answer is accurate.
    - Score allocation: full accuracy scores full marks for this item, partial accuracy scores half marks, and inaccuracy scores 0 marks.
    
    - 3. Clarity, score = 0.1 * total score
    - Scoring criteria: Is the answer clear and easy to understand, and does it use the right terminology?
    - Score allocation: Full marks for very clear, half marks for generally clear, 0 marks for unclear.
    
    - 4. Detail, score = 0.1 * total score
    - Scoring criteria: Does the answer provide additional relevant information or examples to support their point?
    - Score allocation: Full marks for providing detailed information, 0 for not providing any.
    
    Output the total score calculated above in the following JSON format, and round it to an integer. Please note that there may be multiple answers, and each answer needs to be scored.
    
    [{{
        "mark": score
    }},
    {{
        "mark": score
    }},
    ]
    
    Please be fair and impartial when marking the answers, and do not let your emotions get the better of you.
    If a user's answer is irrelevant to the question, please mark it accordingly.
    Do not give explanations, just output the answer.
    Let's solve this problem step by step to make sure we get the correct answer.


"""

_marking_rubric = """

作为一名公平且无偏见的考官，你的任务是根据提供的信息计算用户回答的得分。

现在给出如下的相关信息:
{exam_items}

## 相关信息字段解析:
    exam_item_x:第x个回答
        total_mark: 总分
        question: 问题
        answer: 参考答案
        user_answer: 用户的回答
        
## 请使用以下步骤来以及评判标准计算用户的总得分：
    步骤
    - 根据问题的参考答案，评估用户的回答,如果出现模糊的情况请结合问题评估
    - 你不仅要结合参考答案还要结合通用知识来判断如下分数
    - 确保你的最终评分不超过问题的总分数也不会低于0分
    - 如果用户没有给出回答，你应该判0分
    - 将得分填入以下JSON格式中

    评判标准
    - 1.完整性,此项分值=0.7*总得分。关键点得分=此项分值/关键点个数,你需要自己判断有哪些关键点.
        - 评分标准：回答是否包含了所有参考答案中的关键点。
        - 分值分配：完全包含关键点得满分，每包含一个关键点得分为每个关键点应得分数，一个都未包含得0分。
    
    - 2.准确性,此项分值=0.1*总得分
        - 评分标准：回答中提到的计算方式是否准确无误。
        - 分值分配：完全准确得此项得满分，部分准确得一半分，不准确得0分。
    
    - 3.清晰度,此项分值=0.1*总得分
        - 评分标准：回答是否清晰、易于理解，是否使用了恰当的术语。
        - 分值分配：非常清晰得满分，一般清晰得一半分，不清晰得0分。
    
    - 4.详细程度,此项分值=0.1*总得分
        - 评分标准：回答是否提供了额外的相关信息或例子来支持其观点。
        - 分值分配：提供了详细信息得满分，提供部分得一半分，未提供得0分。
    
    将上述所有计算所得总得分数输出到如下JSON格式中，并四舍五入总得分为整数,请注意可能有多个回答，每个回答都需要判分。
    
    [{{
        "mark": 得分
    }},
    {{
        "mark": 得分
    }},
    ]
    
    请公平公正地进行评分，不要参杂感情色彩。
    如果用户的答案与问题无关，请根据实际情况评分。
    不要给出解释只需输出答案。
    让我们一步一步地解决这个问题，以确保我们得到正确的答案。
"""

_CONTEXT_CHECK= """
你是一个内容审核员，需要审核用户给出的上下文，但是有时候上下文的内容包含了目录，作者等无用信息，
如果用户提供的的上下文中包含了无用信息，请你在审核后直接输出"Invalid Content"，
[Note]
1.不要输出其他的信息只要输出"Invalid Content"
2.不要做出解释

现在给定如下上下文:
{{Context}}

"""

class Prompt:
    multiple_choice_prompt_en = _MC_TEMPLATE_EN
    multiple_choice_prompt_cn = _MC_TEMPLATE_ZH
    true_false_prompt_en = _TF_TEMPLATE_EN
    true_false_prompt_cn = _TF_TEMPLATE_ZH
    blank_filling_prompt_en = _BF_TEMPLATE_EN
    blank_filling_prompt_cn = _BF_TEMPLATE_ZH
    subjective_prompt_en = _SUB_TEMPLATE_EN
    subjective_prompt_cn = _SUB_TEMPLATE_ZH
    calculate_mark_prompt_en = _marking_rubric_en
    calculate_mark_prompt_cn = _marking_rubric
