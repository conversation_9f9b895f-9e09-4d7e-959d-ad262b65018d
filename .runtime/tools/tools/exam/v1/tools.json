{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "process_received_form", "description": "生成考题", "params": {"type": "object", "description": "生成考题", "properties": {"userinput": {"type": "string", "description": "用户输入信息"}}}, "result": {"type": "object", "description": "返回是否操作成功", "properties": {"Isover": {"type": "boolean", "description": "是否结束流程 false 需要进行下一步操作"}, "code": {"type": "integer", "description": "返回信息码"}, "info": {"type": "string", "description": "返回信息"}}}, "alias": ["考试题目生成"], "catalog": "control", "skip_summary": true}]}