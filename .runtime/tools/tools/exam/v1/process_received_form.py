from __runner__ import tool, Context, validator
import json
import os
from typing import Any, Dict, Optional
from .prompt_exam import Prompt
import aiohttp
import asyncio
import time


@tool(version="*")
async def process_received_form(
        context: Context,
        params: Any
):
    # GENERATE_EXAM_URL = "http://supcon-exam-indu-dev.supcon5t.com/api/exam/get_exam_paper"
    # QUERY_QUESTIONS_URL = "http://supcon-exam-indu-dev.supcon5t.com/api/exam/get_task_status"
    GENERATE_EXAM_URL = context.config["exam_form_filling_url"]
    QUERY_QUESTIONS_URL = context.config["exam_process_received_form_url"]
    # form_result = await context.get_interaction("exam_form")
    form_result = await context.call_tool("user_form_filling")
    # await context.log_info(f"form_result 返回数据：{form_result}")

    headers = {"content-type": "application/json"}

    selection_num = form_result.get("result", {}).get("Selection", 0)
    true_or_false_num = form_result.get("result", {}).get("TrueOrFalse", 0)
    blank_filling = form_result.get("result", {}).get("BlankFilling", 0)
    subjective_num = form_result.get("result", {}).get("Subjective", 0)
    file = form_result.get("result", {}).get("file", None)
    file_json = json.loads(file)
    file_type = type(file_json)
    # await context.log_info(f"file_json 返回数据：{file_json}:类型是:{file_type}")

    async with aiohttp.ClientSession() as session:
        payload = {
            "multiple_choice_num": selection_num,
            "blank_filling_num": blank_filling,
            "subjective_num": subjective_num,
            "true_false_num": true_or_false_num,
            "bucket": file_json["bucket"],
            "object": file_json["object"]
        }
        returned_bucket = ""
        returned_object = ""

        #### 产生UUID
        task_id = ""

        try:
            async with session.post(GENERATE_EXAM_URL, json=payload, timeout=10, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    # await context.log_info(f"返回task_id：{data}")
                    code = data.get("code", [])
                    if code == 200:
                        task_id = data.get("task_id", "")

                else:
                    print(f"获得状态码失败: {response.status}")
        except Exception as e:
            print(f"获取失败: {str(e)}")

        query_id = {
            "task_ids": [task_id]
        }
        bucket_info = dict()
        # await context.log_info(f"UUID返回数据：{query_id}")
        startTime = time.time()
        ## 轮询查看状态
        while True:
            try:
                # 发送请求，设置10秒超时
                async with session.post(QUERY_QUESTIONS_URL, json=query_id, timeout=10, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        # await context.log_info(f"轮询成功，数据:{data}")
                        codes = data.get("codes", [])
                        if 200 in codes:
                            returned_bucket = data.get("bucket", "")
                            returned_object = data.get("object", "")
                            bucket_info["bucket"] = returned_bucket
                            bucket_info["object"] = returned_object
                            break
                    else:
                        print(f"轮询失败，状态码: {response.status}")
            except Exception as e:
                # await context.log_info(f"轮询异常: {str(e)}")
                raise Exception(f"轮询异常: {str(e)}")
            # 等待5秒后进行下一次轮询
            await asyncio.sleep(7)

            # 超时校验
            end_time = time.time()
            interval = end_time - startTime
            if interval > 60 * 10:  # 超过10分钟则退出
                await context.add_view({
                    "format": "card",
                    "content": {
                        "type": 'markdown',
                        "title": '生成考题失败',
                        "details": "模型无响应或响应超时，请稍后再试。",
                    }
                })

                return {
                    "code": 201,
                    "info": "模型无响应或响应超时，请稍后再试。",
                    "IsOver": True
                }

    # await context.log_info(f"返回的bucket={returned_bucket}, object={returned_object}")

    await context.add_view({
        "format": "card",
        "content": {
            "type": "summary_file",
            "title": '考题已生成',
            "details": bucket_info,
            "description": "已经生成考题"
        }
    })
    return {
        "code": 200,
        "info": "考题生成成功",
        "IsOver": True
    }
