import io
import json
import time
import urllib
#import cache
import pandas as pd
import redis
from __runner__ import tool, Context
import requests
import base64
import os
from redis import Redis
from redis.cluster import RedisCluster
from docx.shared import Inches

from io import BytesIO
import re


HEAT_API_HOST = "http://***********:31501"
HEAT_API_HOST1 = "http://***********:31501"
# AUTONOMOUS_SPC_URL = "https://obp-dev.supcon5t.com/operate-optimization-web"  # os.getenv("AUTONOMOUS_OPT_URL")  # 读取网址信息
# RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=data_process_and_data_split_api_online_py&built_in=1&time_out=6000"
# RUNTIME_OPT_EXEC_URL = "http://parameter-identification-tpt.obp:80/call/app?name=data_process_and_data_split_api_online_py&built_in=1&time_out=6000"
# RUNTIME_OPT_PID_EXEC_URL = "http://parameter-identification-tpt.obp:80/call/app?name=spc_process_data_PID_py&built_in=1&time_out=6000"
# RUNTIME_FORECAST_EXEC_URL = "http://parameter-identification-tpt.obp:80/call/app?name=spc_forecast_py&built_in=1&time_out=6000"
# RUNTIME_QUANTATION_EXEC_URL = "http://parameter-identification-tpt.obp:80/call/app?name=spc_quantation_py&built_in=1&time_out=6000"

def get_redis_client(host, port, is_cluster=False, **kwargs):
    if is_cluster:
        #集群模式:只磊一个节点即可自动发现全集群
        return RedisCluster(host=host, port=port, decode_responses=True, **kwargs)
    else:
        # 单鼠模式
        return Redis(host=host, port=port, decode_responses=True, **kwargs)


expires = "604800"
redis_db = 4
redis_password = None

class Cache:
    def __init__(self, redis_host, redis_port, redis_db=redis_db, redis_password=redis_password, expires=int(expires),
                 model="debug"):
        pool = redis.ConnectionPool(host=redis_host, port=redis_port, db=redis_db, password=redis_password)
        self.__redis_client = redis.Redis(connection_pool=pool)
        self.expires = expires


    def save_to_redis(self,file_path):
        """
        将本地文件存到redis中
        """
        if not os.path.exists(file_path):
            raise StorageCustomError(f"{file_path} not exists")

        try:
            # 读取文件并转为二进制
            with open(file_path, 'rb') as file:
                binary_data = file.read()
            filename = os.path.basename(file_path)

            # 将模型数据存储到 Redis
            self.__redis_client.set(filename, binary_data, ex=self.expires)
        except Exception as e:
            raise StorageCustomError(f"save_to_redis failed,error is {repr(e)}")

    def save_stream_to_redis(self, stream:io.BytesIO,filename):
        try:
            # 从 BytesIO 流中读取数据
            binary_data = stream.getvalue()
            # 将模型数据存储到 Redis
            self.__redis_client.set(filename, binary_data, ex=self.expires)
        except Exception as e:
            raise StorageCustomError(f"save_stream_to_redis failed,error is {repr(e)}")


    def get_stream_from_redis(self,filename) -> io.BytesIO:
        """
        从redis中获取key对应的文件流
        """

        if not self.__redis_client.exists(filename):
            raise StorageCustomError(f"{filename} not found in redis")

        try:
            model_bytes = self.__redis_client.get(filename)
            # 使用 io.BytesIO 将数据解流
            model_stream = io.BytesIO(model_bytes)
            model_stream.seek(0)

            return model_stream
        except Exception as e:
            raise StorageCustomError(f"get_stream_from_redis failed,error is {repr(e)}")

    def xadd(self, stream_key, value_dict):
        self.__redis_client.xadd(stream_key, value_dict)

    def get(self, key):
        return self.__redis_client.get(key)

    
    def __enter__(self):
        # 进入上下文时返回自己
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 关闭 Redis 客户端资源
        self.__redis_client.connection_pool.disconnect()
        self.__redis_client.close()


class StorageCustomError(Exception):
    pass


#备注:初始化放在全局，不要放在main方法内部
# ca = cache.CacheTools(redis_host='localhost', redis_port='6379', model="run")
# ca.sync_object("test")


class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_DATAPROCESS_TOPIC = "single_python_train_spc_dataprocess"  # 发送预测算法执行topic
    SPC_DATAPROCESS_TOPIC_RES = "single_python_train_spc_dataprocess_res"  # 预测算法响应topic
    SPC_DATAPROCESS_ALG_NAME = "spc_dataprocess_train"  # 与算法文件名对应
    SPC_DATAPROCESS_ALG_FULL_NAME = "spc_dataprocess_train.py"  # 与算法文件名对应
    SPC_FORECAST_TOPIC = "single_python_train_spc_forecast"  # 发送预测算法执行topic
    SPC_FORECAST_TOPIC_RES = "single_python_train_spc_forecast_res"  # 预测算法响应topic
    SPC_FORECAST_ALG_NAME = "spc_forecast_train"  # 与算法文件名对应
    SPC_FORECAST_ALG_FULL_NAME = "spc_forecast_train.zip"  # 与算法文件名对应
    SPC_DATAPROCESSPID_TOPIC = "single_python_train_spc_dataprocesspid"  # 发送预测算法执行topic
    SPC_DATAPROCESSPID_TOPIC_RES = "single_python_train_spc_dataprocesspid_res"  # 预测算法响应topic
    SPC_DATAPROCESSPID_ALG_NAME = "spc_dataprocess_PID_train"  # 与算法文件名对应
    SPC_DATAPROCESSPID_ALG_FULL_NAME = "spc_dataprocess_PID_train.py"  # 与算法文件名对应
    SPC_QUANTATION_TOPIC = "single_python_train_spc_quantation"  # 发送预测算法执行topic
    SPC_QUANTATION_TOPIC_RES = "single_python_train_spc_quantation_res"  # 预测算法响应topic
    SPC_QUANTATION_ALG_NAME = "spc_quantation_train"  # 与算法文件名对应
    SPC_QUANTATION_ALG_FULL_NAME = "spc_quantation_train.zip"  # 与算法文件名对应

    TASK_DATAPROCESSPID_NAME = "SPC:DATAPROCESSPID"
    TASK_FORECAST_NAME = "SPC:FORECAST"
    TASK_DATAPROCESS_NAME = "SPC:DATAPROCESS"
    TASK_QUANTATION_NAME = "SPC:QUANTATION"


class IntermediateValueError(Exception):
    def __init__(self, message, intermediate_value):
        super().__init__(message)
        self.intermediate_value = intermediate_value


@tool(version="*")
async def spc_csv_upload(context: Context, params: any):
    """理论上id appId target_column等内容需要由GPT给出，目前采用第一个节点写死的逻辑"""
    #appId = '123456'
    parent_path = f'hen_opt/upload'  # TODO
    clientId = str(context.session_id)
    appId = clientId
    formResult = await context.get_interaction("spc_up_from")

    if formResult is None:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "需要确认您的详细需求，请补充信息：",
                "description": "",
                "details": ""
            }
        })
        await context.add_view({
            "format": "markdown",
            "content": "基于TPT的深度分析与智能识别，我已获取了问题的关键信息。为进一步提升最终效果，请对以下关键信息进行确认，以便我们更高效地推进后续工作。"
        })

        context.require_interaction(
            {
                "id": "spc_up_from",
                "title": "请上传位号历史数据文件",
                "type": "form",
                "form": {
                    "schema": {
                        "type": "object",
                        "description": "请上传位号历史数据文件",
                        "properties": {
                            "file": {
                                "title": "位号历史数据文件(.csv)",
                                "type": "string",
                                "format": "file-object",
                                "widget": "tptfile",
                                "validator": "validate_file",
                                "template": [{
                                    'label': "spc_up_test_10w_10s",  # 此处需要与上传的文件名对应，否则无法获取数据
                                    'url': f"{HEAT_API_HOST}/api/file/download?filename='spc_up_test_10w_10s.csv'&file_path={os.path.dirname(parent_path)}"
                                }]
                            }
                        },
                        "required": ["file"]
                    },
                    # "default": {}
                }
            })
        return {}

    
    # file_data = formResult['file'].split(',')[1]  # 分离出base64编码部分
    file_info = json.loads(formResult.get("file"))
    file_data = await context.get_file(file_info)
    # decoded_data = base64.b64decode(file_data)
    # df = pd.read_csv(io.BytesIO(decoded_data), on_bad_lines='skip')
    file_data_id = io.StringIO(file_data)
    df = pd.read_csv(file_data_id)
    await context.add_view({
        "format": "card",
        "content": {
            "type": "string",
            "title": "clientId",
            "details": clientId
        }
    })


    # json_data = df.to_json(orient='records')

    # ca = cache.CacheTools(redis_host='localhost', redis_port='6379', model='run')

    # csv_file_path = os.path.join('0.data', 'spc', f'{appId}', 'prediction', 'spc_raw_data.csv')
    # file_info = await context.add_file(csv_file_path, decoded_data)


    # 获取参数
    # try:
    #     target_column = params['target_column'] if 'target_column' in params.keys() else None
    #     if target_column is None:
    #         select_pv_name = await context.get_interaction("select_pv")
    #         if select_pv_name is None:
    #             # data, 与选项同时返回，方便代码处理选项
    #             context.require_interaction({
    #                 "id": "select_pv",
    #                 "title": "请选择以下哪个变量作为被控变量(PV)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         target_column = select_pv_name[0]["data"] if isinstance(select_pv_name, list) else select_pv_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择target_column报错: {str(e)}, 错误内容:{select_pv_name} ")

    # try:
    #     select_mv_name = await context.get_interaction("select_mv")
    #     if select_mv_name is None:
    #         context.require_interaction({
    #             "id": "select_mv",
    #             "title": "请选择以下哪个变量作为操作变量(MV)",
    #             "type": "select",
    #             "mode": "single",
    #             "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                        enumerate(df.columns)]
    #         })
    #         return {}
    #     control_column = select_mv_name[0]["data"] if isinstance(select_mv_name, list) else select_mv_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择control_column报错: {str(e)}, 错误内容:{select_mv_name} ")

    # try:
    #     mv_pv_correlation_name = await context.get_interaction("select_mv_pv_correlation")
    #     if mv_pv_correlation_name is None:
    #         context.require_interaction({
    #             "id": "select_mv_pv_correlation",
    #             "title": "用户需要给出操作变量与过程变量的正负相关性",
    #             "type": "select",
    #             "mode": "single",
    #             "select": [{"title": f"{tmp_title}", "data": tmp_data} for tmp_title, tmp_data in zip(['操作变量变大，被控变量变大为正', '操作变量变大，被控变量变小为负'], [1, -1])]
    #         })
    #         return {}
    #     mv_pv_correlation = mv_pv_correlation_name[0]["data"] if isinstance(mv_pv_correlation_name, list) else mv_pv_correlation_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择mv_pv_correlation报错: {str(e)}, 错误内容:{mv_pv_correlation} ")
    # if mv_pv_correlation_name is not None:

    json_obj = {
        "file_variables": [{"tag": column_name,"description": "文件位号"} for idx_name, column_name in enumerate(df.columns)]
    }

    address = "/tpt-auto-model/spcControlOptimize"
    result = await context.get_interaction("open_page")
    await context.log_info(f"result = {result}")
    if result is None:
        context.require_interaction({
            "id": "open_page",
            "title": "优化参数信息",
            "type": "open_page",
            "open_page": address,
            "open_type": "execute",
            # 增加页面传参，保持回填数据一致
            "page_data": json_obj
        })
        return {}
        
    else:
        pv_target = result['pv_target']['tag']
        mv_control = result['mv_control']['tag']
        mv_pv_correlation = result['mv_pv_correlation']
        aim_input = result['aim_input']
    target_column = pv_target
    control_column = mv_control
    if result is not None:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "已获取用户上传的数据，开始对数据进行处理",
                "description": "",
                "details": ""
            }
        })
        await context.add_view({
            "format": "markdown",
            "content": "已获取用户上传数据的关键信息，开始对数据进行分析，计算数据得分、缺失值数量，校验上传的数据质量是否满足用户需求。"
        })
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": str(context.session_id)
        },
        {
            "defaultValue": "",
            "name": "target_column",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": target_column  # json
        },
        {
            "defaultValue": "",
            "name": "file_info",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": str(file_info['object'])
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": appId
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_DATAPROCESS_ALG_NAME,
            "sourcePath": Config.SPC_DATAPROCESS_ALG_FULL_NAME,
            "output": output_params
        },
        "id": str(context.session_id),
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_DATAPROCESS_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )
    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_DATAPROCESS_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_DATAPROCESS_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_DATAPROCESS_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_DATAPROCESS_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    # print("算法执行响应结果:" + str(res))
    #print("message :", message)
    ##print("result :", result.keys())
    await context.log_info(f"res = {res}")

    await context.add_view("""
        | Key      | Value        |
        |----------|--------------|
        | Tag      | {tag} |
        | Score    | {score} |
        | Max NaN  | {max_nan} |
        """.format(
        tag=result['tag'],
        score=result['score'],
        max_nan=result['max_nan']
    ))

    await context.set_cache('target_column',target_column)
    await context.set_cache('control_column',control_column)

    return {"pv_target": target_column,
            "mv_control": control_column,
            "data_score": result['score'],
            "data_num_nan": str(result['max_nan']),
            "data_tag": str(result['tag']),
            "mv_pv_correlation": int(mv_pv_correlation[0]),
            "aim_input": aim_input}
            


@tool(version="*")
async def spc_PID_csv_upload(context: Context, params: any):
    """理论上id appId target_column等内容需要由GPT给出，目前采用第一个节点写死的逻辑"""
    clientId = context.session_id
    appId = '123456'
    parent_path = f'hen_opt/upload'  # TODO

    formResult = await context.get_interaction("spc_up_from")
    if formResult is None:
        await context.add_view({
            "format": "tip",
            "content": {
                "type": 'default',
                "title": '',
                "content": "需要确认您的详细需求，请补充信息：",
                "description": "",
                "details": ""
            }
        })
        await context.add_view({
            "format": "markdown",
            "content": "基于TPT的深度分析与智能识别，我已获取了问题的关键信息。为进一步提升最终效果，请对以下关键信息进行确认，以便我们更高效地推进后续工作。"
        })
        context.require_interaction(
            {
                "id": "spc_up_from",
                "title": "请上传位号历史数据文件",
                "type": "form",
                "form": {
                    "schema": {
                        "type": "object",
                        "description": "请上传位号历史数据文件",
                        "properties": {
                            "file": {
                                "title": "位号历史数据文件(.csv)",
                                "type": "string",
                                "format": "file-object",
                                "widget": "tptfile",
                                "validator": "validate_file",
                                "template": [{
                                    'label': "spc_test_data_with_dates",  # 此处需要与上传的文件名对应，否则无法获取数据
                                    'url': f"{HEAT_API_HOST}/api/file/download?filename='spc_test_data_with_dates.csv'&file_path={os.path.dirname(parent_path)}"
                                }]
                            }
                        },
                        "required": ["file"]
                    },
                    "default": {}
                }
            })
        return {}
    try:
        file_info = json.loads(formResult.get("file"))
        file_data = await context.get_file(file_info)
        file_data_id = io.StringIO(file_data)
        df = pd.read_csv(file_data_id)
    except Exception as e:
        raise RuntimeError(f"file_data解析失败: {str(e)} df's problem !")
    # 判断数据长度是否少于20分钟
    if 'Timestamp' in df.columns:
        df['Timestamp'] = pd.to_datetime(df['Timestamp'])
        time_length = (df['Timestamp'].iloc[-1] - df['Timestamp'].iloc[0]).total_seconds() / 60
        time_gap = (df['Timestamp'].iloc[1] - df['Timestamp'].iloc[0]).total_seconds()
    else:
        Timestamp = params['Timestamp'] if 'Timestamp' in params.keys() else None
        if Timestamp is None:
            Timestamp_name = await context.get_interaction("select_time")
            if Timestamp_name is None:
                context.require_interaction({
                    "id": "select_time",
                    "title": "请选择以下哪个变量作为时间戳(Timestamp)",
                    "type": "select",
                    "mode": "single",
                    "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
                               enumerate(df.columns)]
                })
                return {}
            Timestamp = Timestamp_name[0]["data"] if isinstance(Timestamp_name, list) else Timestamp_name["data"][0]
        df[Timestamp] = pd.to_datetime(df[Timestamp])
        time_length = (df['Timestamp'].iloc[-1] - df['Timestamp'].iloc[0]).total_seconds() / 60
        time_gap = (df['Timestamp'].iloc[1] - df['Timestamp'].iloc[0]).total_seconds()
    if time_length < 20:
        short_time_length = '数据时长短于20分钟,需要补充。'
        timestamp_length_tag = 1
    else:
        short_time_length = '数据时长已经满足条件。'
        timestamp_length_tag = 0
    if time_gap > 10:
        short_time_length += '数据采样时间间隔过长，PV信息密度稀疏，可能对效果造成影响'
        timestamp_gap_tag = 0
    else:
        short_time_length += '数据采样时间间隔已经满足条件'
        timestamp_gap_tag = 1

    # try:
    #     PV = params['PV'] if 'PV' in params.keys() else None
    #     if PV is None:
    #         PV_name = await context.get_interaction("select_pv")
    #         if PV_name is None:
    #             context.require_interaction({
    #                 "id": "select_pv",
    #                 "title": "请选择以下哪个变量作为被控变量(PV)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         PV = PV_name[0]["data"] if isinstance(PV_name, list) else PV_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择PV报错: {str(e)}, 错误内容:{PV} ")

    # try:
    #     MV = params['MV'] if 'MV' in params.keys() else None
    #     if MV is None:
    #         MV_name = await context.get_interaction("select_mv")
    #         if MV_name is None:
    #             context.require_interaction({
    #                 "id": "select_mv",
    #                 "title": "请选择以下哪个变量作为操作变量(MV)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         MV = MV_name[0]["data"] if isinstance(MV_name, list) else MV_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择MV报错: {str(e)}, 错误内容:{MV} ")

    # try:
    #     SV = params['SV'] if 'SV' in params.keys() else None
    #     if SV is None:
    #         SV_name = await context.get_interaction("select_sv")
    #         if SV_name is None:
    #             context.require_interaction({
    #                 "id": "select_sv",
    #                 "title": "请选择以下哪个变量作为目标设定值(SV)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         SV = SV_name[0]["data"] if isinstance(SV_name, list) else SV_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择SV报错: {str(e)}, 错误内容:{SV} ")

    # try:
    #     PB = params['PB'] if 'PB' in params.keys() else None
    #     if PB is None:
    #         PB_name = await context.get_interaction("select_pb")
    #         if PB_name is None:
    #             context.require_interaction({
    #                 "id": "select_pb",
    #                 "title": "请选择以下哪个变量作为比例度(PB)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         PB = PB_name[0]["data"] if isinstance(PB_name, list) else PB_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择PB报错: {str(e)}, 错误内容:{PB} ")

    # try:
    #     Ti = params['Ti'] if 'Ti' in params.keys() else None
    #     if Ti is None:
    #         Ti_name = await context.get_interaction("select_ti")
    #         if Ti_name is None:
    #             context.require_interaction({
    #                 "id": "select_ti",
    #                 "title": "请选择以下哪个变量作为积分时间常数(Ti)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         Ti = Ti_name[0]["data"] if isinstance(Ti_name, list) else Ti_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择Ti报错: {str(e)}, 错误内容:{Ti} ")

    # try:
    #     Td = params['Td'] if 'Td' in params.keys() else None
    #     if Td is None:
    #         Td_name = await context.get_interaction("select_td")
    #         if Td_name is None:
    #             context.require_interaction({
    #                 "id": "select_td",
    #                 "title": "请选择以下哪个变量作为微分时间常数(Td)",
    #                 "type": "select",
    #                 "mode": "single",
    #                 "select": [{"title": f"{column_name}", "data": column_name} for idx_name, column_name in
    #                            enumerate(df.columns)]
    #             })
    #             return {}
    #         Td = Td_name[0]["data"] if isinstance(Td_name, list) else Td_name["data"][0]
    # except Exception as e:
    #     raise RuntimeError(f"选择Td报错: {str(e)}, 错误内容:{Td} ")

    # params = await context.get_cache("opt_offline_file_upload_result")

    # address = "tpt-auto-model/pidSelfTune"
    # context.require_interaction({
    #         "id": "open_opt_param_page",
    #         "title": "优化参数信息",
    #         "type": "open_page",
    #         "open_page": address,
    #         "page_type": "execute",
    #         # 增加页面传参，保持回填数据一致
    #         "page_data": params

    #     })

    json_obj = {
        "file_variables": [{"tag": column_name,"description": "文件位号"} for idx_name, column_name in enumerate(df.columns)]
    }

    
    address = "/tpt-auto-model/pidSelfTune"
    result = await context.get_interaction("open_page")
    await context.log_info(f"result = {result}")
    if result is None:
        context.require_interaction({
            "id": "open_page",
            "title": "优化参数信息",
            "type": "open_page",
            "open_page": address,
            "page_type": "execute",
            # 增加页面传参，保持回填数据一致
            "page_data": json_obj
        })
        return {}
        
    else:
        PV = result['pv']['tag']
        MV = result['mv']['tag']
        SV = result['sv']['tag']
        PB = result['pb']['tag']
        Ti = result['ti']['tag']
        Td = result['td']['tag']
        loop_type = result['loop_type']
        # return {
        #     "output": result
        # }



    await context.add_view({
        "format": "card",
        "content": {
            "type": "string",
            "title": "clientId",
            "details": clientId
        }
    })
    await context.add_view({
        "format": "card",
        "content": {
            "type": "string",
            "title": "file_info",
            "details": str(file_info['object'])
        }
    })

    #if Td_name is not None:
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "已获取用户上传的数据，开始对数据进行处理",
            "description": "",
            "details": ""
        }
    })
    await context.add_view({
        "format": "markdown",
        "content": "已获取用户上传数据的关键信息，开始对数据进行分析，计算数据得分、缺失值数量，校验上传的数据质量是否满足用户需求。"
    })
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "target_column",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": PV  # json
        },
        {
            "defaultValue": "",
            "name": "file_info",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": str(file_info['object'])
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": appId
        },
        {
            "defaultValue": "",
            "name": "SV_name",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": SV
        },
        {
            "defaultValue": "",
            "name": "MV_name",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": MV
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_DATAPROCESSPID_ALG_NAME,
            "sourcePath": Config.SPC_DATAPROCESSPID_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }

    print(input_params)

    # r = Cache('seak8sm2.supcon5t.com', '30380')
    # # 推送数据到 Redis Stream 中
    # stream_key = "runtime_python_stream:" + Config.SPC_DATAPROCESSPID_TOPIC
    # value = {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)}
    # r.__redis_client.xadd(stream_key, value)


    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    # r = get_redis_client(context.config["redis_host"], context.config["redis_port"], is_cluster = context.config["redis_cluster"], redis_db=context.config["redis_db"])
    #redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    #获取对应集群的redis地址
    # redis_host = ca.configs.get("redis", "host")
    # redis_port = ca.configs.get("redis", "port")
    # redis_db = ca.configs.get("redis","db")
    # r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
    

    r.xadd(
        "runtime_python_stream:" + Config.SPC_DATAPROCESSPID_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_DATAPROCESSPID_NAME}:" + clientId)
        # 从 Redis 中获取数据
        # redis_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_DATAPROCESSPID_NAME}:" + clientId
        # execute_res = r.__redis_client.get(redis_key)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_DATAPROCESSPID_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")

        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            #json_array = json.loads(execute_res) # 已经是str类型，不需要解码
            await context.log_info(f"json_array = {json_array}")
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_DATAPROCESSPID_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_DATAPROCESSPID_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    await context.log_info(f"result = {result}")
    markdown_table = json.loads(result['return_md'])["markdown_table"]
    score = json.loads(result['return_md'])["score"]
    # 展示结果图片
    await context.add_view(markdown_table)

    return {'markdown_table': markdown_table
            , 'data_score': float(score)
            , 'PV': PV
            , 'MV': MV
            , 'SV': SV
            , 'PB': PB
            , 'Ti': Ti
            , 'Td': Td
            , 'short_time_length': short_time_length
            , 'timestamp_gap_tag': timestamp_gap_tag
            , 'timestamp_length_tag': timestamp_length_tag
            , 'loop_type': loop_type
            }



@tool(version="*")
async def spc_forecast(context: Context, params: any):
    """理论上id appId target_column等内容需要由GPT给出，目前采用第一个节点写死的逻辑"""
    samples_count_score = params['samples_count_score']  # 50
    clientId = str(context.session_id)
    cur_time = str(int(time.time() * 1000))
    id_ = '123456'
    appId = '123456'
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "开始预测模型构建，建立具备工况差异感知能力的统一预测体系：",
            "description": "",
            "details": ""
        }
    })
    await context.add_view({
        "format": "markdown",
        "content": "通过引入自适应建模机制，系统能够依据数据特征自动匹配并优化最适配的算法框架，从而建立具备工况差异感知能力的统一预测体系。该体系可在实际生产运营中动态响应复杂工况变化，为精准预测提供可靠依据。"
    })

    # plt_begin_time = await context.get_interaction("plt_begin_time_from")
    # if plt_begin_time is None:
    #     context.require_interaction({
    #         "id": "plt_begin_time_from",
    #         "title": "预测结果绘图展示时开始时间",
    #         "type": "form",
    #         "form": {
    #             "schema": {
    #                 "type": "object",
    #                 "description": "",
    #                 "properties": {
    #                     "plt_begin_time": {
    #                         "type": "string",
    #                         "title": "预测图展示的开始时间",
    #                         "description": "默认为全部展示,可选: 2024-06-08 17:00:00",
    #                     },
    #                 },
    #                 "required": [
    #                     "plt_begin_time"
    #                 ]
    #             }
    #         }
    #     })
    #     return {}

    # plt_end_time = await context.get_interaction("plt_end_time_from")
    # if plt_end_time is None:
    #     context.require_interaction({
    #         "id": "plt_end_time_from",
    #         "title": "预测结果绘图展示时结束时间",
    #         "type": "form",
    #         "form": {
    #             "schema": {
    #                 "type": "object",
    #                 "description": "",
    #                 "properties": {
    #                     "plt_end_time": {
    #                         "type": "string",
    #                         "title": "预测图展示的结束时间",
    #                         "description": "默认为全部展示,可选: 2024-06-08 21:00:00",
    #                     },
    #                 },
    #                 "required": [
    #                     "plt_end_time"
    #                 ]
    #             }
    #         }
    #     })
    #     return {}

    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        # {
        #     "defaultValue": "",
        #     "name": "plt_begin_time",
        #     "type": 1,
        #     "typeName": "str",  # list
        #     "userInput": 1,
        #     "value": str(plt_begin_time["plt_begin_time"])  # json
        # },
        # {
        #     "defaultValue": "",
        #     "name": "plt_end_time",
        #     "type": 1,
        #     "typeName": "str",
        #     "userInput": 1,
        #     "value": str(plt_end_time["plt_end_time"])
        # },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": appId
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_FORECAST_ALG_NAME,
            "sourcePath": Config.SPC_FORECAST_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_FORECAST_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )
    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_FORECAST_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_FORECAST_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_FORECAST_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_FORECAST_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    await context.log_info(f"res = {res}")

    # await context.add_view({
    #     "format": "card",
    #     "content": {
    #         "type": "html",
    #         "title": "预测曲线",
    #         "details": result['fig_html']
    #     }
    # })
    #
    md_test = """
        | Key      | Value        |
        |----------|--------------|
        | MSE      | {tag}        |
        """.format(
        tag=round(result['mse'], 4))

    await context.add_view({
        "format": "card",
        "content": {
            "type": "string",
            "title": "预测小结",
            "details": md_test
        }
    })
    if float(result['mse']) < 10:
        forecast_result_reson = '模型预测曲线的MSE低,说明模型可以用于超级控制'
    else:
        forecast_result_reson = '模型预测曲线的MSE较高,模型未从数据学习到物理/化学规律.'
        if samples_count_score <= 50:
            forecast_result_reson += '分析原因为样本量太少,建议补充更多数据'

    return {"mse": float(result['mse']),
            #"plt_end_time": plt_end_time["plt_end_time"],
            #"plt_begin_time": plt_begin_time["plt_begin_time"],
            "forecast_result": True if result['acc'] > 0.8 else False,
            "forecast_result_reson": forecast_result_reson}



@tool(version="*")
async def spc_quantation(context: Context, params: any):
    """理论上id appId target_column等内容需要由GPT给出，目前采用第一个节点写死的逻辑"""
    clientId = str(context.session_id)
    cur_time = str(int(time.time() * 1000))
    appId = '123456'
    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "开始模型轻量化加速，对构建的预测模型进行轻量化，降低模型存储需求和计算复杂度：",
            "description": "",
            "details": ""
        }
    })
    await context.add_view({
        "format": "markdown",
        "content": "这个环节将对构建的预测模型进行轻量化加速，减少模型的存储空间和计算量，在保持足够预测精度的前提下，满足实际应用场景对模型大小和推理速度的要求，从而实现模型在资源受限设备上的高效部署与快速推理。"
    })
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": appId
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_QUANTATION_ALG_NAME,
            "sourcePath": Config.SPC_QUANTATION_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_QUANTATION_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_QUANTATION_NAME}:" + clientId)
        # print('execute_res: ', Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_QUANTATION_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_QUANTATION_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_QUANTATION_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_QUANTATION_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    await context.log_info(f"result = {result}")
    return {'is_require_quantation': '可以使用提升速度后的模型' if result['mse_down'] < 8 else f"不建议使用提升速度后的模型,因为loss提高了{result['mse_down']}%",
            'speed_up': str(result['speed_up'] / 100),
            'mse_down': str((result['mse_down'])),
            }

