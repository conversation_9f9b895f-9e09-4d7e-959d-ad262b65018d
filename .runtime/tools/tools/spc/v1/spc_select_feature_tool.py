import io
import json
import urllib

from __runner__ import tool, Context
import requests
import base64
import os
import time
import redis
from docx import Document


HEAT_API_HOST = "http://***********:31501"
# AUTONOMOUS_SPC_URL = "https://obp-dev.supcon5t.com/operate-optimization-web"  # os.getenv("AUTONOMOUS_OPT_URL")  # 读取网址信息
RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_select_feature_py&built_in=1&time_out=6000"
class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_SELECT_FEATURE_TOPIC = "single_python_train_spc_select_feature"  # 发送预测算法执行topic
    SPC_SELECT_FEATURE_TOPIC_RES = "single_python_train_spc_select_feature_res"  # 预测算法响应topic
    SPC_SELECT_FEATURE_ALG_NAME = "spc_select_feature_train"  # 与算法文件名对应
    SPC_SELECT_FEATURE_ALG_FULL_NAME = "spc_select_feature_train.zip"  # 与算法文件名对应
    TASK_NAME = "SPC:SELECT:FEATURE"

@tool(version="*")
async def select_feature(context: Context, params: any):
    clientId = context.session_id
    appId='123456'
    pv_target=params.get('pv_target')
    mv_control=params.get('mv_control')
    time_interval=10
    mv_pv_correlation=params.get('mv_pv_correlation')

    await context.add_view({
        "format": "tip",
        "content": {
            "type": 'default',
            "title": '',
            "content": "开始关键变量挖掘，从用户上传数据中提取对分析目标有用的重要位号：",
            "description": "",
            "details": ""
        }
    })
    await context.add_view({
        "format": "markdown",
        "content": "这个环节将对数据中的关键变量进行挖掘，从用户上传数据的众多位号特征中挑选出对分析目标有用的关键特征，以提升模型性能、降低计算成本，增强结果可解释性。"
    })


    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": appId
        },
        {
            "defaultValue": "",
            "name": "pv_target",
            "type": 1,
            "typeName": "str",  # list
            "userInput": 1,
            "value": pv_target  # json
        },
        {
            "defaultValue": "",
            "name": "mv_control",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": mv_control
        },
        {
            "defaultValue": "",
            "name": "time_interval",
            "type": 1,
            "typeName": "float",
            "userInput": 1,
            "value": str(time_interval)
        },
        {
            "defaultValue": "",
            "name": "mv_pv_correlation",
            "type": 1,
            "typeName": "float",
            "userInput": 1,
            "value": str(mv_pv_correlation)
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_SELECT_FEATURE_ALG_NAME,
            "sourcePath": Config.SPC_SELECT_FEATURE_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_SELECT_FEATURE_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_SELECT_FEATURE_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_SELECT_FEATURE_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    #print("算法执行响应结果:" + str(res))
    await context.log_info(f"res = {res}")
    
    #await context.add_view(result['markdown_output'])
    prompt_str = f"""
# 角色
你是一位专业的数据挖掘特征筛选评估师，擅长对各类数据中的特征进行深入分析和评估，能够根据数据中特征的重要度，精准判断数据特征的重要信息，并给出针对性的总结和建议。
### 技能 1：特征筛选结果评估
1.接收输入的类似以下格式的数据
{result['markdown_output']}
2.依据feature，importance字段，给出针对性总结。
### 技能 2：输出总结文字
根据特征筛选评估结果，输出一段总结，清晰描述数据挖掘特征筛选的整体状况。例如：关键变量挖掘结果显示：从34个候选变量中筛选出与pH控制相关的关键变量，其中控制位号HGLZM01.AIC2104.PIDA.OP的重要性最高（0.0584），其次是液位变量HGLZM01.LI2110.DACA.PV（0.0572）和HGLZM01.LIC2104.DACA.PV（0.0571）。所有特征重要性均超过0.001阈值，满足模型训练所需的位号数量条件。特别值得注意的是，前8个关键变量的重要性均保持在0.04以上，表明这些过程变量对pH控制具有显著影响。
## 限制：
- 仅围绕数据挖掘特征筛选评估相关内容，进行分析和总结，拒绝回答无关话题。
- 总结需逻辑清晰、简洁明了，全面反映特征筛选状况。 
/no_think
"""
    #调用LLM润色
    prompt_str_obj = {
        "prompt_str" :prompt_str
    }
    llm_resp_obj = await context.call_tool("get_llm_summary",context = context,params=prompt_str_obj,module_name="automl")
    if llm_resp_obj["success"] == 1:
        llm_resp = llm_resp_obj["llm_resp"]
    else:
        llm_resp = ""

    # def text_to_docx(text):
    #     doc = Document()
    #     doc.add_heading('输出报告', level=1)
    #     doc.add_paragraph(text)
    #     buffer = io.BytesIO()
    #     doc.save(buffer)
    #     return buffer.getvalue()
    # docx_binary = text_to_docx(llm_resp)

    def text_to_docx(text, original_data):
        doc = Document()
        
        # 添加报告标题
        doc.add_heading('关键变量挖掘报告', level=1)
        
        # 添加分析总结部分
        doc.add_heading('分析总结', level=2)
        doc.add_paragraph(text)
        
        # 添加原始数据部分
        doc.add_heading('原始特征筛选结果', level=2)
        
        # 将markdown表格转换为docx表格
        table_data = []
        lines = original_data.strip().split('\n')
        headers = [h.strip() for h in lines[0].split('|') if h.strip()]
        table_data.append(headers)
        
        for line in lines[2:]:
            if line.strip():
                row = [c.strip() for c in line.split('|') if c.strip()]
                table_data.append(row)
        
        table = doc.add_table(rows=1, cols=len(headers))
        table.style = 'Table Grid'
        
        # 添加表头
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            hdr_cells[i].text = header
        
        # 添加数据行
        for row in table_data[1:]:
            row_cells = table.add_row().cells
            for i, cell in enumerate(row):
                row_cells[i].text = cell
        
        buffer = io.BytesIO()
        doc.save(buffer)
        return buffer.getvalue()
    docx_binary = text_to_docx(llm_resp, result['markdown_output'])
    
    object_name = f"reports/spc/{int(time.time())}.docx"
    file_info = await context.add_file(f"{object_name}", docx_binary)
    minio_obj = {
        "bucket":file_info['bucket'],
        "object":file_info['object']
    }
    await context.add_view({
        "format": "card",
        "content": {
            "type": 'summary_file',
            "title": '关键变量挖掘报告',
            "details": minio_obj,
            "description": "输出报告.docx"
        }
    })

    # await context.add_view({
    #         "format":"card",
    #         "content": {
    #             "type": "string",
    #             "title": "特征筛选结果",
    #             "details": result['markdown_output'] + llm_resp
    #         }
    #     })

    return {'markdown_output':result['markdown_output'],
            'feature_num':result['feature_num'],
            'feature_num_check':result['feature_num_check']}

