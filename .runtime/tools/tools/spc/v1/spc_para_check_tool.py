'''
TPT调用能力的api
'''
import io
import json
import urllib
from __runner__ import tool, Context
import requests
import base64
import os
import time
import redis
from redis import Redis
from redis.cluster import RedisCluster

def get_redis_client(host, port, is_cluster=False, **kwargs):
    if is_cluster:
        #集群模式:只磊一个节点即可自动发现全集群
        return RedisCluster(host=host, port=port, decode_responses=True, **kwargs)
    else:
        # 单鼠模式
        return Redis(host=host, port=port, decode_responses=True, **kwargs)

HEAT_API_HOST = "http://***********:31501"
RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_para_check_py&built_in=1&time_out=6000"


class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_FORECAST_TOPIC = "single_python_train_spc_para_check"  # 发送预测算法执行topic
    SPC_FORECAST_TOPIC_RES = "single_python_train_spc_para_check_res"  # 预测算法响应topic
    SPC_FORECAST_ALG_NAME = "spc_para_check_train"  # 与算法文件名对应
    SPC_FORECAST_ALG_FULL_NAME = "spc_para_check_train.py"  # 与算法文件名对应
    TASK_NAME = "SPC:PIDCHECK"


@tool(version="*")
async def spc_para_check_tool(context: Context, params: any):
    clientId = context.session_id
    # if not params.get('loop_type'):
    #     raise ValueError('loop type is required')
    # --------- 先判断所需要的参数是否都获取到了 ------- #
    await context.log_info(f"params = {params}")
    try:
        lalala = {
            'in_params' : {
                "id": "123456",
                "appId": "123456",
                'loop_type': params['loop_type']
            }
        }
    except Exception as e:
        raise RuntimeError(f"获取params失败: {str(e)}")

    if params['loop_type'] == '1':
        lalala['in_params']['loop_type'] = '流量'
    elif params['loop_type'] == '2':
        lalala['in_params']['loop_type'] = '压力'
    elif params['loop_type'] == '3':
        lalala['in_params']['loop_type'] = '温度'
    elif params['loop_type'] == '4':
        lalala['in_params']['loop_type'] = '液位'
    
    # try:
    #     #if not params.get('end_time'):
    #     loop_type_name = await context.get_interaction("loop_type_from")
    #     if loop_type_name is None:
    #         context.require_interaction({
    #             "id": "loop_type_from",  # 此处必须要与上面的get_interaction("target_column_from")
    #             # 内容一致，否则会处于交互界面
    #             "title": "TPT没有识别到，请重新指定待整定回路的回路类型",
    #             "type": "form",
    #             "form": {
    #                 "schema": {
    #                     "type": "object",
    #                     "description": "",
    #                     "properties": {
    #                         "loop_type_name": {
    #                             "type": "string",
    #                             "title": "回路类型",
    #                             "description": "请指定待整定回路的回路类型，如：流量，压力，液位",
    #                         },
    #                     },
    #                     "required": ["loop_type_name"]
    #                 }
    #             }
    #         })
    #         return {}

    #     lalala['in_params']['loop_type'] = loop_type_name['loop_type_name']
    #     # in_params['id'] = '123456'
    #     # in_params["appId"] = "123456"
    #     # in_params['start_time']= '2023/12/31 5:30:05'


    # except Exception as e:
    #     raise RuntimeError(f"回路类型新获取失败: {str(e)}")




    # ----------------- 调用能力主函数 -------------- #
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "loop_type",
            "type": 1,
            "typeName": "str",  # list
            "userInput": 1,
            "value": lalala['in_params']['loop_type']  # json
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "markdown_table",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_FORECAST_ALG_NAME,
            "sourcePath": Config.SPC_FORECAST_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    # r = get_redis_client(context.config["redis_host"], context.config["redis_port"], is_cluster = context.config["redis_cluster"], redis_db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_FORECAST_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            #json_array = json.loads(execute_res) # 已经是str类型，不需要解码
            await context.log_info(f"json_array = {json_array}")

            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_FORECAST_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_FORECAST_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    await context.log_info(f"result = {result}")
    # 展示结果图片
    await context.add_view(result['markdown_table'])
#     await context.add_view("""
#     | Key      | Value        | Key      | Value        |
#     |----------|--------------|----------|--------------|
#     | old_PB   | {old_PB}     | new_PB   | {new_PB}     |
#     | old_Ti   | {old_Ti}     | new_Ti   | {new_Ti}     |
#     | old_Td   | {old_Td}     | new_Td   | {new_Td}     |
#     |check_answer|{check_answer}|        |              |
#     """.format(
#     old_PB=data_dict['old_PB'],
#     old_Ti=data_dict['old_Ti'],
#     old_Td=data_dict['old_Td'],
#     new_PB=data_dict['new_PB'],
#     new_Ti=data_dict['new_Ti'],
#     new_Td=data_dict['new_Td'],
#     check_answer=data_dict['check_answer']
# ))




    return {'markdown_table' : result['markdown_table']}


