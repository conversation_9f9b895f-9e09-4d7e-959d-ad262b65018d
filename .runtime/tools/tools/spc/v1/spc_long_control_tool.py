# -*- coding: utf-8 -*-
"""
Created on Mon May 26 20:08:51 2025

@author: ma<PERSON><PERSON><PERSON>
"""


import io
import json
import time
import urllib

import pandas as pd
import redis
from __runner__ import tool, Context
import requests
import base64
import os
# from redis import Redis
# from redis.cluster import RedisCluster

# def get_redis_client(host, port, is_cluster=False, **kwargs):
#     if is_cluster:
#         #集群模式:只一个节点即可自动发现全集群
#         return RedisCluster(host=host, port=port, decode_responses=True, **kwargs)
#     else:
#         # 单机模式
#         return Redis(host=host, port=port, decode_responses=True, **kwargs)


class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 30000  # 算法执行超时时间

    # changed
    SPC_CONTROL_TOPIC = "single_python_train_spc_long_control"  # 发送控制算法执行topic
    SPC_CONTROL_TOPIC_RES = "single_python_train_spc_long_control_res"  # 控制算法响应topic
    SPC_CONTROL_ALG_NAME = "spc_long_control_train"  # 与算法文件名对应
    SPC_CONTROL_ALG_FULL_NAME = "spc_long_control_train.zip"  # 与算法文件名对应
    TASK_NAME = "SPC:CONTROL"

HEAT_API_HOST = "http://***********:31501"
RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_long_control_api_py&built_in=1&time_out=6000"

@tool(version="*")
async def spc_long_control_tool(context: Context, params: any):
    clientId = str(context.session_id)
    try:
        # 构建请求数据，仅包含必要参数
        data = {
            'id': '123456',
            'appId': '123456',
            # "aim_input": 76,   # 控制目标（如"大罐液位控制"）
            # "mv_pv_correlation": -1,     # MV与PV相关性: "positive"或"negative"
            'mv_pv_correlation': params['mv_pv_correlation'],
            # "plt_begin_time": "2024-11-06 11:57:20",   # 数据展示起始时间（ISO 8601格式）
            # "plt_end_time": "2024-11-06 14:07:20"     # 数据展示结束时间（ISO 8601格式）
        }
    except Exception as e:
        raise RuntimeError(f"获取params失败: {str(e)}")

    # data['plt_begin_time'] = params['plt_begin_time'].replace('T', ' ')
    # data['plt_end_time'] = params['plt_end_time'].replace('T', ' ')

    try:
        aim_input_dict = await context.get_interaction("aim_input_from")
        if aim_input_dict is None:
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": "需要确认您的详细需求，请补充信息：",
                    "description": "",
                    "details": ""
                }
            })
            await context.add_view({
                "format": "markdown",
                "content": "基于TPT的深度分析与智能识别，我已获取了问题的关键信息。为进一步提升最终效果，请对以下关键信息进行确认，以便我们更高效地推进后续工作。"
            })

            context.require_interaction({
                "id": "aim_input_from",
                "title": "TPT没有识别到控制设置值,请重新指定该控制值(Float)",
                "type": "form",
                "form": {
                    "schema": {
                        "type": "object",
                        "description": " ",
                        "properties": {
                            "aim_input": {
                                "type": "string",
                                "title": "控制目标设定值",
                                "description": "请指定本次超级控制的目标设定值",
                            },
                        },
                        "required": [
                            "aim_input"
                        ]
                    }
                }
            })
            return {}
        if aim_input_dict is not None:
            await context.add_view({
                "format": "tip",
                "content": {
                    "type": 'default',
                    "title": '',
                    "content": "已获取用户补充信息，开始进行AI智能控制：",
                    "description": "",
                    "details": ""
                }
            })
            await context.add_view({
                "format": "markdown",
                "content": "这个环节将结合预测模型对目标变量未来数据的预测结果，通过预设的控制算法实现AI控制，以确保系统能够根据预测趋势自动调整设备运行参数，达成预期的性能指标。"
            })
        data['aim_input'] = float(aim_input_dict['aim_input'])
    except Exception as e:
        raise RuntimeError(f"aim_input重新获取失败: {str(e)}")
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        # {
        #     "defaultValue": "",
        #     "name": "plt_begin_time",
        #     "type": 1,
        #     "typeName": "str",  # list
        #     "userInput": 1,
        #     "value": params['plt_begin_time'].replace('T', ' ')  # json
        # },
        # {
        #     "defaultValue": "",
        #     "name": "plt_end_time",
        #     "type": 1,
        #     "typeName": "str",
        #     "userInput": 1,
        #     "value": params['plt_end_time'].replace('T', ' ')
        # },
        {
            "defaultValue": "",
            "name": "appId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": '123456'
        },
        {
            "defaultValue": "",
            "name": "aim_input",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": float(aim_input_dict['aim_input'])
        },
        {
            "defaultValue": "",
            "name": "mv_pv_correlation",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": params['mv_pv_correlation']
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_CONTROL_ALG_NAME,
            "sourcePath": Config.SPC_CONTROL_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    # r = get_redis_client(context.config["redis_host"], context.config["redis_port"], is_cluster = context.config["redis_cluster"], redis_db=context.config["redis_db"])

    r.xadd(
        "runtime_python_stream:" + Config.SPC_CONTROL_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        #print('execute_res: ', Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            # json_array = json.loads(execute_res)
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_CONTROL_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_CONTROL_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break
    # finally:
    #     if r is not None:
    #         r.close()
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    await context.log_info(f"res = {res}")

    await context.add_view({
        "format": "card",
        "content": {
            "type": "html",
            "title": "单步控制结果",
            "details": result['img_html']
        }
    })

    return {'is_reasonable': {'magnitude_valid': bool(result['magnitude_valid']), 'direction_valid': str(result['direction_valid']), 'd_pv_std': str(result['d_history_pv_std_value'])}}
