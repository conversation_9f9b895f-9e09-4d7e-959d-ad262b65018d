'''
TPT调用能力的api
'''
import io
import json
import urllib
from __runner__ import tool, Context
import requests
import base64
import os
import time
import redis
from redis import Redis
from redis.cluster import RedisCluster


def get_redis_client(host, port, is_cluster=False, **kwargs):
    if is_cluster:
        #集群模式:只磊一个节点即可自动发现全集群
        return RedisCluster(host=host, port=port, decode_responses=True, **kwargs)
    else:
        # 单鼠模式
        return Redis(host=host, port=port, decode_responses=True, **kwargs)


HEAT_API_HOST = "http://***********:31501"
RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_PID_auto_tune_py&built_in=1&time_out=6000"


# Config
class Config:
    # fixed
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    TIME_OUT = 3000  # 算法执行超时时间

    # changed
    SPC_FORECAST_TOPIC = "single_python_train_spc_PID_auto_tune"  # 发送预测算法执行topic
    SPC_FORECAST_TOPIC_RES = "single_python_train_spc_PID_auto_tune_res"  # 预测算法响应topic
    SPC_FORECAST_ALG_NAME = "spc_PID_auto_tune_train"  # 与算法文件名对应
    SPC_FORECAST_ALG_FULL_NAME = "spc_PID_auto_tune_train.py"  # 与算法文件名对应
    TASK_NAME = "SPC:PIDAUTOTUNE"


@tool(version="*")
async def spc_PID_auto_tune_tool(context: Context, params: any):
    clientId = context.session_id
    # --------- 先判断所需要的参数是否都获取到了 ------- #
    try:
        lalala = {
            'in_params': {
                "id": "123456",
                "appId": "123456",
                # 'PV': 'PV',
                # 'MV': 'MV',
                # 'SV': 'SV',
                # 'PB': 'PB',
                # 'Ti': 'Ti',
                # 'Td': 'Td',
                'start_time': '',
                # '2023/12/31 5:30:05', #'2024/01/01 7:30:00',   #'2024/01/04 12:00:02',  # '2024/01/04 12:00:02''2023/12/31
                'end_time': '' #'2023/12/31 6:30:05'# '2024/01/01 8:30:00' # '2024/01/04 14:00:02' #'2023/12/31 02:48:05'
            }
        }
    except Exception as e:
        raise RuntimeError(f"获取params失败: {str(e)}")

    try:
        if params.get('PV'):
            lalala['in_params']['PV'] = params.get('PV')
    except Exception as e:
        raise RuntimeError(f"PV获取失败: {str(e)}")

    try:
        if params.get('SV'):
            lalala['in_params']['SV'] = params.get('SV')
    except Exception as e:
        raise RuntimeError(f"SV获取失败: {str(e)}")

    try:
        if params.get('MV'):
            lalala['in_params']['MV'] = params.get('MV')
    except Exception as e:
        raise RuntimeError(f"MV获取失败: {str(e)}")

    try:
        if params.get('PB'):
            lalala['in_params']['PB'] = params.get('PB')
    except Exception as e:
        raise RuntimeError(f"PB获取失败: {str(e)}")

    try:
        if params.get('Ti'):
            lalala['in_params']['Ti'] = params.get('Ti')
    except Exception as e:
        raise RuntimeError(f"Ti获取失败: {str(e)}")

    try:
        if params.get('Td'):
            lalala['in_params']['Td'] = params.get('Td')
    except Exception as e:
        raise RuntimeError(f"Td获取失败: {str(e)}")

    # try:
    #     #if not params.get('end_time'):
    #     start_time_name = await context.get_interaction("start_time_from")
    #     if start_time_name is None:
    #         context.require_interaction({
    #             "id": "start_time_from",  # 此处必须要与上面的get_interaction("target_column_from")
    #             # 内容一致，否则会处于交互界面
    #             "title": "TPT没有识别到，请重新指定您所希望整定时间段的起始时间点start_time",
    #             "type": "form",
    #             "form": {
    #                 "schema": {
    #                     "type": "object",
    #                     "description": "",
    #                     "properties": {
    #                         "start_time_name": {
    #                             "type": "string",
    #                             "title": "开始时间",
    #                             "description": "请指定您所希望整定时间段的起始时间点start_time，格式如：2024-01-01 01:08:01. 如果您只需要最后时刻的PID参数推荐，请输入-1",
    #                         },
    #                     },
    #                     "required": ["start_time_name"]
    #                 }
    #             }
    #         })
    #         return {}

    #     lalala['in_params']['start_time'] = start_time_name['start_time_name']
    #     if start_time_name['start_time_name'] == '-1':
    #         lalala['in_params']['start_time'] = ''

    # except Exception as e:
    #     raise RuntimeError(f"start_time重新获取失败: {str(e)}")

    # try:
    #     # if not params.get('end_time'):
    #     end_time_name = await context.get_interaction("end_time_from")
    #     if end_time_name is None:
    #         context.require_interaction({
    #             "id": "end_time_from",  # 此处必须要与上面的get_interaction("target_column_from")
    #             # 内容一致，否则会处于交互界面
    #             "title": "TPT没有识别到，请重新指定您所希望整定时间段的最终时间点end_time",
    #             "type": "form",
    #             "form": {
    #                 "schema": {
    #                     "type": "object",
    #                     "description": "",
    #                     "properties": {
    #                         "end_time_name": {
    #                             "type": "string",
    #                             "title": "结束时间",
    #                             "description": "请指定您所希望整定时间段的最终时间点end_time，格式如：2024-01-01 01:39:44",
    #                         },
    #                     },
    #                     "required": ["end_time_name"]
    #                 }
    #             }
    #         })
    #         return {}

    #     lalala['in_params']['end_time'] = end_time_name['end_time_name']
        # in_params['id'] = '123456'
        # in_params["appId"] = "123456"
        # in_params['start_time']= '2023/12/31 5:30:05'


    except Exception as e:
        raise RuntimeError(f"end_time重新获取失败: {str(e)}")

    # 入参 出参
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "id",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['id']
        },
        {
            "defaultValue": "",
            "name": "PV",
            "type": 1,
            "typeName": "str",  # list
            "userInput": 1,
            "value": lalala['in_params']['PV']  # json
        },
        {
            "defaultValue": "",
            "name": "SV",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['SV']
        },
        {
            "defaultValue": "",
            "name": "MV",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['MV']
        },
        {
            "defaultValue": "",
            "name": "PB",
            "type": 1,
            "typeName": "str",  # list
            "userInput": 1,
            "value": lalala['in_params']['PB']  # json
        },
        {
            "defaultValue": "",
            "name": "Ti",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['Ti']
        },
        {
            "defaultValue": "",
            "name": "Td",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['Td']
        },
        {
            "defaultValue": "",
            "name": "start_time",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['start_time']
        },
        {
            "defaultValue": "",
            "name": "end_time",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": lalala['in_params']['end_time']
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": "{\"deviceTypeName\": \"gpu\", \"gpuNo\": 1}"  # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "fig_html",
            "type": 1,
            "typeName": "str"
        }
    ]

    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": Config.SPC_FORECAST_ALG_NAME,
            "sourcePath": Config.SPC_FORECAST_ALG_FULL_NAME,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    # r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=context.config["redis_db"])
    # r = get_redis_client(context.config["redis_host"], context.config["redis_port"], is_cluster = context.config["redis_cluster"], redis_db=context.config["redis_db"])
    r.xadd(
        "runtime_python_stream:" + Config.SPC_FORECAST_TOPIC,
        {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
        id='*'
    )

    start = time.time()
    execute_status = 1
    result = None
    while True:
        time.sleep(2)
        execute_res = r.get(Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId)
        redis_implementation_key = Config.TENANT_ID + ":" + f"TRAIN:{Config.TASK_NAME}:" + clientId
        await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
        if execute_res is not None:
            json_array = json.loads(execute_res.decode('utf-8'))
            #json_array = json.loads(execute_res) # 已经是str类型，不需要解码
            await context.log_info(f"json_array = {json_array}")
            if json_array[0].get("implementation") == 1:
                message = json_array[0].get("errorInfo")
                result = r.get(f"{Config.SPC_FORECAST_TOPIC}_" + clientId)
                redis_result_key = f"{Config.SPC_FORECAST_TOPIC}_" + clientId
                await context.log_info(f"redis_result_key = {redis_result_key}")
                result = None if result is None else json.loads(result)
                await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                break
            if json_array[0].get("implementation") == 2:
                message = json_array[0].get("errorInfo")
                execute_status = 2
                await context.log_info(f"message = {message}, execute_status = 2")
                break
        if time.time() - start > Config.TIME_OUT:
            message = "Executing timeout"
            await context.log_info(f"message = {message}")
            break

    await context.log_info(f"result = {result}")
    # ----------------- 调用能力主函数 -------------- #
    # 展示结果图片
    # await context.add_view(data_dict['fig_html'])
    await context.add_view({
        "format": "card",
        "content": {
            "type": "html",
            "title": "PID在线自整定推荐参数轨迹",
            "details": result['fig_html']
        }
    })

    return {'fig_html': result['fig_html']}


'''
TPT调用能力的api
'''
# import io
# import json
# import urllib
# from __runner__ import tool, Context
# import requests
# import base64
# import os


# HEAT_API_HOST = "http://***********:31501"
# RUNTIME_OPT_EXEC_URL = "http://***********:31668/call/app?name=spc_PID_auto_tune_py&built_in=1&time_out=6000"


# @tool(version="*")
# async def spc_PID_auto_tune_tool(context: Context, params: any):
#     # --------- 先判断所需要的参数是否都获取到了 ------- #
#     try:
#         lalala ={
#             'in_params' : {
#                 "id": "123456",
#                 "appId": "123456",
#                 'PV': 'PV',
#                 'MV': 'MV',
#                 'SV': 'SV',
#                 'PB': 'PB',
#                 'Ti': 'Ti',
#                 'Td': 'Td',
#                 'start_time': '2023/12/31 5:30:05', ##'2024/01/04 12:00:02',  # '2024/01/04 12:00:02''2023/12/31 01:48:05'
#                 'end_time':  '2023/12/31 6:30:05' #'2024/01/04 14:00:02'# #
#             }
#         }
#     except Exception as e:
#         raise RuntimeError(f"获取params失败: {str(e)}")

#     # 如果某些参数没获取到，提示有哪些参数没获取到。其中start_time是允许不写的
#     if not params.get('PV'):
#         raise ValueError('PV is required')
#     if not params.get('MV'):
#         raise ValueError('MV is required')
#     if not params.get('SV'):
#         raise ValueError('SV is required')
#     if not params.get('end_time'):
#         raise ValueError('end_time is required')
#     if not params.get('PB') or not params.get('Ti') or not params.get('Td'):
#         raise ValueError('PID parameters are required')

#     # TODO 之后改成这段
#     # try:
#     #     # 必填字段（不包括 start_time）
#     #     required_fields = ['PV', 'MV', 'SV', 'PB', 'Ti', 'Td', 'end_time']
#     #     missing_fields = [key for key in required_fields if not params.get(key)]
#     #
#     #     if missing_fields:
#     #         raise ValueError(f"缺少以下必要字段: {', '.join(missing_fields)}")
#     #
#     #     # 可选字段
#     #     start_time = params.get('start_time')  # 如果没有，默认为 None
#     #
#     #     # 构建 in_params
#     #     in_params = {
#     #         "id": "123456",
#     #         "appId": "123456",
#     #         'PV': params['PV'],
#     #         'MV': params['MV'],
#     #         'SV': params['SV'],
#     #         'PB': params['PB'],
#     #         'Ti': params['Ti'],
#     #         'Td': params['Td'],
#     #         'end_time': params['end_time']
#     #     }
#     #
#     #     if start_time:
#     #         in_params['start_time'] = start_time
#     #
#     # except Exception as e:
#     #     raise RuntimeError(f"解析或校验参数失败: {str(e)}")


#     # TODO 从伟哥那边拿到params之后，先判断是否有PB，Ti，Td的位号
#     # if not params.get('PB') or not params.get('Ti') or not params.get('Td'):
#     #     context.require_interaction({
#     #         "id": "tuning_file_form",
#     #         "title": "请上传PID参数位号的历史数据文件",
#     #         "type": "form",
#     #         "form": {
#     #             "schema": {
#     #                 "type": "object",
#     #                 "description": "请上传PID参数位号的历史数据文件",
#     #                 "properties": {
#     #                     "file": {
#     #                         "title": "位号历史数据文件(.csv)",
#     #                         "type": "string",
#     #                         "format": "data-url"
#     #                     }
#     #                 },
#     #                 "required": ["file"]
#     #             },
#     #             "default": {}
#     #         }
#     #     })
#     #     return {}
#     # 然后应该重新走一遍从第一个开始的流程

#     # ----------------- 调用能力主函数 -------------- #
#     try:
#         response = requests.post(url=RUNTIME_OPT_EXEC_URL, data=json.dumps(lalala), headers={
#             'Content-Type': 'application/json'})
#         if response.status_code != 200:
#             raise ValueError(response.text)
#         result = response.json()
#         data_str = result["data"]  # 是一个字符串，里面包含 fig_html 和 markdown_table
#         data_dict = json.loads(data_str)  # 再解析成字典

#         # 展示结果图片
#         # await context.add_view(data_dict['fig_html'])
#         await context.add_view({
#             "format":"card",
#             "content": {
#                 "type": "html",
#                 "title": "PID在线自整定推荐参数轨迹",
#                 "details": data_dict['fig_html']
#             }
#         })

#     except Exception as e:
#         raise RuntimeError(f"调用PID强化学习在线自整定能力失败: {str(e)} ")

#     return {'fig_html': data_dict['fig_html']}






