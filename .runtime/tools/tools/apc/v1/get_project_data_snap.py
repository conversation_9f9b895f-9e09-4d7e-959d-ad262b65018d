from __runner__ import tool, Context
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# APC_AGENT_TOKEN = os.getenv('APC_AGENT_TOKEN')  # 读取env文件中的key值为Inner_token的值
# APC_AGENT_URL = os.getenv("APC_AGENT_URL")  # 读取网址信息


# 根据projectId对快照数据信息查询(工程类型为Adcon)
# 0、需要先读取全厂的信息数据ID
# 1、根据全厂ID查询已加载的控制器信息
# 2、根据控制器ID读取快照信息
@tool(version="*")
async def get_adcon_data_snap(context: Context, params: any):
    APC_AGENT_URL = context.config["APC_AGENT_URL"]
    X_TPT_TOKEN = context.config['APC_AGENT_TPT_TOKEN']
    APC_AGENT_TOKEN = context.config['APC_AGENT_TOKEN']
    projectName = params["projectName"]
    groupType = 8  # 写死
    params = {'groupType': groupType}
    # 获取全厂ID
    response_config = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/config/node/all'
                                   , params=params, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    if response_config.status_code != 200:
        return response_config.reason
    json_obj_config = json.loads(response_config.text)
    if json_obj_config['code'] != 100000000:
        return json_obj_config['message']
    # 解析数据 得到全厂id
    groupId = json_obj_config['data']['id']
    params_config = {'groupId': groupId}
    # 根据全厂ID对控制器信息进行查询
    response_projecttree = requests.get(
        url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/project/data/projecttree'
        , params=params_config, headers={
            'Content-Type': 'application/json',
            'Authorization': APC_AGENT_TOKEN,
            'X-TPT-TOKEN': X_TPT_TOKEN,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        })
    json_obj_projecttree = json.loads(response_projecttree.text)
    dataList = [emp for emp in json_obj_projecttree['data']['loadedProjectList'] if
                emp["projectName"] == projectName]  # 根据工程名称进行筛选
    if len(dataList) > 0:
        projectId = dataList[0]['projectId']
        params_snap = {'projectId': projectId}
        # 根据projectId对快照数据进行查询
        response_snap = requests.get(url=APC_AGENT_URL + '/inter-api/apc-dashboard/v1/project/data/snap'
                                     , params=params_snap, headers={
                'Content-Type': 'application/json',
                'Authorization': APC_AGENT_TOKEN,
                'X-TPT-TOKEN': X_TPT_TOKEN,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
            })
        json_obj_snap = json.loads(response_snap.text)
        projectName = json_obj_snap['data']['projectName']

        projectStatus = json_obj_snap['data']['projectStatus']
        projectStatusDesc = get_run_status_desc(projectStatus)

        controllerSwitch = json_obj_snap['data']['controllerSwitch']
        controllerSwitchDesc = get_status_desc(controllerSwitch)

        controllerStatus = json_obj_snap['data']['controllerStatus']
        controllerStatusDesc = get_status_desc(controllerStatus)

        controllerRunInfo = generate_batch_form(json_obj_snap['data']['adconParamStatusDtos'])

        snapMarkInfo = f"""
## {projectName}-快照信息展示
|          应用工程       |  工程状态      |  控制开关  |  控制器状态  |
|:----------------------:|:---------------:|:--------:|:--------:|
|{projectName}|{projectStatusDesc}|{controllerSwitchDesc}|{controllerStatusDesc}|

|      类型   |  变量名      |  过程状态  |  开关  |  开关状态  |  运行状态  |
|:----------:|:----------:|:--------:|:--------:|:--------:|:--------:|
{controllerRunInfo["controllerMarkDown"]}
"""

        await context.add_view({
            "format": "card",
            "content": {
                "type": 'markdown',
                "title": '快照信息展示',
                "description": "快照信息展示",
                "details": f"""
{snapMarkInfo}

"""
            }
        })
        onlineControllerBaseRunInfo = {"projectName": projectName, "projectStatus": projectStatusDesc,
                                       "controllerSwitch": controllerSwitchDesc,
                                       "controllerStatus": controllerStatusDesc}

        onlineControllerList = controllerRunInfo["controllerListReturn"]
        # return json_obj_snap['data']
        return format_response(
            success=True,
            data={"onlineControllerBaseRunInfo":onlineControllerBaseRunInfo,"onlineControllerList":onlineControllerList}
        )
    else:
        return format_response(
            success=False,
            message=projectName + '未能加载，无法查询到快照信息！'
        )

# 状态码映射字典
PROCESS_STATUS_MAPPING = {
    '0': "正常",
    '1': "正常",
    '2': "优化输出增量卡上限",
    '3': "优化输出增量卡下限",
    '4': "开关关闭",
    '5': "积分变量",
    '6': "优化输出超上限",
    '7': "优化输出超下限",
    '8': "优化输出达到设定值",
    '9': "非关键MV测量值坏值",
    '10': "关键MV测量值坏值",
    '11': "配置错误",
    '12': "上下限错误",
    '13': "卡边优化"
}

# 状态码映射字典
STATUS_MAPPING = {
    '0': "OFF",
    '1': "ON"
}

# 状态码映射配置
RUN_STATUS_MAPPING = {
    "Ready": "准备",
    "Loaded": "已加载",
    "Running": "运行中",
    "Reloading": "重新加载中",
    "UnLoaded": "未加载",
    "Stopped": "已停止",
    "Error": "错误",
    "unknown": "错误",
    "TimeOut": "超时",
    "Exception": "异常",
    "Loading Failed": "加载失败",
    "Loading": "加载中",
    "Accident Terminated": "异常中止"
}


def get_process_status_desc(code):
    """根据状态码获取中文描述"""
    return PROCESS_STATUS_MAPPING.get(code, "未知状态码")


def get_status_desc(code):
    """根据状态码获取中文描述"""
    return STATUS_MAPPING.get(code, "OFF")


def get_run_status_desc(code):
    """根据状态码获取中文描述"""
    return RUN_STATUS_MAPPING.get(code, "异常")


def generate_batch_form(adconParamStatusDtos):
    ret = ""
    controllerListReturn = []
    for s in adconParamStatusDtos:
        processStatus = s['processStatus']
        processStatusDesc = get_process_status_desc(processStatus)
        switchingDesc = get_status_desc(s['switching'])
        switchStatusDesc = get_status_desc(s['switchStatus'])
        # runStatusDesc = get_status_desc(s['runStatus'])
        ret = ret + f"|{s['type']}|{s['paramName']}|{processStatusDesc}|{switchingDesc}|{switchStatusDesc}|{s['runStatus']}|\r\n"
        controllerItem = {"type": s['type'], "paramName": s['paramName'], "processStatus": processStatusDesc,
                          "switching": switchingDesc, "switchStatus": switchStatusDesc, "runStatus": s['runStatus']}
        controllerListReturn.append(controllerItem)
    return {"controllerMarkDown": ret, "controllerListReturn": controllerListReturn}


def format_response(data=None, success=True, message=""):
    if data is None:
        data = {}
    return {
        "success": success,
        "message": message,
        "data": data
    }
