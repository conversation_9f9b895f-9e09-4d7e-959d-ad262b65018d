#!/usr/bin/env python
# -*- coding:utf-8 -*-

import asyncio
import multiprocessing
import time
import json
import redis
import requests
from datetime import datetime
import threading
from __runner__ import tool, Context
from redis.cluster import RedisCluster, ClusterNode

class Config:
    PREDICT_TOPIC = "single_python_train_predict"  # 发送预测算法执行topic
    PREDICT_TOPIC_RES = "single_python_train_predict_res"  # 预测算法响应topic
    FINE_TUNING_TOPIC = "single_python_fine_tuning"  # 发送微调算法执行topic
    FINE_TUNING_TOPIC_RES = "single_python_fine_tuning_res"  # 发送微调算法响应topic
    REGRESSION_TOPIC = "single_python_regression"  # 发送回归算法执行topic
    REGRESSION_TOPIC_RES = "single_python_regression_res"  # 发送回归算法响应topic
    TENANT_ID = "0"  # 租户
    REDIS_HOST = "seak8sm1.supcon5t.com"  # redis地址/stream_train_logs/
    REDIS_PORT = 26379  # redis端口
    REDIS_DB = 2  # redis数据库
    REDIS_CLUSTER_NODES = ""  # redis集群
    #RUNTIME_URL = "http://***********:31668/stream_train_logs/"  # 运行时sse调用接口
    #RUNTIME_PID_CLOSE_URL = "http://***********:31668/disconnect_train_logs/"  # 运行时关闭进程接口
    TIME_OUT = 3000  # 算法执行超时时间
    PREDICT_ALG_NAME = "AutoMLTimePredictor"
    PREDICT_ALG_FULL_NAME = "AutoMLTimePredictor.zip"
    FINE_TUNING_ALG_NAME = "tpt1x_finetune"
    FINE_TUNING_ALG_FULL_NAME = "tpt1x_finetune.zip"
    REGRESSION_AUTOML_ALG_NAME = "AutoMLRegressionOnline"
    REGRESSION_AUTOML_ALG_FULL_NAME = "AutoMLRegressionOnline.zip"
    REGRESSION_TPT_ALG_NAME = "TPT_Regression"
    REGRESSION_TPT_ALG_FULL_NAME = "TPT_Regression.zip"
    PREDICT_REDIS_END_PRE = "TRAIN:PREDICT:"  # redis存储预测算法执行结果前缀
    PREDICT_REDIS_PRE = "single_python_train_predict_"  # redis存储预测算法自己发送redis结果key前缀
    FINETUNING_REDIS_END_PRE = "TRAIN:FINETUNING:"  # redis存储微调算法执行结果前缀
    FINETUNING_REDIS_PRE = "single_python_fine_tuning_"  # redis存储微调算法自己发送redis结果key前缀
    REGRESSION_REDIS_END_PRE = "TRAIN:REGRESSION:"  # redis存储回归算法执行结果前缀
    REGRESSION_AUTOML_REDIS_PRE = "single_python_train_regression_automl_"  # redis存储automl回归算法自己发送redis结果key前缀
    REGRESSION_TPT_REDIS_PRE = "single_python_train_regression_tpt_"  # redis存储tpt回归算法自己发送redis结果key前缀


# 消费redis stream预测返回消息
def start_predict_consumer():
    r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
    try:
        r.xgroup_create(
            name="runtime_python_stream:" + Config.PREDICT_TOPIC_RES,
            groupname=Config.PREDICT_TOPIC_RES + "_group",
            id='0',
            mkstream=True
        )
    except redis.exceptions.ResponseError as e:
        if "BUSYGROUP" not in str(e):
            raise
    while True:
        try:
            messages = r.xreadgroup(
                groupname=Config.PREDICT_TOPIC_RES + "_group",
                consumername=Config.PREDICT_TOPIC_RES + "_consumer",
                streams={"runtime_python_stream:" + Config.PREDICT_TOPIC_RES: '>'},
                block=5000,
                count=10
            )
            if messages:
                for stream, message_list in messages:
                    for message_id, message_data in message_list:
                        decoded_message = {
                            k.decode('utf-8'): v.decode('utf-8')
                            for k, v in message_data.items()
                        }
                        #print(f"处理消息: {decoded_message}")
                        r.set("TRAIN:PREDICT:" + decoded_message.get("key"), decoded_message.get("value"), ex=604800)
                        r.xack("runtime_python_stream:" + Config.PREDICT_TOPIC_RES, Config.PREDICT_TOPIC_RES
                               + "_group", message_id)
        except KeyboardInterrupt:
            #print("消费者停止")
            break
        except Exception as e:
            #print(f"处理出错: {e}")
            time.sleep(1)

# 执行预测算法
@tool(private=True)
async def execute_predict_alg(context: Context,params: any):
    clientId = context.session_id
    target_variables = params["target_variables"]
    input_variables= params["input_variables"]
    specified_algorithm= params["alg_name"]
    prediction_input_time_period = params["prediction_input_time_period"]
    prediction_output_time_period = params["prediction_output_time_period"]
    frequency = params["frequency"]
    save_dir_path="" #训练成功后模型保存路径，目前为空，需要"s3://"类型的字符串
    train_device_type = params["train_device_type"]
    csv_path = params["csv_path"] 
    #print(datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "执行execute_predict_alg方法, clientId:" + clientId)
    if 'tpt' in specified_algorithm:
        alg_name = Config.FINE_TUNING_ALG_NAME
        alg_full_name = Config.FINE_TUNING_ALG_FULL_NAME
        topic = Config.FINE_TUNING_TOPIC
        redis_end_pre = Config.FINETUNING_REDIS_END_PRE
        redis_pre = Config.FINETUNING_REDIS_PRE
    else:
        alg_name = Config.PREDICT_ALG_NAME
        alg_full_name = Config.PREDICT_ALG_FULL_NAME
        topic = Config.PREDICT_TOPIC
        redis_end_pre = Config.PREDICT_REDIS_END_PRE
        redis_pre = Config.PREDICT_REDIS_PRE

    r = None
    message = ""
    execute_status = 1
    result = None
    cur_time = str(int(time.time() * 1000))
    input_params = [
        {
            "defaultValue": "",
            "name": "clientId",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": clientId
        },
        {
            "defaultValue": "",
            "name": "target_variables",
            "type": 9,
            "typeName": "list",
            "userInput": 1,
            "value": json.dumps(target_variables)
        },
        {
            "defaultValue": "",
            "name": "input_variables",
            "type": 9,
            "typeName": "list",
            "userInput": 1,
            "value": json.dumps(input_variables)
        },
        {
            "defaultValue": "",
            "name": "specified_algorithm",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": specified_algorithm
        },
        {
            "defaultValue": "",
            "name": "prediction_input_time_period",
            "type": 2,
            "typeName": "float",
            "userInput": 1,
            "value": str(prediction_input_time_period)
        },
        {
            "defaultValue": "",
            "name": "prediction_output_time_period",
            "type": 2,
            "typeName": "float",
            "userInput": 1,
            "value": str(prediction_output_time_period)
        },
        {
            "defaultValue": "",
            "name": "frequency",
            "type": 2,
            "typeName": "float",
            "userInput": 1,
            "value": str(frequency)
        },
        {
            "defaultValue": "",
            "name": "save_dir_path",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": save_dir_path
        },
        {
            "defaultValue": "",
            "name": "csv_path",
            "type": 1,
            "typeName": "str",
            "userInput": 1,
            "value": csv_path
        },
        {
            "defaultValue": "",
            "name": "microContent",
            "type": 13,
            "typeName": "json",
            "userInput": 1,
            "value": f'{{"deviceTypeName": "{train_device_type}", "gpuNo": 1}}' # 确认算法走cpu还是npu
        }
    ]
    output_params = [
        {
            "name": "res",
            "type": 1,
            "typeName": "str"
        }
    ]
    algorithm = {
        "algorithm": {
            "builtIn": 1,
            "input": input_params,
            "name": alg_name,
            "sourcePath": alg_full_name,
            "output": output_params
        },
        "id": clientId,
        "tableName": "",
        "type": "redis",
        "curTime": cur_time
    }
    try:
        #r = redis.Redis(host=Config.REDIS_HOST, port=Config.REDIS_PORT, db=Config.REDIS_DB)
        if(context.config["redis_cluster"] == 1):
           Config.REDIS_CLUSTER_NODES = [ClusterNode(host=context.config["redis_host"], port=context.config["redis_port"])]  # redis集群
           r = RedisCluster(startup_nodes=Config.REDIS_CLUSTER_NODES, decode_responses=True)
        else :
            r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=Config.REDIS_DB)
        r.xadd(
            "runtime_python_stream:" + topic,
            {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
            id='*'
        )

        await sse_client(context,clientId, alg_full_name, cur_time)  

        start = time.time()
        while True:
            time.sleep(2)
            execute_res = r.get(Config.TENANT_ID + ":" + redis_end_pre + clientId)
            redis_implementation_key = Config.TENANT_ID + ":" + redis_end_pre + clientId
            await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
            if execute_res is not None:
                json_array = json.loads(execute_res)
                if json_array[0].get("implementation") == 1:
                    message = json_array[0].get("logInfo")
                    result = r.get(redis_pre + clientId)
                    redis_result_key = redis_pre + clientId
                    await context.log_info(f"redis_result_key = {redis_result_key}")
                    result = None if result is None else json.loads(result)
                    await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                    break
                if json_array[0].get("implementation") == 2:
                    message = json_array[0].get("errorInfo")
                    execute_status = 2
                    await context.log_info(f"message = {message}, execute_status = 2")
                    break
            if time.time() - start > Config.TIME_OUT:
                message = "Executing timeout"
                await context.log_info(f"message = {message}")
                break
    finally:
        if r is not None:
            r.close()
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    #print("算法执行响应结果:" + str(res))

    await context.log_info(f"res = {res}")
    return res

# 执行回归算法
@tool(private=True)
async def execute_regression_alg(context:Context,params: any):
    clientId = context.session_id
    target_variables = params["output_variables"]
    input_variables= params["input_variables"]
    specified_algorithm= params["alg_name"]
    specified_algorithms = []
    save_dir_path="" #训练成功后模型保存路径，目前为空，需要"s3://"类型的字符串
    csv_path = params["csv_path"]
    train_device_type = params["train_device_type"]
    #print在k8s环境出现ascii编码问题
    #print(datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "执行execute_regression_alg方法, clientId:" + clientId)

    topic = Config.REGRESSION_TOPIC
    redis_end_pre = Config.REGRESSION_REDIS_END_PRE
    if 'tpt' in specified_algorithm:
        alg_name = Config.REGRESSION_TPT_ALG_NAME
        alg_full_name = Config.REGRESSION_TPT_ALG_FULL_NAME
        redis_pre = Config.REGRESSION_TPT_REDIS_PRE
    else:
        alg_name = Config.REGRESSION_AUTOML_ALG_NAME
        alg_full_name = Config.REGRESSION_AUTOML_ALG_FULL_NAME
        redis_pre = Config.REGRESSION_AUTOML_REDIS_PRE

    r = None
    try:
        cur_time = str(int(time.time() * 1000))
        input_params = [
            {
                "defaultValue": "",
                "name": "clientId",
                "type": 1,
                "typeName": "str",
                "userInput": 1,
                "value": clientId
            },
            {
                "defaultValue": "",
                "name": "targets",
                "type": 9,
                "typeName": "list",
                "userInput": 1,
                "value": json.dumps(target_variables)
            },
            {
                "defaultValue": "",
                "name": "input_variables",
                "type": 9,
                "typeName": "list",
                "userInput": 1,
                "value": json.dumps(input_variables)
            },
            {
                "defaultValue": "",
                "name": "specified_algorithms",
                "type": 9,
                "typeName": "list",
                "userInput": 1,
                "value": json.dumps(specified_algorithms)
            },
            {
                "defaultValue": "",
                "name": "save_dir_path",
                "type": 1,
                "typeName": "str",
                "userInput": 1,
                "value": save_dir_path
            },
            {
                "defaultValue": "",
                "name": "csv_path",
                "type": 1,
                "typeName": "str",
                "userInput": 1,
                "value": csv_path
            },
            {
                "defaultValue": "",
                "name": "microContent",
                "type": 13,
                "typeName": "json",
                "userInput": 1,
                "value": f'{{"deviceTypeName": "{train_device_type}", "gpuNo": 1}}' # 确认算法走cpu还是npu
            }
        ]
        output_params = [
            {
                "name": "res",
                "type": 1,
                "typeName": "str"
            }
        ]
        algorithm = {
            "algorithm": {
                "builtIn": 1,
                "input": input_params,
                "name": alg_name,
                "sourcePath": alg_full_name,
                "output": output_params
            },
            "id": clientId,
            "tableName": "",
            "type": "redis",
            "curTime": cur_time
        }
        if(context.config["redis_cluster"] == 1):
           Config.REDIS_CLUSTER_NODES = [ClusterNode(host=context.config["redis_host"], port=context.config["redis_port"])]  # redis集群
           r = RedisCluster(startup_nodes=Config.REDIS_CLUSTER_NODES, decode_responses=True)
        else :
            r = redis.Redis(host=context.config["redis_host"], port=context.config["redis_port"], db=Config.REDIS_DB)
        r.xadd(
            "runtime_python_stream:" + topic,
            {"tenant_id": Config.TENANT_ID, "value": json.dumps(algorithm, ensure_ascii=False)},
            id='*'
        )

        await sse_client(context,clientId, alg_full_name, cur_time)

        start = time.time()
        message=""
        execute_status = 1
        result = None
        while True:
            time.sleep(2)
            execute_res = r.get(Config.TENANT_ID + ":" + redis_end_pre + clientId)
            redis_implementation_key = Config.TENANT_ID + ":" + redis_end_pre + clientId
            await context.log_info(f"redis_implementation_key = {redis_implementation_key}")
            if execute_res is not None:
                json_array = json.loads(execute_res)
                if json_array[0].get("implementation") == 1:
                    message = json_array[0].get("errorInfo")
                    result = r.get(redis_pre + clientId)
                    redis_result_key = redis_pre + clientId
                    await context.log_info(f"redis_result_key = {redis_result_key}")
                    result = None if result is None else json.loads(result)
                    await context.log_info(f"message = {message}, execute_status = 1, result = {result}")
                    break
                if json_array[0].get("implementation") == 2:
                    message = json_array[0].get("errorInfo")
                    execute_status = 2
                    await context.log_info(f"message = {message}, execute_status = 2")
                    break
            if time.time() - start > Config.TIME_OUT:
                message = "Executing timeout"
                await context.log_info(f"message = {message}")
                break
    finally:
        if r is not None:
            r.close()
    res = {
        "message": message,
        "executeStatus": execute_status,  # 1成功, 2失败
        "result": result
    }
    #print("算法执行响应结果:" + str(res))
    await context.log_info(f"res = {res}")
    return res


async def sse_client(context: Context,client_id: str, sourcePath: str, cur_time: str):

    #print("调用sse_client方法")
    params = {
        "sourcePath": sourcePath,
        "client_id": client_id,
        "cur_time": cur_time
    }

    headers = {
        "Accept": "text/event-stream",
        "tenant-id": Config.TENANT_ID
    }
    #response = requests.get(Config.RUNTIME_URL + client_id, params=params, headers=headers, stream=True)
    response = requests.get(context.config["sse_model_train_log_url"] + client_id, params=params, headers=headers, stream=True)
    try:
        if response.status_code == 200:
            for line in response.iter_lines(decode_unicode=True):
                if ":" in line:
                    field, value = line.split(':', 1)
                    value = value.lstrip()
                    if "data" in field:
                        # TODO value 为日志信息
                        await context.append_view({
                            "details": f"""
>    -{value}
"""
                        })
#                     #测试打印所有接收的日志
#                     await context.append_view({
#                             "details": f"""
# >    -{line}
# """
#                         })
                    if "event" in field and "ended" in value:
                        #print("关闭连接")
                        break
        else:
           {} #print("连接失败")
    finally:
        disconnect_params = {
            "cur_time": cur_time,
            "sourcePath": sourcePath
        }
        disconnect_headers = {
            "tenant-id": Config.TENANT_ID
        }
        disconnect_response = requests.post(
            #Config.RUNTIME_PID_CLOSE_URL + client_id,
            context.config["dissconn_sse_model_train_log_url"] + client_id,
            params=disconnect_params,
            headers=disconnect_headers
        )
        disconnect_response.close()
        #print("调用断开连接接口：" + str(disconnect_response))
        response.close()
       

# if __name__ == '__main__':

    # 监听预测redis返回消息
    # p1 = multiprocessing.Process(target=start_predict_consumer)
    # p1.start()

    # for index in range(1):
    #     x = threading.Thread(target=execute_predict_alg,
    #                          args=(str(index+111), ['GXHYNH_133_AI_2101A.PV', 'GXHYNH_133_AI_2101C.PV'],
    #                                ['GXHYNH_133_PI_2101.PV', 'GXHYNH_133_PI_2102.PV',
    #                                 'GXHYNH_133_FI_2101A.PV', 'GXHYNH_133_FI_2102A.PV',
    #                                 'GXHYNH_133_FI_2104A.PV', 'GXHYNH_133_FI_2105A.PV',
    #                                 'GXHYNH_133_FI_2106A.PV', 'GXHYNH_133_AI_2101A.PV',
    #                                 'GXHYNH_133_AI_2101C.PV'], 'AutoML', 10, 0.5, 0.08333333, './', 's3://recommend/0/admin', 's3://recommend/0/admin/fafed236524d4e26be5964db3e866deb_预测任务数据示例.csv'))
    #     x.start()

