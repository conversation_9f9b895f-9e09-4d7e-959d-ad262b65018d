{"$schema": "../../../schema/tools.schema.json", "tools": [{"name": "get_self_control_rate", "alias": ["装置实时自控率、平稳率计算", "装置实时自控平稳率计算"], "related_app": [{"name": "loop_evaluation", "alias": "回路评估"}, {"name": "loop_monitor", "alias": "回路监控"}], "description": "获取指定id装置的自控率能力", "params": {"type": "object", "description": "获取指定id装置的自控率的输入参数", "properties": {"groupId": {"type": "number", "description": "装置id"}}, "required": ["groupId"]}, "result": {"type": "object", "description": "指定id装置的自控率、平稳率等数据", "properties": {"closeRate": {"type": "number", "description": "自控率"}, "stableRate": {"type": "number", "description": "平稳率"}}, "required": ["closeRate", "stableRate"]}, "catalog": "statistics"}, {"skip_summary": true, "name": "get_group_or_loop_id_list", "alias": ["回路信息查询", "回路组态查询"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}, {"name": "loop_info_query", "alias": "回路信息查询"}], "description": "获取所有可能的回路或装置id列表。", "params": {"description": "获取指定路径装置的输入参数，loopName和loopDesc只允许输入其中一个,也可以都没有。", "properties": {"advanceLoopType": {"description": "只有按复杂回路类型来查询时，才需要传入此项；如果不需要按复杂回路类型来查询时，请传入null。如果传入了loopName或loopDesc，则此项会被忽略。 复杂回路类型： 0常规 1串级 4超驰 5选择 7保护", "enum": [0, 1, 4, 5, 7, null], "type": ["integer", "null"]}, "groupPath": {"description": "装置路径或装置名称，如果用户问题中没有匹配的则为空。", "items": {"type": "string"}, "type": "array", "x-source": ["milvus_m3_deviceinfo"]}, "loopDesc": {"description": "回路描述列表，回路描述通常带有自然语言，例如V951水包液位、1.0MPa进抽提蒸汽流量等，如果没有则为空，例如问氯碱装置有哪些回路则为空。", "items": {"type": "string"}, "type": "array", "x-source": ["milvus_m3_loopinfo"]}, "loopName": {"description": "回路名称列表，只能是数字+英文的形式，需要整定的都是回路，如果没有则为空。用户提问的回路名可能不完整。", "items": {"type": "string"}, "type": "array", "x-source": ["milvus_m3_loopinfo"]}, "needTagValue": {"description": "是否需要获取对应位号的实时值(如测量值、设定值、各类量程上下限等)，如果需要(例如获取回路的PID参数等)则传入True；否则传入False", "type": "boolean"}, "queryingCascade": {"description": "如果要查询两个回路是否互相为串级关系，则传入True；否则传入False", "type": "boolean"}, "queryingList": {"description": "如果要返回所有符合条件的回路/装置，则传入True；否则传入False。如果是整定则必须传入False", "type": "boolean"}, "queryingLoop": {"description": "如果查询结果要包含回路信息，则传入True；如果只需要查询装置信息，则传入False", "type": "boolean"}}, "required": ["groupPath", "loopName", "loopDesc", "advanceLoopType", "queryingLoop", "queryingList", "needTagValue", "queryingCascade"], "type": "object"}, "result": {"type": "object", "description": "所选的回路/装置详细信息，查询的结果会放在相应的字段中，保证只有一个字段不为None，这个字段存储着查询的结果", "properties": {"queryGroupSingleResult": {"type": "object", "description": "查询单条装置信息的结果", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "装置id"}}}, "queryGroupListResult": {"type": "array", "description": "所有符合条件的装置列表", "items": {"type": "object", "description": "装置信息", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "装置id"}}}}, "queryLoopWithoutTagValueSingleResult": {"type": "object", "description": "查询单条回路信息的结果(无位号实时值)", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "回路id"}, "loopName": {"type": "string", "description": "回路名称"}, "loopDescription": {"type": ["string", "null"], "description": "回路描述"}, "loopType": {"type": "integer", "description": "回路类型", "enum": [0, 1, 2, 3, 4, 5, 6]}, "advanceLoopType": {"type": "number", "description": "复杂回路类型 0常规 1串级 2分程 3比值 4超驰 5选择 6串级+选择 7保护", "enum": [0, 1, 2, 3, 4, 5, 6, 7]}, "cascadeLoopName": {"type": ["string", "null"], "description": "如果复杂回路类型为串级，对应串级回路的名称"}, "masterOrSlave": {"type": "boolean", "description": "true为串级主回路，false为串级副回路"}, "suggestTuningMethod": {"type": "number", "description": "推荐的整定算法 0IMC 1智能向导 2用户交互界面", "enum": [0, 1, 2]}, "currentPIDParam": {"type": "string", "description": "该回路当前的PID参数，格式为：P/I/D"}, "groupId": {"type": "number", "description": "该回路所属分组id"}}}, "queryLoopWithTagValueSingleResult": {"type": "object", "description": "查询单条回路信息的结果(有位号实时值)", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "回路id"}, "loopName": {"type": "string", "description": "回路名称"}, "loopDescription": {"type": ["string", "null"], "description": "回路描述"}, "loopType": {"type": "integer", "description": "回路类型", "enum": [0, 1, 2, 3, 4, 5, 6]}, "advanceLoopType": {"type": "number", "description": "复杂回路类型 0常规 1串级 2分程 3比值 4超驰 5选择 6串级+选择 7保护", "enum": [0, 1, 2, 3, 4, 5, 6, 7]}, "cascadeLoopName": {"type": ["string", "null"], "description": "如果复杂回路类型为串级，对应串级回路的名称"}, "masterOrSlave": {"type": "boolean", "description": "true为串级主回路，false为串级副回路"}, "suggestTuningMethod": {"type": "number", "description": "推荐的整定算法 0IMC 1智能向导 2用户交互界面", "enum": [0, 1, 2]}, "currentPIDParam": {"type": "string", "description": "该回路当前的PID参数，格式为：P/I/D"}, "groupId": {"type": "number", "description": "该回路所属分组id"}, "tags": {"type": "array", "description": "回路所属的位号列表 按顺序分别代表测量值、设定值、阀位值、比例、积分、微分、阀位值量程上限、阀位值量程下限、设定值量程上限、设定值量程下限、是否串级、控制模式、正反作用、阀位值限幅高限、阀位值限幅低限、设定值限幅高限、设定值限幅低限", "items": {"type": ["string", "null"]}}, "tagValues": {"type": "array", "description": "位号列表中对应下标位号的实时值 按顺序分别代表测量值、设定值、阀位值、比例、积分、微分、阀位值量程上限、阀位值量程下限、设定值量程上限、设定值量程下限、是否串级、控制模式、正反作用、阀位值限幅高限、阀位值限幅低限、设定值限幅高限、设定值限幅低限", "items": {"type": ["number", "string", "null"]}}}}, "queryLoopWithoutTagValueListResult": {"type": "array", "description": "查询回路列表信息的结果(无位号实时值)", "items": {"type": "object", "description": "所有符合条件的回路列表", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "装置/回路id"}, "loopName": {"type": "string", "description": "回路名称"}, "loopDescription": {"type": ["string", "null"], "description": "回路描述"}, "loopType": {"type": "integer", "description": "回路类型", "enum": [0, 1, 2, 3, 4, 5, 6]}, "advanceLoopType": {"type": "number", "description": "复杂回路类型 0常规 1串级 2分程 3比值 4超驰 5选择 6串级+选择 7保护", "enum": [0, 1, 2, 3, 4, 5, 6, 7]}, "cascadeLoopName": {"type": ["string", "null"], "description": "如果复杂回路类型为串级，对应串级回路的名称"}, "masterOrSlave": {"type": "boolean", "description": "true为串级主回路，false为串级副回路"}, "suggestTuningMethod": {"type": "number", "description": "推荐的整定算法 0IMC 1智能向导 2用户交互界面", "enum": [0, 1, 2]}, "currentPIDParam": {"type": "string", "description": "该回路当前的PID参数，格式为：P/I/D"}, "groupId": {"type": "number", "description": "该回路所属分组id"}}}}, "queryLoopWithTagValueListResult": {"type": "array", "description": "查询回路列表信息的结果(有位号实时值)", "items": {"type": "object", "description": "所有符合条件的回路列表", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "装置/回路id"}, "loopName": {"type": "string", "description": "回路名称"}, "loopDescription": {"type": ["string", "null"], "description": "回路描述"}, "loopType": {"type": "integer", "description": "回路类型", "enum": [0, 1, 2, 3, 4, 5, 6]}, "advanceLoopType": {"type": "number", "description": "复杂回路类型 0常规 1串级 2分程 3比值 4超驰 5选择 6串级+选择 7保护", "enum": [0, 1, 2, 3, 4, 5, 6, 7]}, "cascadeLoopName": {"type": ["string", "null"], "description": "如果复杂回路类型为串级，对应串级回路的名称"}, "masterOrSlave": {"type": "boolean", "description": "true为串级主回路，false为串级副回路"}, "suggestTuningMethod": {"type": "number", "description": "推荐的整定算法 0IMC 1智能向导 2用户交互界面", "enum": [0, 1, 2]}, "currentPIDParam": {"type": "string", "description": "该回路当前的PID参数，格式为：P/I/D"}, "tags": {"type": "array", "description": "回路所属的位号列表 按顺序分别代表测量值、设定值、阀位值、比例、积分、微分、阀位值量程上限、阀位值量程下限、设定值量程上限、设定值量程下限、是否串级、控制模式、正反作用、阀位值限幅高限、阀位值限幅低限、设定值限幅高限、设定值限幅低限", "items": {"type": ["string", "null"]}}, "tagValues": {"type": "array", "description": "位号列表中对应下标位号的实时值 按顺序分别代表测量值、设定值、阀位值、比例、积分、微分、阀位值量程上限、阀位值量程下限、设定值量程上限、设定值量程下限、是否串级、控制模式、正反作用、阀位值限幅高限、阀位值限幅低限、设定值限幅高限、设定值限幅低限", "items": {"type": ["number", "string", "null"]}}, "groupId": {"type": "number", "description": "该回路所属分组id"}}}}, "queryLoopNotFoundResult": {"type": "object", "description": "找不到指定回路时返回的结果", "properties": {"missingLoopOrGroup": {"type": "string", "description": "找不到所选回路/装置时返回的消息"}, "found": {"type": ["boolean", "null"], "description": "表示没有找到指定的回路/装置"}, "id": {"type": "number", "description": "缺省id，用来传给后续的工作流提示没有找到回路，使用没有符合条件的回路的方案，防止在执行转换函数时报错"}, "loopName": {"type": "string", "description": "缺省回路名称，用来传给后续的工作流提示没有找到回路，使用没有符合条件的回路的方案，防止在执行转换函数时报错"}, "groupId": {"type": "number", "description": "缺省分组id，用来传给后续的工作流提示没有找到回路，使用没有符合条件的回路的方案，防止在执行转换函数时报错"}}}, "resultField": {"type": "string", "description": "优先判断此字段，此字段的内容表示存放查询结果的字段的名称，除此字段指定名称的字段外其余字段均为None"}}, "required": ["resultField"]}, "catalog": "statistics"}, {"skip_summary": true, "name": "loop_info_manage", "alias": ["回路信息管理"], "related_app": [{"name": "loop_info_manage", "alias": "回路信息管理"}], "description": "查询回路和相关装置的详细信息，用列表的方式呈现，会打开额外的页面", "params": {"type": "object", "properties": {}, "required": []}, "result": {"type": "object", "properties": {}, "required": []}, "catalog": "statistics"}, {"name": "loop_auto_config", "alias": ["回路信息自动更新"], "description": "回路自动组态能力，当查询的回路不在回路表中时自动从datahub查找回路并创建至回路表中，也可以删除指定名称的回路", "params": {"type": "object", "description": "回路自动组态能力的输入参数", "properties": {"loopName": {"type": "string", "description": "回路名称"}, "loopDesc": {"type": "string", "description": "回路描述"}, "forDelete": {"type": "boolean", "description": "是否为删除此回路，是则为True，否则为False"}}, "required": ["loopName", "loopDesc", "forDelete"]}, "result": {"type": "object", "description": "回路信息自动更新结果", "oneOf": [{"type": "object", "description": "创建的回路详细信息", "properties": {"groupName": {"type": "string", "description": "装置名称"}, "groupPath": {"type": "string", "description": "装置路径"}, "id": {"type": "number", "description": "回路id"}, "loopName": {"type": "string", "description": "回路名称"}, "advanceLoopType": {"type": "number", "description": "复杂回路类型 0常规 1串级 2分程 3比值 4超驰 5选择 6串级+选择 7保护", "enum": [0, 1, 2, 3, 4, 5, 6, 7]}, "cascadeLoopName": {"type": "string", "description": "如果复杂回路类型为串级，对应串级回路的名称"}, "masterOrSlave": {"type": "boolean", "description": "true为串级主回路，false为串级副回路"}, "suggestTuningMethod": {"type": "number", "description": "推荐的整定算法 0IMC 1智能向导 2用户交互界面", "enum": [0, 1, 2]}, "currentPIDParam": {"type": "string", "description": "该回路当前的PID参数，格式为：P/I/D"}}, "required": ["groupName", "groupPath", "id", "loopName", "advanceLoopType", "cascadeLoopName", "master<PERSON>r<PERSON><PERSON><PERSON>", "suggestTuningMethod", "currentPIDParam"]}, {"type": "object", "description": "创建失败/删除操作完成后的消息", "properties": {"failed": {"type": "boolean", "description": "是否创建失败"}, "msg": {"type": "string", "description": "失败原因/操作消息"}}, "required": ["failed", "msg"]}]}, "catalog": "statistics"}, {"name": "get_loop_detail", "alias": ["回路详细信息查询"], "description": "获取指定回路详细信息的能力，会返回包含描述、回路类型、当前PID参数、位号列表及对应的位号实时值等信息。需要提供回路所属的装置id", "params": {"type": "object", "description": "获取指定回路详细信息的输入参数", "properties": {"loopId": {"type": "number", "description": "回路id"}}, "required": ["loopId"]}, "result": {"type": "object", "description": "指定回路的详细信息，包含描述、回路类型(0 1 2 3 4 5分别代表流量、压力、液位、温度、质量、其他)、当前PID参数、位号列表及对应实时值等信息", "properties": {"description": {"type": "string", "description": "回路描述"}, "loopType": {"type": "number", "description": "回路类型，0 1 2 3 4 5分别代表流量、压力、液位、温度、质量、其他", "enum": [0, 1, 2, 3, 4, 5]}, "pidParams": {"type": "string", "description": "当前PID参数"}, "tags": {"type": "array", "description": "回路所属的位号列表 按顺序分别代表测量值、设定值、阀位值、比例、积分、微分、阀位值量程上限、阀位值量程下限、设定值量程上限、设定值量程下限、是否串级、控制模式、正反作用、阀位值限幅高限、阀位值限幅低限、设定值限幅高限、设定值限幅低限", "items": {"type": "string"}}, "tagValues": {"type": "array", "description": "位号列表中对应下标位号的实时值 按顺序分别代表测量值、设定值、阀位值、比例、积分、微分、阀位值量程上限、阀位值量程下限、设定值量程上限、设定值量程下限、是否串级、控制模式、正反作用、阀位值限幅高限、阀位值限幅低限、设定值限幅高限、设定值限幅低限", "items": {"type": "number"}}}, "required": ["description", "loopType", "pidParams", "tags", "tagValues"]}, "catalog": "statistics"}, {"name": "get_loop_2h_report", "alias": ["回路指标历史数据库查询", "回路评估数据查询"], "related_app": [{"name": "loop_evaluation", "alias": "回路评估"}, {"name": "loop_tuning", "alias": "回路整定"}], "description": "获取指定时间段指定回路评估实时报告的能力，包含回路综合评分、综合评级等信息。查询的起止时间范围(startTime、endTime)为必填项，若用户没有指定，则传最近2小时的时间范围。", "params": {"type": "object", "description": "获取指定回路评估实时报告的输入参数", "properties": {"loopName": {"type": "array", "description": "回路名称列表，全字匹配", "items": {"type": "string"}}, "startTime": {"type": "string", "description": "查询起始时间，yyyy-MM-dd HH:mm格式，必传"}, "endTime": {"type": "string", "description": "查询截止时间，yyyy-MM-dd HH:mm格式，必传"}, "reportType": {"type": "number", "description": "报告类型 0实时报告 1日报 2周报 3月报 请按问题需求来选择报告类型，例如：最近x小时或某一天就选实时报告，本周的就选日报，本月的就选周报，最近几个月的就选月报", "enum": [0, 1, 2, 3]}}, "required": ["loopName", "startTime", "endTime", "reportType"]}, "result": {"type": "object", "description": "指定回路的评估实时报告，包含回路综合评分等信息", "properties": {"reportList": {"type": "array", "description": "指定回路的评估实时报告列表，每条记录包含其特定时间段(dataInterval)的回路名称(loopName)、回路综合评分(comprehensiveScore)、回路综合评级(comprehensiveClass)、自控率(closeRate)、平稳率(stableRate)等信息。其中，回路综合评级字段不同的值对应了不同的回路评级，不要直接展示回路综合评级的字段值，应展示回路综合评级字段值对应的回路评级，以下是回路综合评级字段值对应的回路评级：1为优，2为良，3为中，4为差，5为开环，6为回路异常，7为条件剔除", "items": {"type": "object", "properties": {"dataInterval": {"type": "array", "items": {"type": "string"}}, "loopName": {"type": "string", "description": "回路名称"}, "comprehensiveScore": {"type": "number", "description": "回路综合评分"}, "comprehensiveClass": {"type": "integer", "description": "回路综合评级 1为优，2为良，3为中，4为差，5为开环，6为回路异常，7为条件剔除", "enum": [1, 2, 3, 4, 5, 6, 7]}, "closeRate": {"type": "number", "description": "自控率"}, "stableRate": {"type": "number", "description": "平稳率"}}}}}, "required": ["reportList"]}, "catalog": "statistics"}, {"name": "get_group_2h_report", "alias": ["装置指标历史数据库查询", "装置评估数据查询"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}, {"name": "loop_evaluation", "alias": "回路评估"}], "description": "获取指定时间段指定id装置评估报告列表的能力，每份报告包含综合评分、综合评级等信息。查询的起止时间范围(startTime、endTime)为必填项。其中输入参数中的报告类型(reportType)参数可控制每份报告的评估时间范围，可用于获取指定装置的日/周/月自控率、平稳率趋势等。", "params": {"type": "object", "description": "获取指定id装置评估实时报告的输入参数", "properties": {"groupId": {"type": "number", "description": "装置id"}, "startTime": {"type": "string", "description": "查询起始时间，yyyy-MM-dd HH:mm:ss格式，必传，必须严格按照此格式传入，不能带额外的时区信息等"}, "endTime": {"type": "string", "description": "查询截止时间，yyyy-MM-dd HH:mm:ss格式，必传，必须严格按照此格式传入，不能带额外的时区信息等"}, "reportType": {"type": "number", "description": "报告类型 0实时报告 1日报 2周报 3月报 请按问题需求来选择报告类型，例如：最近x小时或某一天就选实时报告，本周的就选日报，本月的就选周报，最近几个月的就选月报", "enum": [0, 1, 2, 3]}}, "required": ["groupId", "startTime", "endTime", "reportType"]}, "result": {"type": "object", "description": "指定id装置的评估实时报告，包含综合评分等信息", "properties": {"reportList": {"type": "array", "description": "指定id装置的评估实时报告列表，每条记录包含其特定时间段(dataInterval)的装置名称(groupName)、综合评分(comprehensiveScore)、综合评级(comprehensiveClass)、自控率(closeRate)、平稳率(stableRate)等信息。其中，综合评级字段不同的值对应了不同的评级，不要直接展示综合评级的字段值，应展示综合评级字段值对应的评级，以下是综合评级字段值对应的评级：1为优，2为良，3为中，4为差，5为开环，6为回路异常，7为条件剔除", "items": {"type": "object", "properties": {"dataInterval": {"type": "string", "description": "时间段"}, "groupName": {"type": "string", "description": "装置名称"}, "comprehensiveScore": {"type": "number", "description": "综合评分"}, "comprehensiveClass": {"type": "number", "description": "综合评级", "enum": [1, 2, 3, 4, 5, 6, 7]}, "closeRate": {"type": "number", "description": "自控率"}, "stableRate": {"type": "number", "description": "平稳率"}}}}}, "required": ["reportList"]}, "catalog": "statistics"}, {"name": "get_group_2h_close_stable_rate", "alias": ["装置历史自控率、平稳率计算", "装置历史自控平稳率计算"], "related_app": [{"name": "loop_evaluation", "alias": "回路评估"}], "description": "获取指定id装置自控率的能力", "params": {"type": "object", "description": "获取特定时间段指定id装置自控率的输入参数", "properties": {"groupId": {"type": "number", "description": "装置id"}, "startTime": {"type": "string", "description": "查询起始时间，yyyy-MM-dd HH:mm:ss格式"}, "endTime": {"type": "string", "description": "查询截止时间，yyyy-MM-dd HH:mm:ss格式"}, "reportType": {"type": "number", "description": "报告类型 0实时报告 1日报 2周报 3月报 请按问题需求来选择报告类型，例如：最近x小时或某一天就选实时报告，本周的就选日报，本月的就选周报，最近几个月的就选月报", "enum": [0, 1, 2, 3]}}, "required": ["groupId", "startTime", "endTime", "reportType"]}, "result": {"type": "object", "description": "指定id装置的自控率、平稳率", "properties": {"closeRate": {"type": "number", "description": "指定id装置的自控率"}, "stableRate": {"type": "number", "description": "指定id装置的平稳率"}}, "required": ["closeRate", "stableRate"]}, "catalog": "statistics"}, {"name": "get_manual_tuning_record", "alias": ["人工整定结果查询"], "description": "获取指定回路的最近一次人工整定记录", "params": {"type": "object", "description": "获取指定回路最近一次人工整定记录的输入参数", "properties": {"loopName": {"type": "string", "description": "回路名称"}}, "required": ["loopName"]}, "result": {"type": "object", "description": "指定回路的最近一次人工整定记录", "properties": {"loopName": {"type": "string", "description": "回路名称"}, "pidNow": {"type": "string", "description": "整定前pid参数"}, "pidSuggest": {"type": "string", "description": "整定后pid参数"}, "tuningTime": {"type": "string", "description": "整定时间"}, "grade": {"type": "number", "description": "结果评分(星级)"}, "dataInterval": {"type": "object", "description": "选取数据时间段"}}, "required": ["loopName", "pidNow", "pidSuggest", "tuningTime", "grade", "dataInterval"]}, "catalog": "statistics"}, {"name": "get_loop_realtime_state", "alias": ["回路运行实时状态计算"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}, {"name": "loop_evaluation", "alias": "回路评估"}, {"name": "loop_monitor", "alias": "回路监控"}], "description": "获取指定回路的实时运行状态", "params": {"type": "object", "description": "获取指定回路/装置下所有回路的实时运行状态的输入参数", "properties": {"loopName": {"type": "string", "description": "回路名称 如果要查询某个特定回路的运行状态，请传入此项"}, "groupId": {"type": "integer", "description": "装置id 如果要查询某个装置下所有回路的运行状态，请传入此项"}}, "required": []}, "result": {"type": "object", "description": "指定回路/装置下所有回路的实时运行状态，会返回所有符合条件的记录，存放在一个数组中", "properties": {"resultList": {"type": "array", "description": "所有符合条件的回路的实时运行状态记录", "items": {"type": "object", "description": "回路实时运行状态", "properties": {"loopName": {"type": "string", "description": "回路名称"}, "description": {"type": "string", "description": "回路描述"}, "selfControlStatus": {"type": "string", "description": "自控情况"}, "realtimeStableStatus": {"type": "string", "description": "平稳情况"}, "rejectCondition": {"type": "string", "description": "剔除条件"}, "runningState": {"type": "string", "description": "运行状态"}}}}}, "required": ["resultList"]}, "catalog": "statistics"}, {"skip_summary": true, "name": "imc_tuning", "alias": ["基于历史数据的参数整定", "基于历史数据的回路整定", "IMC整定"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}], "dependencies": {"file": [{"category": {"name": "f_loop_history", "alias": "回路历史数据"}, "required": false}]}, "description": "对指定回路进行普通整定，需要用户提供回路历史数据时间段", "params": {"type": "object", "description": "普通整定的输入参数", "properties": {"loopId": {"type": "number", "description": "回路id"}, "startTime": {"type": "string", "description": "历史数据选取起始时间，yyyy-MM-dd HH:mm:ss格式"}, "endTime": {"type": "string", "description": "历史数据选取截止时间，yyyy-MM-dd HH:mm:ss格式"}, "fileId": {"type": "number", "description": "如果在之前的操作中上传了位号历史数据文件，则在此项指定要读取文件的id"}}, "required": ["loopId", "startTime", "endTime", "fileId"]}, "result": {"type": "object", "description": "整定结果", "properties": {"excitCoef": {"type": "string", "description": "激励指数"}, "disturbCoef": {"type": "string", "description": "扰动指数"}, "nonlinearCoef": {"type": "string", "description": "非线性指数"}, "pidParams": {"type": "object", "description": "整定后p、i、d参数"}, "rDelay": {"type": "string", "description": "时滞时间τ(分钟)"}, "rGain": {"type": "string", "description": "模型增益K"}, "rT1": {"type": "string", "description": "时间常数T1"}, "rT2": {"type": "string", "description": "时间常数T2"}, "reliabilityIndex": {"type": "number", "description": "置信度"}, "loopType": {"type": "number", "description": "回路类型，0 1 2 3 4 5分别代表流量、压力、液位、温度、质量、其他", "enum": [0, 1, 2, 3, 4, 5]}}, "required": ["excit<PERSON>oef", "<PERSON><PERSON><PERSON><PERSON>", "nonlinear<PERSON><PERSON>f", "pidParams", "r<PERSON><PERSON><PERSON>", "r<PERSON><PERSON>", "rT1", "rT2", "reliabilityIndex", "loopType"]}, "catalog": "optimization"}, {"name": "tuning_file_upload", "alias": ["上传文件"], "description": "上传imc整定需要的位号历史数据文件，csv格式", "params": {"type": "object", "description": "", "properties": {}, "required": []}, "result": {"type": "object", "description": "上传成功后返回的文件id，如果用户选择不上传文件，则返回-1", "properties": {"fileId": {"type": "number", "description": "文件id，如果用户选择不上传文件，则返回-1"}, "formResult": {"type": "object", "description": "额外的表单数据(如果有)"}}, "required": ["fileId"]}, "catalog": "optimization"}, {"skip_summary": true, "name": "pre_tuning", "alias": ["知识驱动型参数整定", "基于专家经验的参数整定", "预整定"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}], "description": "对指定回路进行预整定，适用于用户没有指定回路历史数据时间范围的情况，即使数据校验的结果(valid)为False也可以正常执行此能力", "params": {"type": "object", "description": "预整定的输入参数", "properties": {"loopId": {"type": "number", "description": "回路id"}}, "required": ["loopId"]}, "result": {"type": "object", "description": "整定结果", "properties": {"pidParams": {"type": "array", "description": "整定后的PID参数", "items": {"type": ["string", "null"]}}}, "required": ["pidParams"]}, "catalog": "optimization"}, {"skip_summary": true, "name": "batch_pre_tuning", "alias": ["开车阶段参数批量整定"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}], "dependencies": {"file": [{"category": {"name": "f_loop_config", "alias": "回路组态"}}]}, "description": "对上传的回路列表csv文件中的回路进行批量预整定", "params": {"type": "object", "description": "", "oneOf": [{"type": "object", "description": "如果上一步上传了回路清单文件，则传入上一步返回的文件id", "properties": {"fileId": {"type": "number", "description": "如果上一步上传了回路清单文件，则传入上一步返回的文件id"}}, "required": ["fileId"]}, {"type": "object", "description": "所有要进行预整定的回路id列表", "properties": {"loopIdList": {"type": "array", "description": "所有要进行预整定的回路id列表", "items": {"type": "number"}}}, "required": ["loopIdList"]}]}, "result": {"type": "object", "description": "整定结果列表", "properties": {"resultList": {"type": "array", "description": "整定结果列表", "items": {"type": "object", "properties": {"loopName": {"type": "string", "description": "回路名称"}, "pidParams": {"type": "array", "description": "整定后的PID参数", "items": {"type": "string"}}, "message": {"type": "string", "description": "整定后消息"}}}}}, "required": ["resultList"]}, "catalog": "optimization"}, {"name": "human_tuning", "alias": ["拟人化整定", "闭环回路的参数迭代整定", "基于回路波形判断的参数迭代整定"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}], "description": "对指定回路进行拟人化整定", "params": {"type": "object", "description": "拟人化整定的输入参数，其中回路名称(loopName)、回路所属装置id(groupId)为必填，其余参数可选", "properties": {"loopId": {"type": "number", "description": "回路id"}, "startTime": {"type": "string", "description": "历史数据选取起始时间，yyyy-MM-dd HH:mm:ss格式"}, "endTime": {"type": "string", "description": "历史数据选取截止时间，yyyy-MM-dd HH:mm:ss格式"}, "errorTol": {"type": "number", "description": "允许偏差绝对值，默认值0.03，表示允许测量值与设定值的偏差绝对值"}}, "required": ["loopId"]}, "result": {"type": "object", "description": "整定结果", "properties": {"pidParams": {"type": "string", "description": "整定后的PID参数"}, "message": {"type": "string", "description": "整定状态，如果整定失败，会给出失败原因"}}, "required": ["pidParams", "message"]}, "catalog": "optimization"}, {"skip_summary": true, "name": "guide_tuning", "alias": ["智能向导整定", "多策略自适应整定"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}], "dependencies": {"file": [{"category": {"name": "f_loop_history", "alias": "回路历史数据"}, "required": false}]}, "description": "向导整定，会根据回路的历史数据进行回路状态检测，根据检测算法返回的建议采用不同的整定算法。能力内部实现逻辑：首先进行输入数据的基本校验，确保SV/MV量程范围及PV/SV/MV质量码的合法性。随后通过数据分段处理识别有效数据段，并计算关键指标：自控率反映控制回路投入自动的比例，平稳率评估设定值跟踪的稳定性，阀门饱和率检测执行机构是否频繁达到极限，激励指数衡量过程激励的充分性，以及振荡指数量化控制回路的振荡程度。函数根据这些指标的组合情况，结合控制模式（手动/自动）、质量码状态、PID参数是否在合理范围内等条件，通过多层嵌套的条件分支生成具体的整定建议和运行状态评估。例如，当初始状态标志为真且自控率过低时建议专家库整定；阀门饱和率过高时区分手动/自动模式给出不同提示；激励不足时结合控制模式提供针对性建议；对于高平稳率或高振荡的情况则分别推荐优化或调整策略。最终输出包含分段信息、中间变量、整定建议和状态评估，为PID参数优化提供全面依据。所有临时内存均在函数末尾释放，确保资源管理安全。", "pre_analysis": false, "verbose": false, "params": {"type": "object", "description": "向导整定的输入参数", "properties": {"loopId": {"type": "number", "description": "回路id"}}, "required": ["loopId"]}, "result": {"type": "object", "description": "检测结果建议，如果可整定，也会返回整定结果", "properties": {"adviceMsg": {"type": "string", "description": "建议消息"}, "tuningResult": {"type": "object", "description": "整定结果"}, "excitCoef": {"type": "number", "description": "激励指数"}, "isInitialState": {"type": "boolean", "description": "是否为初始状态"}, "closeRate": {"type": "number", "description": "自控率"}, "stableRate": {"type": "number", "description": "平稳率"}, "valveSaturation": {"type": "number", "description": "阀门饱和率"}, "oscillateIndex": {"type": "number", "description": "振荡指数"}, "controlMode": {"type": "number", "description": "开闭环状态 0手动、1或2自动、20未知", "enum": [0, 1, 2, 20]}}, "required": ["adviceMsg", "isInitialState", "closeRate", "stableRate", "valveSaturation", "oscillateIndex", "controlMode"]}, "catalog": "optimization"}, {"skip_summary": true, "name": "data_validation", "alias": ["回路数据校验", "数据校验"], "related_app": [{"name": "loop_tuning", "alias": "回路整定"}, {"name": "loop_info_query", "alias": "回路信息查询"}], "dependencies": {"file": [{"category": {"name": "f_loop_history", "alias": "回路历史数据"}}]}, "description": "数据校验能力，用来检测指定回路近期的历史数据是否有效", "params": {"type": "object", "description": "数据校验的输入参数", "properties": {"loopId": {"type": "number", "description": "回路id"}}, "required": ["loopId"]}, "result": {"type": "object", "description": "检测结果，valid字段为True时表示数据有效，否则表示数据无效，数据无效不影响后续能力的执行", "properties": {"valid": {"type": "boolean", "description": "给定回路近期的历史数据是否有效"}}, "required": ["valid"]}, "catalog": "evaluation"}, {"skip_summary": true, "name": "get_loop_2h_report_diagnosis", "alias": ["控制回路性能评估及诊断"], "related_app": [{"name": "loop_evaluation", "alias": "回路评估"}], "description": "控制回路性能评估及诊断能力，对控制回路的运行性能进行多维度评价，给出综合评分(comprehensiveScore)、综合评级(comprehensiveClass)，并给出相关运维建议(suggestion)、给出振荡检测(isValveOscillate)和阀门粘滞检测(isValveStic)", "params": {"type": "object", "description": "控制回路性能评估及诊断的输入参数", "properties": {"loopName": {"type": "string", "description": "回路名称，全字匹配"}, "groupId": {"type": "number", "description": "回路所属装置id"}}, "required": ["loopName", "groupId"]}, "result": {"type": "object", "description": "诊断结果，包含综合评分、综合评级、运维建议、阀门振荡检测(True为振荡，False为平稳)和阀门粘滞检测(True为粘滞，False为不粘滞)", "properties": {"comprehensiveScore": {"type": "number", "description": "综合评分"}, "comprehensiveClass": {"type": "number", "description": "综合评级 枚举项按顺序分别表示优、良、中、差、开环、条件剔除、回路异常", "enum": [1, 2, 3, 4, 5, 6, 7]}, "suggestion": {"type": "string", "description": "运维建议"}, "isValveOscillate": {"type": "boolean", "description": "阀门是否振荡，True为振荡，False为平稳"}, "isValveStic": {"type": "boolean", "description": "阀门是否粘滞，True为粘滞，False为不粘滞"}, "valveTralve": {"type": "number", "description": "阀门行程"}}, "required": []}, "catalog": "evaluation"}, {"name": "get_auto_tuning_records", "alias": ["自动整定结果查询"], "description": "获取指定回路自整定记录的能力，回路所属装置id、查询开始时间和查询截止时间是可选参数", "params": {"type": "object", "description": "获取指定回路自整定记录的输入参数", "properties": {"loopName": {"type": "string", "description": "回路名称，可模糊匹配"}, "groupId": {"type": "number", "description": "回路所属装置id，可选参数"}, "startTime": {"type": "string", "description": "自整定记录选取起始时间，yyyy-MM-dd HH:mm:ss格式"}, "endTime": {"type": "string", "description": "自整定记录选取截止时间，yyyy-MM-dd HH:mm:ss格式"}}, "required": ["loopName"]}, "result": {"type": "object", "description": "自整定记录", "properties": {"loopName": {"type": "string", "description": "回路名称"}, "pidParaBeforeTuning": {"type": "string", "description": "整定前PID参数"}, "pidParaAfterTuning": {"type": "string", "description": "整定后PID参数"}, "advice": {"type": "string", "description": "整定建议"}, "starsOfGrade": {"type": "number", "description": "整定效果评分(星级)"}}, "required": ["loopName", "pidParaBeforeTuning", "pidParaAfterTuning", "advice", "starsOfGrade"]}, "catalog": "statistics"}, {"name": "loop_correlation_analysis", "alias": ["回路相关性分析"], "description": "回路相关性分析能力，可以反映待分析位号和其他指定位号的相关性", "params": {"type": "object", "description": "回路相关性分析能力的输入参数", "properties": {"targetTagName": {"type": "string", "description": "待分析位号名称"}, "correlationTagNames": {"type": "object", "description": "其他指定位号的名称列表"}, "couplingDuration": {"type": "number", "description": "耦合时长阈值，默认值为0.3"}, "startTime": {"type": "string", "description": "查询起始时间，yyyy-MM-dd HH:mm:ss格式"}, "endTime": {"type": "string", "description": "查询截止时间，yyyy-MM-dd HH:mm:ss格式"}}, "required": ["targetTagName", "correlationTagNames", "couplingDuration", "startTime", "endTime"]}, "result": {"type": "object", "description": "回路相关性分析结果", "properties": {"correlationDurationList": {"type": "object", "description": "耦合作用时长对比图"}, "correlationIndexTrend": {"type": "object", "description": "回路相关性强度分析图"}, "mainTagName": {"type": "string", "description": "目标分析位号"}}, "required": ["correlationDurationList", "correlationIndexTrend", "mainTagName"]}, "catalog": "evaluation"}, {"name": "get_loop_history_close_stable_rate", "alias": ["回路历史自控率、平稳率计算", "回路历史自控平稳率计算"], "related_app": [{"name": "loop_evaluation", "alias": "回路评估"}], "description": "获取指定回路的历史自控率、平稳率", "params": {"type": "object", "description": "获取指定回路的实时运行状态的输入参数", "properties": {"loopName": {"type": "string", "description": "回路名称"}, "groupId": {"type": "number", "description": "回路所属装置id"}, "startTime": {"type": "string", "description": "回路实时报告选取起始时间，yyyy-MM-dd HH:mm格式"}, "endTime": {"type": "string", "description": "回路实时报告选取截止时间，yyyy-MM-dd HH:mm格式"}, "reportType": {"type": "number", "description": "报告类型 0实时报告 1日报 2周报 3月报 请按问题需求来选择报告类型，例如：最近x小时或某一天就选实时报告，本周的就选日报，本月的就选周报，最近几个月的就选月报", "enum": [0, 1, 2, 3]}}, "required": ["loopName", "groupId", "startTime", "endTime", "reportType"]}, "result": {"type": "object", "description": "指定回路的历史自控率、平稳率", "properties": {"closeRate": {"type": "number", "description": "自控率"}, "stableRate": {"type": "number", "description": "平稳率"}}, "required": ["closeRate", "stableRate"]}, "catalog": "evaluation"}]}