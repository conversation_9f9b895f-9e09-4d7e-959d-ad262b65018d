use opentelemetry::{KeyValue, global, trace::<PERSON>r<PERSON><PERSON>ider as _};
use opentelemetry_sdk::{
    Resource,
    metrics::{MeterProviderBuilder, PeriodicReader, SdkMeterProvider},
    trace::{RandomIdGenerator, <PERSON><PERSON>, Sdk<PERSON>racer<PERSON>rovider},
};
use opentelemetry_semantic_conventions::{
    SCHEMA_URL,
    attribute::{DEPLOYMENT_ENVIRONMENT_NAME, SERVICE_NAME, SERVICE_VERSION},
};

use tracing_opentelemetry::{MetricsLayer, OpenTelemetryLayer};
use tracing_subscriber::{
    EnvFilter, Layer as _, layer::SubscriberExt as _, util::SubscriberInitExt as _,
};

// Create a Resource that captures information about the entity for which telemetry is recorded.
fn resource() -> Resource {
    Resource::builder()
        .with_service_name(env!("CARGO_PKG_NAME"))
        .with_schema_url(
            [
                KeyValue::new(SERVICE_NAME, env!("CARGO_PKG_NAME")),
                KeyValue::new(SERVICE_VERSION, env!("CARGO_PKG_VERSION")),
                KeyValue::new(DEPLOYMENT_ENVIRONMENT_NAME, "develop"),
            ],
            SCHEMA_URL,
        )
        .build()
}

// Create an EnvFilter that sets nacos_sdk to INFO level while keeping others at DEBUG
fn create_log_filter() -> EnvFilter {
    EnvFilter::builder()
        .with_env_var("TOOLS_SERVER_LOG")
        .try_from_env()
        .unwrap_or_else(|_| EnvFilter::new("debug,nacos_sdk=info,h2=info,tower=info"))
}

// Construct MeterProvider for MetricsLayer
fn init_meter_provider() -> SdkMeterProvider {
    let exporter = opentelemetry_otlp::MetricExporter::builder()
        .with_tonic()
        .with_temporality(opentelemetry_sdk::metrics::Temporality::default())
        .build()
        .unwrap();

    let reader = PeriodicReader::builder(exporter)
        .with_interval(std::time::Duration::from_secs(30))
        .build();

    // For debugging in development
    let stdout_reader =
        PeriodicReader::builder(opentelemetry_stdout::MetricExporter::default()).build();

    let meter_provider = MeterProviderBuilder::default()
        .with_resource(resource())
        .with_reader(reader)
        .with_reader(stdout_reader)
        .build();

    global::set_meter_provider(meter_provider.clone());

    meter_provider
}

// Construct TracerProvider for OpenTelemetryLayer
fn init_tracer_provider() -> SdkTracerProvider {
    let exporter = opentelemetry_otlp::SpanExporter::builder()
        .with_tonic()
        .build()
        .unwrap();

    SdkTracerProvider::builder()
        // Customize sampling strategy
        .with_sampler(Sampler::ParentBased(Box::new(Sampler::TraceIdRatioBased(
            1.0,
        ))))
        // If export trace to AWS X-Ray, you can use XrayIdGenerator
        .with_id_generator(RandomIdGenerator::default())
        .with_resource(resource())
        .with_batch_exporter(exporter)
        .build()
}

// Initialize tracing-subscriber and return OtelGuard for opentelemetry-related termination processing
pub fn init_remote_tracing() -> OtelGuard {
    let tracer_provider = init_tracer_provider();
    let meter_provider = init_meter_provider();

    let tracer = tracer_provider.tracer("tracing-otel-subscriber");

    let filter = create_log_filter();

    tracing_subscriber::registry()
        // The global level filter prevents the exporter network stack
        // from reentering the globally installed OpenTelemetryLayer with
        // its own spans while exporting, as the libraries should not use
        // tracing levels below DEBUG. If the OpenTelemetry layer needs to
        // trace spans and events with higher verbosity levels, consider using
        // per-layer filtering to target the telemetry layer specifically,
        // e.g. by target matching.
        .with(filter)
        .with(tracing_subscriber::fmt::layer())
        .with(MetricsLayer::new(meter_provider.clone()))
        .with(OpenTelemetryLayer::new(tracer))
        .init();

    OtelGuard {
        tracer_provider,
        meter_provider,
    }
}

pub struct OtelGuard {
    tracer_provider: SdkTracerProvider,
    meter_provider: SdkMeterProvider,
}

impl Drop for OtelGuard {
    fn drop(&mut self) {
        if let Err(err) = self.tracer_provider.shutdown() {
            eprintln!("{err:?}");
        }
        if let Err(err) = self.meter_provider.shutdown() {
            eprintln!("{err:?}");
        }
    }
}

#[allow(dead_code)]
pub struct FileGuard {
    debug_guard: tracing_appender::non_blocking::WorkerGuard,
    warning_guard: tracing_appender::non_blocking::WorkerGuard,
}

pub fn start_tracing(save_files: bool) -> FileGuard {
    let binding = std::env::current_exe().unwrap();
    let name = binding.file_stem().unwrap_or_default().to_string_lossy();

    let debug_file = tracing_appender::rolling::Builder::new()
        .rotation(tracing_appender::rolling::Rotation::HOURLY)
        .filename_prefix(format!("{name}-debug"))
        .filename_suffix("log")
        .build(".runtime/logs")
        .unwrap();

    let warning_file = tracing_appender::rolling::Builder::new()
        .rotation(tracing_appender::rolling::Rotation::HOURLY)
        .filename_prefix(format!("{name}-warning"))
        .filename_suffix("log")
        .build(".runtime/logs")
        .unwrap();

    let (debug_file_writer, debug_guard) = tracing_appender::non_blocking(debug_file);

    let (warning_file_writer, warning_guard) = tracing_appender::non_blocking(warning_file);

    let filter = create_log_filter();

    let terminal_layer = tracing_subscriber::fmt::layer()
        .with_ansi(true)
        .with_line_number(true)
        // .with_target(false)
        .with_span_events(tracing_subscriber::fmt::format::FmtSpan::CLOSE)
        .with_filter(filter);

    if save_files {
        let debug_file_layer = tracing_subscriber::fmt::layer()
            .with_ansi(false)
            .with_target(false)
            .with_span_events(tracing_subscriber::fmt::format::FmtSpan::CLOSE)
            .with_writer(debug_file_writer)
            .with_filter(tracing_subscriber::filter::LevelFilter::DEBUG);

        let warning_file_layer = tracing_subscriber::fmt::layer()
            .with_ansi(false)
            .with_target(false)
            .with_span_events(tracing_subscriber::fmt::format::FmtSpan::CLOSE)
            .with_writer(warning_file_writer)
            .with_filter(tracing_subscriber::filter::LevelFilter::WARN);

        tracing_subscriber::registry()
            .with(terminal_layer)
            .with(debug_file_layer)
            .with(warning_file_layer)
            .init();
    } else {
        tracing_subscriber::registry().with(terminal_layer).init();
    };

    FileGuard {
        debug_guard,
        warning_guard,
    }
}
