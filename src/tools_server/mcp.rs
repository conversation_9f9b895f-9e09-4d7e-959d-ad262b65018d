//! Model Context Protocol (MCP) Service Implementation
//! Provides standard MCP service interfaces, supporting tool integration and capability invocation

use std::sync::Arc;

use axum::{
    Extension, Router,
    extract::Path,
    middleware::{Next, from_fn},
    response::Response,
};
use rmcp::{
    <PERSON><PERSON>erver, ServerHandler,
    model::{
        CallToolRequestParam, CallToolResult, Content, ListToolsResult, PaginatedRequestParam,
        ServerInfo,
    },
    service::RequestContext,
    transport::{
        StreamableHttpService, common::http_header::HEADER_SESSION_ID,
        streamable_http_server::session::local::LocalSessionManager,
    },
};

use tool::{
    global::GlobalToolRepository,
    repository::{CallToolInput, ToolRepository},
};

use crate::auth::Claims;

/// Create service router
///
/// Supports global capability routing (/api/mcp/) and module-specific routing (/api/mcp/{module})
#[tracing::instrument(skip_all)]
pub fn router(global_repository: Arc<GlobalToolRepository>) -> Router {
    let service = StreamableHttpService::new(
        move || {
            Ok(McpService {
                global_repository: global_repository.clone(),
                info: ServerInfo::default(),
            })
        },
        LocalSessionManager::default().into(),
        Default::default(),
    );

    Router::new()
        .route_service("/", service.clone())
        .route_service("/{module_name}", service)
        .layer(from_fn(request_info))
}

#[derive(Clone, Default)]
pub struct RequestInfoExtension {
    pub module_name: Option<String>,
    pub tenant_id: String,
    pub user_id: String,
    pub session_id: String,
}

// middleware for capturing request info
async fn request_info(
    Path(module_name): Path<Option<String>>,
    Extension(claims): Extension<Claims>,
    mut request: axum::extract::Request,
    next: Next,
) -> Response {
    let session_id = request
        .headers()
        .get(HEADER_SESSION_ID)
        .and_then(|v| v.to_str().ok())
        .unwrap_or_default()
        .to_string();

    request.extensions_mut().insert(RequestInfoExtension {
        module_name,
        tenant_id: claims.tenant_id(),
        user_id: claims.user_id(),
        session_id,
    });

    next.run(request).await
}

/// Session module router
#[derive(Clone)]
struct McpService {
    global_repository: Arc<GlobalToolRepository>,
    info: ServerInfo,
}

impl std::fmt::Debug for McpService {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("McpService").finish()
    }
}

/// Implements routing functionality for the MCP server
impl ServerHandler for McpService {
    fn get_info(&self) -> ServerInfo {
        self.info.clone()
    }

    /// List available tools
    #[tracing::instrument]
    async fn list_tools(
        &self,
        request: Option<PaginatedRequestParam>,
        mut context: RequestContext<RoleServer>,
    ) -> Result<ListToolsResult, rmcp::ErrorData> {
        let info = context
            .extensions
            .get_mut::<axum::http::request::Parts>()
            .and_then(|p| p.extensions.get_mut::<RequestInfoExtension>().cloned());

        let module_name = info.as_ref().and_then(|i| i.module_name.as_ref());

        let tools = match module_name {
            Some(module_name) => self
                .global_repository
                .list_module_tools(module_name)
                .await
                .map_err(|e| rmcp::ErrorData::internal_error(e.to_string(), None))?
                .into_iter()
                .map(|t| rmcp::model::Tool {
                    name: t.name().to_owned().into(),
                    description: Some(t.description().to_owned().into()),
                    input_schema: match t.params_schema().as_object() {
                        Some(schema) => schema.to_owned().into(),
                        None => serde_json::Map::new().into(),
                    },
                    annotations: None,
                })
                .collect(),
            None => self
                .global_repository
                .list_tools()
                .await
                .map_err(|e| rmcp::ErrorData::internal_error(e.to_string(), None))?
                .into_iter()
                .map(|t| rmcp::model::Tool {
                    name: format!("{}__{}", t.module_name(), t.name()).into(),
                    description: Some(t.description().to_owned().into()),
                    input_schema: match t.params_schema().as_object() {
                        Some(schema) => schema.to_owned().into(),
                        None => serde_json::Map::new().into(),
                    },
                    annotations: None,
                })
                .collect(),
        };

        Ok(ListToolsResult {
            next_cursor: None,
            tools,
        })
    }

    /// Call specified tool
    #[tracing::instrument(skip_all)]
    async fn call_tool(
        &self,
        request: CallToolRequestParam,
        mut context: RequestContext<RoleServer>,
    ) -> Result<CallToolResult, rmcp::ErrorData> {
        let info = context
            .extensions
            .get_mut::<axum::http::request::Parts>()
            .and_then(|p| p.extensions.get_mut::<RequestInfoExtension>().cloned())
            .unwrap_or_default();

        let output = match info.module_name {
            Some(module_name) => {
                self.global_repository
                    .call_tool(CallToolInput {
                        tenant_id: info.tenant_id,
                        user_id: info.user_id,
                        session_id: info.session_id,
                        executation_id: "".to_string(),
                        module_name,
                        tool_name: request.name.to_string(),
                        metadata: None,
                        payload: request.arguments.unwrap_or_default().into(),
                        temp_view_sender: None,
                    })
                    .await
            }
            None => {
                let (module_name, tool_name) = request.name.split_once("__").ok_or_else(|| {
                    rmcp::ErrorData::internal_error("Invalid tool name".to_string(), None)
                })?;

                self.global_repository
                    .call_tool(CallToolInput {
                        tenant_id: info.tenant_id,
                        user_id: info.user_id,
                        session_id: info.session_id,
                        executation_id: "".to_string(),
                        module_name: module_name.to_owned(),
                        tool_name: tool_name.to_owned(),
                        metadata: None,
                        payload: request.arguments.unwrap_or_default().into(),
                        temp_view_sender: None,
                    })
                    .await
            }
        }
        .map_err(|e| rmcp::ErrorData::internal_error(e.to_string(), None))?;

        Ok(CallToolResult::success(vec![Content::json(
            output.payload,
        )?]))
    }
}
