use std::sync::Arc;

use axum::{Extension, Json, Router, extract::State, http::StatusCode, routing::post};
use axum_jrpc::{
    Jrp<PERSON><PERSON><PERSON><PERSON>, JsonRpcExtractor, JsonRpcResponse,
    error::{<PERSON><PERSON>Rp<PERSON><PERSON><PERSON><PERSON>, JsonRpcErrorReason},
};
use tool::{
    global::GlobalToolRepository,
    repository::{CallToolInput, ToolRepository},
};

use crate::auth::Claims;

pub fn router(global_repository: Arc<GlobalToolRepository>) -> Router {
    Router::new()
        .route("/", post(rpc).get(list_rpc))
        .with_state(global_repository.clone())
}

#[tracing::instrument(skip(global_repository))]
async fn rpc(
    State(global_repository): State<Arc<GlobalToolRepository>>,
    Extension(claims): Extension<Claims>,
    JsonRpcExtractor {
        id,
        parsed: params,
        method,
    }: JsonRpcExtractor,
) -> JrpcResult {
    let (module_name, tool_name) = method.split_once("__").ok_or_else(|| {
        JsonRpcResponse::error(
            id.clone(),
            JsonRpcError::new(
                JsonRpcErrorReason::MethodNotFound,
                format!("Invalid method: {method}"),
                serde_json::Value::Null,
            ),
        )
    })?;

    let result = global_repository
        .call_tool(CallToolInput {
            tenant_id: claims.tenant_id(),
            user_id: claims.user_id(),
            executation_id: "".to_string(),
            module_name: module_name.to_string(),
            tool_name: tool_name.to_string(),
            session_id: serde_json::to_string(&id).unwrap_or_default(),
            metadata: None,
            payload: params,
            temp_view_sender: None,
        })
        .await;

    match result {
        Ok(output) => Ok(JsonRpcResponse::success(id, output.payload)),
        Err(e) => Ok(JsonRpcResponse::error(
            id,
            JsonRpcError::new(
                JsonRpcErrorReason::InternalError,
                e.to_string(),
                serde_json::Value::Null,
            ),
        )),
    }
}

async fn list_rpc(
    State(global_repository): State<Arc<GlobalToolRepository>>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<serde_json::Value>)> {
    let tools = match global_repository.list_tools().await {
        Ok(tools) => tools,
        Err(e) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(serde_json::json!({
                    "error": e.to_string(),
                })),
            ));
        }
    };

    Ok(Json(serde_json::json!({
        "tools": tools,
    })))
}
