use std::{pin::Pin, sync::Arc};

use axum::{
    Extension, Json, Router,
    extract::{DefaultBodyLimit, Path, Query, State, WebSocketUpgrade, ws::CloseFrame},
    response::{
        IntoResponse,
        sse::{Event, Sse},
    },
    routing::{delete, get, post},
};
use bson::doc;
use entity::{COUNT, FACET, ID, LIMIT, MATCH, OPTIONS, PROJECT, REGEX, SET, SKIP, SORT};
use futures::{SinkExt, Stream, StreamExt as _, TryStreamExt as _};
use ractor::{MessagingErr, RactorErr};
use serde::{Deserialize, Serialize};
use shared_core::conversation::{
    ConversationListItem, ConversationListRequest, ConversationListResponse,
    ConversationStreamEvent, ImageInfo, InteractionRequest, StreamRequest,
    actor::Conversation<PERSON>ctorE<PERSON>, manager::ConversationManager,
};
use tracing::Instrument;
use uuid::Uuid;

use crate::auth::Claims;
use crate::service::{ErrExt, Error, Response, ResponseExt};

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct DeleteCondition {
    #[serde(default)]
    ids: Vec<Uuid>,
    #[serde(default, with = "time::serde::rfc3339::option")]
    start_time: Option<time::OffsetDateTime>,
    #[serde(default, with = "time::serde::rfc3339::option")]
    end_time: Option<time::OffsetDateTime>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct UpdateConversationRequest {
    pub id: Uuid,
    pub title: String,
}

/// 创建对话管理 API 路由
pub fn router(conversation_manager: Arc<ConversationManager>) -> Router {
    Router::new()
        .route(
            "/",
            get(list_conversations)
                .post(create_conversation)
                .put(update_conversation),
        )
        .route("/{id}", delete(delete_conversation))
        .route("/{id}/break", post(break_conversation))
        .route("/all", delete(delete_all_conversation))
        .route("/status", get(conversation_status_ws))
        .route("/delete", post(delete_conversations_by_condition))
        .route(
            "/{id}/stream",
            post(stream_conversation_sse).fallback(stream_conversation_ws),
        )
        .with_state(conversation_manager)
}

pub fn open_router(conversation_manager: Arc<ConversationManager>) -> Router {
    Router::new()
        .route("/{id}", get(get_conversation_by_id))
        .route(
            "/{id}/interaction",
            post(handle_interaction).layer(DefaultBodyLimit::max(16 * 1024 * 1024)),
        )
        .with_state(conversation_manager)
}

// GET /api/conversation - 获取对话列表
async fn list_conversations(
    Query(query): Query<ConversationListRequest>,
    State(manager): State<Arc<ConversationManager>>,
    Extension(claims): Extension<Claims>,
) -> Response {
    let title = query.title;
    let limit = query.limit.unwrap_or(100);
    let offset = (query.offset.unwrap_or(1) - 1) * limit;

    let mut r#match = doc! {
        entity::document::ConversationField::UserId.as_str():claims.user_id(),
        entity::document::ConversationField::TenantId.as_str():claims.tenant_id(),
    };

    if title.is_some() && !title.as_ref().unwrap().is_empty() {
        r#match.insert(
            entity::document::ConversationField::Title.as_str(),
            doc! {
                REGEX: format!("{}", title.clone().unwrap()),
                OPTIONS: "i",
            },
        );
    }

    let r#match = doc! {
        MATCH: r#match,
    };

    let facet = doc! {
        FACET:{
            "metadata":[
                { COUNT: "total" }
            ],
            "data":[
                { SORT: { ID: -1 } },
                { SKIP: offset as i64},
                { LIMIT: limit as i64},
            ]
        }
    };

    let project = doc! {
        PROJECT:{
            "data": "$data",
            "total": { "$arrayElemAt": ["$metadata.total", 0] }
        }
    };

    let pipeline = [r#match, facet, project];

    let result = manager
        .runner()
        .document_repository
        .aggregate::<entity::document::Conversation>(pipeline)
        .await
        .map_err(|e| {
            tracing::error!("获取对话列表失败: {:?}", e);
            Error::bad_request_error(format!("获取对话列表失败: {e}"))
        })?;

    if let Some(data) = result.first() {
        match data.get("data") {
            Some(bson::Bson::Array(arr)) => {
                let conversations = arr
                    .iter()
                    .map(|v| {
                        let conversation: entity::document::Conversation =
                            entity::from_bson!(v.clone());
                        Ok(ConversationListItem {
                            id: conversation.id,
                            title: conversation.title,
                            create_time: conversation.create_time,
                            update_time: conversation.update_time,
                            status: conversation.status.into(),
                            sub_status: conversation.sub_status.into(),
                        })
                    })
                    .collect::<error::Result<Vec<_>>>()
                    .map_err(|e| {
                        tracing::error!("转换对话列表失败: {:?}", e);
                        Error::bad_request_error(format!("转换对话列表失败: {e}"))
                    })?;
                let total = match data.get("total") {
                    Some(bson::Bson::Int32(i)) => *i as usize,
                    _ => {
                        // tracing::error!("转换对话列表总数失败");
                        0
                    }
                };
                let response = ConversationListResponse {
                    conversations,
                    limit: query.limit.unwrap_or(100),
                    offset: query.offset.unwrap_or(0),
                    total,
                };
                Response::body(response)
            }
            _ => Response::bad_request("转换对话列表失败".to_string()),
        }
    } else {
        Response::bad_request("查询失败".to_string())
    }
}

// GET /api/conversation/{id} - 获取特定对话的详细信息
async fn get_conversation_by_id(
    Path(id): Path<Uuid>,
    State(manager): State<Arc<ConversationManager>>,
) -> Response {
    match manager.get_conversation(&id).await {
        Ok(conversation) => {
            let conversation = conversation.read().await;
            Response::body(conversation.clone())
        }
        Err(e) => {
            tracing::error!("加载对话失败: {e:?}");
            Response::not_found(format!("加载对话失败: {id:?},{e:?}"))
        }
    }
}

async fn delete_conversation(
    Path(id): Path<Uuid>,
    State(manager): State<Arc<ConversationManager>>,
) -> Response {
    use entity::document::*;
    //删除之前删除actor
    if let Some(actor) = manager.break_actor(&id).await {
        actor.kill();
    }

    let repository = manager.runner().document_repository.clone();
    match manager.delete_conversation(&id).await {
        Ok(_) => {
            // 删除对话后删除案例
            if let Some(playback) = repository
                .find_one_and_delete::<entity::document::Playback>(doc! {
                    PlaybackField::ConversationId.as_str(): id
                })
                .await
                .map_err(|e| {
                    tracing::error!("删除对话回放失败, 对话id: [{:?}]: {:?}", id, e);
                    Error::bad_request_error(format!("删除对话回放失败: {e}"))
                })?
            {
                let deleted_images: Vec<ImageInfo> =
                    bson::from_bson(playback.images).map_err(|e| {
                        tracing::error!("解析对话回放图片失败: {:?}", e);
                        Error::bad_request_error(format!("解析对话回放图片失败: {e}"))
                    })?;

                if !deleted_images.is_empty() {
                    let objects_to_delete = deleted_images
                        .into_iter()
                        .map(|info| (info.object, info.version_id))
                        .collect::<Vec<_>>();
                    manager
                        .runner()
                        .resource_manager
                        .delete_files(
                            resource::PLAYBACK_BUCKET_NAME.to_string(),
                            objects_to_delete,
                        )
                        .await
                        .map_err(|e| {
                            tracing::error!("删除对话回放图片失败: {:?}", e);
                            Error::bad_request_error(format!("删除对话回放图片失败: {e}"))
                        })?;
                }
            }
            Response::success()
        }
        Err(e) => {
            tracing::error!("删除对话失败: {:?}", e);
            Response::bad_request(format!("删除对话失败: {e}"))
        }
    }
}

async fn delete_conversations_by_condition(
    State(manager): State<Arc<ConversationManager>>,
    Json(condition): Json<DeleteCondition>,
) -> Response {
    match manager
        .delete_conversation_by_condition(
            condition.ids,
            condition.start_time.zip(condition.end_time),
        )
        .await
    {
        Ok(_) => Response::success(),
        Err(e) => {
            tracing::error!("删除对话失败: {e}");
            Response::bad_request(format!("删除对话失败: {e}"))
        }
    }
}

async fn update_conversation(
    State(manager): State<Arc<ConversationManager>>,
    Json(conversation): Json<UpdateConversationRequest>,
) -> Response {
    use entity::document::*;

    let now = time::OffsetDateTime::now_utc();
    match manager.get_conversation(&conversation.id).await {
        Ok(conversation_lock) => {
            let mut conversation_lk = conversation_lock.write().await;
            conversation_lk.title = conversation.title.clone();
            conversation_lk.update_time = Some(now);
        }
        Err(e) => {
            tracing::error!("更新conversation失败: {e:?}");
            return Response::bad_request(format!("更新conversation失败: {e:?}"));
        }
    }

    match manager
        .runner()
        .document_repository
        .update_one::<entity::document::Conversation>(
            doc! {ID: conversation.id},
            doc! {
                SET:{
                    ConversationField::Title.as_str(): conversation.title.clone(),
                    ConversationField::UpdateTime.as_str(): now,
                }
            },
        )
        .await
    {
        Ok(_) => Response::success(),
        Err(e) => {
            tracing::error!("更新对话 {} 失败: {e}", conversation.id);
            Response::bad_request(format!("更新对话 {} 失败: {e}", conversation.id))
        }
    }
}

async fn delete_all_conversation(
    State(manager): State<Arc<ConversationManager>>,
    Extension(claims): Extension<Claims>,
) -> Response {
    // 删除该用户所有的对话，删除之前删除actor
    manager.get_all_actor().await.iter().for_each(|actor| {
        actor.kill();
    });
    match manager
        .delete_all_conversation(claims.user_id(), claims.tenant_id())
        .await
    {
        Ok(_) => Response::success(),
        Err(e) => {
            tracing::error!("删除全部对话失败: {:?}", e);
            Response::bad_request(format!("删除全部对话失败: {e}"))
        }
    }
}

// POST /api/conversation - 创建新对话
async fn create_conversation(
    State(manager): State<Arc<ConversationManager>>,
    Extension(claims): Extension<Claims>,
) -> Response {
    let conversation_lock = manager
        .create_conversation(
            None,
            claims.user_id(),
            claims.tenant_id(),
            claims.user_info.user.user_name,
        )
        .await;
    let conversation = conversation_lock.read().await;
    Response::body(conversation.clone())
}

// POST /api/conversation/{id}/break - 中断当前对话
async fn break_conversation(
    Path(id): Path<Uuid>,
    State(manager): State<Arc<ConversationManager>>,
) -> Response {
    match manager.break_actor(&id).await {
        Some(_) => Response::success(),
        None => Response::not_found(format!("对话 {id} 不存在")),
    }
}

// POST /api/conversation/{id}/interaction - 处理用户交互
async fn handle_interaction(
    Path(id): Path<Uuid>,
    State(manager): State<Arc<ConversationManager>>,
    Json(interaction): Json<InteractionRequest>,
) -> Response {
    match manager.get_actor(&id).await {
        Some(actor) => {
            let result = ractor::call!(actor, ConversationActorEvent::Interaction, interaction);

            match result {
                Ok(Ok(())) => Response::success(),
                Ok(Err(e)) => Response::not_found(format!("{e}")),
                Err(e) => {
                    if let RactorErr::Messaging(MessagingErr::ChannelClosed) = e {
                        // openPage的按钮在对话结束以后点击会出发这个错误
                        return Response::bad_request("请勿重复操作".to_string());
                    }
                    Response::bad_request(format!("{e}"))
                }
            }
        }
        None => Response::not_found(format!("对话 {id} 不存在")),
    }
}

type ConversationStream =
    Pin<Box<dyn Stream<Item = Result<ConversationStreamEvent, error::Error>> + Send + 'static>>;

// POST /api/conversation/{id}/stream - 实时对话流
async fn stream_conversation_sse(
    Path(id): Path<Uuid>,
    State(manager): State<Arc<ConversationManager>>,
    Json(request): Json<StreamRequest>,
) -> Result<axum::response::Response, (axum::http::StatusCode, Json<serde_json::Value>)> {
    match manager.get_actor(&id).await {
        Some(actor) => {
            let sender = manager.ensure_channel(&id).await;
            let receiver = sender.subscribe();

            let build = async move |with_full: bool| -> Result<
                axum::response::Response,
                (axum::http::StatusCode, Json<serde_json::Value>),
            > {
                let stream = tokio_stream::wrappers::BroadcastStream::new(receiver);

                let stream: ConversationStream = if with_full {
                    let conversation = match manager.get_conversation(&id).await {
                        Ok(conversation) => conversation,
                        Err(e) => {
                            return Err((
                                axum::http::StatusCode::NOT_FOUND,
                                Json(
                                    serde_json::json!({ "error": format!("对话 {} 不存在: {e:?}", id) }),
                                ),
                            ));
                        }
                    };

                    let conversation = conversation.read().await.clone();

                    Box::pin(
                        futures::stream::once(async move {
                            error::Result::Ok(ConversationStreamEvent::Full(conversation))
                        })
                        .chain(stream.map_err(error::Error::from)),
                    )
                } else {
                    Box::pin(stream.map_err(error::Error::from))
                };

                let stream = stream.take_while(|event| {
                    futures::future::ready(match event {
                        Ok(event) => !event.is_ready(),
                        _ => false,
                    })
                });

                Ok(Sse::new(stream.map(|item| match item {
                    Ok(event) => Event::default().event("data").json_data(&event),
                    Err(err) => Err(axum::Error::new(err)),
                }))
                .into_response())
            };

            match request {
                StreamRequest::UserInput(user_input) => {
                    let _ = actor.send_message(ConversationActorEvent::UserInput(user_input));

                    build(false).await
                }
                StreamRequest::WorkflowInput(workflow_input) => {
                    let _ =
                        actor.send_message(ConversationActorEvent::WorkflowInput(workflow_input));

                    build(false).await
                }
                StreamRequest::Continue(_) => build(true).await,
            }
        }
        None => Err((
            axum::http::StatusCode::NOT_FOUND,
            Json(serde_json::json!({ "error": format!("对话 {} 不存在", id) })),
        )),
    }
}

async fn stream_conversation_ws(
    Path(id): Path<Uuid>,
    Extension(claims): Extension<Claims>,
    State(manager): State<Arc<ConversationManager>>,
    ws: WebSocketUpgrade,
) -> axum::response::Response {
    let sender = manager.ensure_channel(&id).await;

    let receiver = sender.subscribe();

    let build = async move |with_full: bool,
                            first_message: Option<ConversationActorEvent>|
                -> error::Result<ConversationStream> {
        let actor = match manager.ensure_actor(id).await {
            Ok(actor) => actor,
            Err(e) => return Err(e),
        };

        let stream = tokio_stream::wrappers::BroadcastStream::new(receiver);

        let stream: ConversationStream = if with_full {
            let conversation = match manager.get_conversation(&id).await {
                Ok(conversation) => conversation,
                Err(e) => {
                    tracing::error!("加载对话失败: {e:?}");
                    return Err(error::MongoError::RecordNotFound(id).into());
                }
            };

            let conversation = conversation.read().await.clone();

            Box::pin(
                futures::stream::once(async move {
                    error::Result::Ok(ConversationStreamEvent::Full(conversation))
                })
                .chain(stream.map_err(error::Error::from)),
            )
        } else {
            Box::pin(stream.map_err(error::Error::from))
        };

        if let Some(message) = first_message {
            let _ = actor.send_message(message);
        }

        let stream: ConversationStream = Box::pin(stream.take_while(|event| {
            futures::future::ready(match event {
                Ok(event) => !event.is_ready(),
                _ => false,
            })
        }));

        Ok(stream)
    };

    let on_upgrade = async move |mut socket: axum::extract::ws::WebSocket| {
        use axum::extract::ws::Message;

        // 直到接收到第一个 TextMessage
        if let Some(message) = socket.next().await {
            let message = match message {
                Ok(message) => message,
                Err(err) => {
                    tracing::error!("接收消息失败: {}", err);
                    return;
                }
            };

            if let Message::Text(ref text) = message {
                let request = match serde_json::from_str::<StreamRequest>(text) {
                    Ok(request) => request,
                    Err(err) => {
                        tracing::error!("解析消息失败: {}", err);

                        let _ = socket
                            .send(Message::Text(
                                serde_json::to_string(&serde_json::json!({
                                "error": format ! ("{}", err)
                                }))
                                .unwrap_or_default()
                                .into(),
                            ))
                            .await;

                        return;
                    }
                };

                let stream = match request {
                    StreamRequest::UserInput(user_input) => {
                        build(false, Some(ConversationActorEvent::UserInput(user_input))).await
                    }
                    StreamRequest::WorkflowInput(workflow_input) => {
                        build(
                            false,
                            Some(ConversationActorEvent::WorkflowInput(workflow_input)),
                        )
                        .await
                    }
                    StreamRequest::Continue(_) => build(true, None).await,
                };

                let mut stream = match stream {
                    Ok(stream) => stream,
                    Err(err) => {
                        tracing::error!("构建对话流失败: {}", err);

                        let _ = socket
                            .send(Message::Text(
                                serde_json::to_string(&serde_json::json!({
                                "error": format ! ("{}", err)
                                }))
                                .unwrap_or_default()
                                .into(),
                            ))
                            .await;

                        let _ = socket
                            .send(Message::Close(Some(CloseFrame {
                                code: 4000,
                                reason: format!("error: {err}").into(),
                            })))
                            .await;

                        return;
                    }
                };

                let mut interval = tokio::time::interval(std::time::Duration::from_secs(15));

                'l: loop {
                    tokio::select! {
                        _ = interval.tick() => {
                            if let Err(e) = socket.send(Message::Ping("ping".into())).await {
                                tracing::error!("发送心跳失败: {}", e);
                                break;
                            }
                        }
                        event = stream.next() => {
                            if let Some(Ok(event)) = event {
                                if let Err(err) = socket
                                    .send(Message::Text(
                                        serde_json::to_string(&event).unwrap_or_default().into(),
                                    ))
                                    .await
                                {
                                    tracing::error!("发送消息失败: {}", err);
                                    break 'l;
                                }
                            } else {
                                tracing::debug!("stream结束，对话流结束");
                                break 'l;
                            }
                        }
                        message = socket.next() => {
                            match message {
                                Some(Ok(message)) => {
                                    if let Message::Close(frame) = message {
                                        tracing::info!("对话流关闭: {:?}", frame);
                                        if let Some(frame) = frame {
                                            if frame.code >= 4000 {
                                                let _ = socket.send(Message::Close(Some(frame))).await;
                                            }
                                        }
                                        break 'l;
                                    }
                                }
                                Some(Err(e)) => {
                                    tracing::error!("对话流异常：{e}");
                                    break 'l;
                                },
                                None => {
                                    tracing::debug!("对话流结束");
                                    break 'l;
                                }
                            }
                        }
                    }
                }

                let _ = socket
                    .send(Message::Close(Some(CloseFrame {
                        code: 4000,
                        reason: "connection done".into(),
                    })))
                    .await;
                let _ = socket.flush().await;
                let _ = socket.close().await;

                tracing::info!("对话流关闭");
            }
        }
    };

    ws.on_upgrade(async move |socket| {
        on_upgrade(socket)
            .instrument(tracing::debug_span!(
                "conversation_stream",
                id = id.to_string(),
                user_id = claims.user_id(),
                tenant_id = claims.tenant_id()
            ))
            .await;
    })
}

async fn conversation_status_ws(
    State(manager): State<Arc<ConversationManager>>,
    Extension(claims): Extension<Claims>,
    ws: WebSocketUpgrade,
) -> axum::response::Response {
    let user_id = claims.user_id();
    let tenant_id = claims.tenant_id();

    let sender = manager
        .ensure_status_channel(user_id.clone(), tenant_id.clone())
        .await;

    let mut receiver = sender.subscribe();

    ws.on_upgrade(async move |mut socket| {
        use axum::extract::ws::Message;

        let mut interval = tokio::time::interval(std::time::Duration::from_secs(15));

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    if let Err(e) = socket.send(Message::Ping("ping".into())).await {
                        tracing::error!("发送心跳失败: {}", e);
                        break;
                    }
                }
                message = socket.next() => {
                    match message {
                        Some(Ok(message)) => {
                            if let Message::Close(frame) = message {
                                tracing::info!("对话状态流关闭: {:?}", frame);
                                if let Some(frame) = frame {
                                    if frame.code >= 4000 {
                                        let _ = socket.send(Message::Close(Some(frame))).await;
                                    }
                                }
                                break;
                            }
                        }
                        Some(Err(e)) => {
                            tracing::error!("对话状态流异常：{e}");
                            break;
                        },
                        None => {
                            tracing::debug!("对话状态流结束");
                            break;
                        }
                    }
                }
                message = receiver.recv()=> {
                    match message{
                        Ok(status) => {
                            if let Err(err) = socket
                                .send(Message::Text(
                                    serde_json::to_string(&status).unwrap_or_default().into(),
                                ))
                                .await
                            {
                                tracing::error!("发送状态消息失败: {}", err);
                                break;
                            }
                            tracing::debug!("收到状态: {:?}", status);
                        },
                        Err(err) => {
                            tracing::error!("接收状态失败: {}", err);
                            break;
                        }
                    }
                }
            };
        }

        let _ = socket
            .send(Message::Close(Some(CloseFrame {
                code: 4000,
                reason: "connection done".into(),
            })))
            .await;
        let _ = socket.flush().await;
        let _ = socket.close().await;
    })
}
