use std::sync::Arc;

use ai::Chat<PERSON><PERSON><PERSON>;
use bson::doc;
use content::{
    ContentCard, ContentCardDelta, ContentDeltaOperation, ContentItem, ContentItemInner,
    ContentView, ContentViewOrText,
};
use entity::{
    ID, SET,
    document::{ContentField, ConversationField, MessageField},
};
use time::OffsetDateTime;
use tokio::sync::RwLock;

use crate::conversation::{
    Author, Conversation, ConversationStatus, ConversationStatusChangedPayload,
    ConversationStatusEvent, ConversationStreamEvent, ConversationSubStatus, Message,
    MessageContentDeltaPayload, MessageNode, MessageStatus, MessageStatusChangedPayload,
    NewMessagePayload,
};

pub struct ConversationController {
    pub inner: Arc<RwLock<Conversation>>,
    pub sender: tokio::sync::broadcast::Sender<ConversationStreamEvent>,
    pub repository: Arc<entity::DocumentRepository>,
    pub status_sender: tokio::sync::broadcast::Sender<ConversationStatusEvent>,
}

struct EventQueue {
    events: Vec<ConversationStreamEvent>,
    sender: tokio::sync::broadcast::Sender<ConversationStreamEvent>,
}

impl EventQueue {
    fn push(&mut self, event: ConversationStreamEvent) {
        self.events.push(event);
    }
}

impl Drop for EventQueue {
    fn drop(&mut self) {
        for event in self.events.drain(..) {
            let _ = self.sender.send(event);
        }
    }
}

impl ConversationController {
    pub async fn tenant_id(&self) -> String {
        self.inner.read().await.tenant_id.clone()
    }

    pub async fn user_id(&self) -> String {
        self.inner.read().await.user_id.clone()
    }

    fn event_queue(&self) -> EventQueue {
        EventQueue {
            events: vec![],
            sender: self.sender.clone(),
        }
    }

    /// add_message
    pub async fn add_message(&self, mut message: Message) -> error::Result<()> {
        let now = OffsetDateTime::now_utc();
        match message.status {
            MessageStatus::Done | MessageStatus::Error | MessageStatus::Cancel => {
                let finish_time = now;
                message.finish_time = Some(finish_time);
            }
            _ => {}
        }
        let mut event_queue = self.event_queue();

        let mut conversation = self.inner.write().await;
        let conversation_id = conversation.id;

        let mut conversation_update = doc! {
                ConversationField::UpdateTime.as_str():now,
        };

        let parent_id = conversation.current_message_id;

        if let Some(parent_id) = parent_id {
            let parent_node = match conversation.messages.get_mut(&parent_id) {
                Some(parent_node) => parent_node,
                None => {
                    return Err(error::ConversationError::NotFound(parent_id).into());
                }
            };

            parent_node.children.push(message.id);
            match &mut parent_node.message.status {
                MessageStatus::Done | MessageStatus::Error | MessageStatus::Cancel => {}
                status => {
                    *status = MessageStatus::Done;
                    //修改message状态
                    self.repository
                        .update_one::<entity::document::Message>(
                            doc! {ID:parent_id},
                            doc! {
                                SET:{
                                    MessageField::Status.as_str(): entity::to_bson!(&MessageStatus::Done),
                                    MessageField::FinishTime.as_str(): Some(now),
                                }
                            },
                        )
                        .await?;

                    event_queue.push(ConversationStreamEvent::MessageStatusChanged(
                        MessageStatusChangedPayload {
                            message_id: parent_id,
                            author: None,
                            status: MessageStatus::Done,
                            metadata: None,
                        },
                    ));
                }
            }

            // set finish time
            if let Some(finish_time) = &mut parent_node.message.finish_time {
                *finish_time = now;
                message.finish_time = Some(*finish_time);
            }

            //更新上一条的children
            self.repository
                .update_one::<entity::document::Message>(doc! {ID:parent_id}, doc! {
                    SET: {
                        MessageField::Children.as_str(): entity::to_bson!(&parent_node.children.clone()),
                    }
                })
                .await?;

            // 保存message和content
            let documents =
                &message.try_into_document(Some(parent_node.id), vec![], conversation_id)?;
            self.repository.insert_one(documents.0.clone()).await?;
            if !documents.1.is_empty() {
                self.repository.insert_many(documents.1.clone()).await?;
            }
        } else {
            // 如果是首个消息，还需要设置标题

            let title = message
                .content
                .iter()
                .find_map(|content| {
                    if let ContentItemInner::Text { text, .. } = &content.content {
                        let title = text.trim().replace('\n', " ");

                        tracing::debug!("设置对话标题: {}", title);

                        Some(title)
                    } else {
                        None
                    }
                })
                .take_if(|title| !title.is_empty());

            if let Some(title) = title {
                conversation.title = title.clone();
                conversation_update.insert(ConversationField::Title.as_str(), title);
            }

            let documents = &message.try_into_document(parent_id, vec![], conversation_id)?;
            self.repository.insert_one(documents.0.clone()).await?;
            if !documents.1.is_empty() {
                self.repository.insert_many(documents.1.clone()).await?;
            }
        }

        let node = MessageNode {
            id: message.id,
            parent_id,
            children: vec![],
            message: message.clone(),
        };

        conversation.messages.insert(node.id, node);

        conversation.current_message_id = Some(message.id);
        conversation_update.insert(
            ConversationField::CurrentMessageId.as_str(),
            conversation.current_message_id,
        );
        //更新conversation
        self.repository
            .update_one::<entity::document::Conversation>(
                doc! {ID:conversation_id},
                doc! {
                    SET:conversation_update,
                },
            )
            .await?;

        event_queue.push(ConversationStreamEvent::NewMessage(NewMessagePayload {
            parent_id,
            message,
        }));

        drop(conversation);

        Ok(())
    }

    /// add_content
    pub async fn add_content(&self, mut content_item: ContentItemInner) -> error::Result<()> {
        let mut event_queue = self.event_queue();
        let mut conversation = self.inner.write().await;

        // 额外处理，如果 content_item 是 view/card/log 类型，则需要设置 hide_all 为 true
        if let ContentItemInner::View {
            view: ContentView::Card(ContentCard { r#type, .. }),
            hide_all,
            ..
        } = &mut content_item
        {
            if r#type == "log" {
                *hide_all = Some(true);
            }
        }

        let message_id = match conversation.current_message_id {
            Some(id) => id,
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        let message = match conversation.messages.get_mut(&message_id) {
            Some(node) => &mut node.message,
            None => {
                return Err(error::MessageError::NotFound(message_id).into());
            }
        };

        let index = message.content.len();

        event_queue.push(ConversationStreamEvent::MessageContentDelta(
            MessageContentDeltaPayload {
                message_id,
                operation: ContentDeltaOperation::Update,
                path: vec![index.into()],
                content: serde_json::to_value(&content_item).unwrap_or_default(),
            },
        ));

        //更新上一个content
        if let Some(last_content) = message.content.last() {
            let document = last_content.try_into_document(message_id)?;
            entity::check_document_size(&document).await?;
            self.repository
                .replace_one(doc! {ID:document.id}, document)
                .await?;
        }

        let content: ContentItem = content_item.clone().into();
        message.content.push(content.clone());

        // 保存content
        let document = content.try_into_document(message_id)?;
        entity::check_document_size(&document).await?;
        self.repository.insert_one(document).await?;

        drop(conversation);

        Ok(())
    }

    pub async fn update_metadata(&self, metadata: serde_json::Value) -> error::Result<()> {
        let mut event_queue = self.event_queue();
        let mut conversation = self.inner.write().await;

        let message_id = match conversation.current_message_id {
            Some(id) => id,
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        let message = match conversation.messages.get_mut(&message_id) {
            Some(node) => &mut node.message,
            None => {
                return Err(error::MessageError::NotFound(message_id).into());
            }
        };

        let new_metadata = match metadata {
            serde_json::Value::Object(metadata) => metadata,
            _ => {
                return Err(error::MessageError::InvalidMetadata.into());
            }
        };

        // 合并机制：保留原有metadata，更新新字段
        message.metadata.extend(new_metadata);

        self.repository
            .update_one::<entity::document::Message>(
                doc! {ID:message.id},
                doc! {
                    SET: {
                        MessageField::Metadata.as_str(): entity::to_bson!(&message.metadata),
                    }
                },
            )
            .await?;

        event_queue.push(ConversationStreamEvent::MessageStatusChanged(
            MessageStatusChangedPayload {
                message_id,
                status: message.status.clone(),
                metadata: Some(message.metadata.clone()),
                author: None,
            },
        ));

        drop(conversation);

        Ok(())
    }

    // update_last_content
    pub async fn update_last_content(&self, content_item: ContentItemInner) -> error::Result<()> {
        let mut event_queue = self.event_queue();
        let mut conversation = self.inner.write().await;

        let message_id = match conversation.current_message_id {
            Some(id) => id,
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        let message = match conversation.messages.get_mut(&message_id) {
            Some(node) => &mut node.message,
            None => {
                return Err(error::MessageError::NotFound(message_id).into());
            }
        };

        match message.content.last_mut() {
            Some(item) => {
                item.content = content_item;
                //更新content
                self.repository
                    .update_one::<entity::document::Content>(
                        doc! {ID:item.id},
                        doc! {
                            SET: {
                                ContentField::Content.as_str(): entity::to_bson!(&item.content),
                            }
                        },
                    )
                    .await?;
            }
            None => {
                return Err(error::MessageError::LastContentNotFound.into());
            }
        };

        let index = message.content.len() - 1;

        let last_item_value = serde_json::to_value(message.content.last()).unwrap_or_default();

        drop(conversation);

        event_queue.push(ConversationStreamEvent::MessageContentDelta(
            MessageContentDeltaPayload {
                message_id,
                operation: ContentDeltaOperation::Update,
                path: vec![index.into()],
                content: last_item_value,
            },
        ));

        Ok(())
    }

    // apply_delta
    pub async fn apply_delta(&self, delta: &ChatDelta) -> error::Result<()> {
        let mut event_queue = self.event_queue();
        let mut conversation = self.inner.write().await;

        let message = match conversation.current_message_id {
            Some(id) => match conversation.messages.get_mut(&id) {
                Some(node) => &mut node.message,
                None => {
                    return Err(error::MessageError::NotFound(id).into());
                }
            },
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        let message_id = message.id; // 复制 message_id

        let mut last_item = match message.content.pop() {
            Some(item) => item,
            None => {
                let item = ContentItemInner::Text {
                    text: "".to_string(),
                };

                event_queue.push(ConversationStreamEvent::MessageContentDelta(
                    MessageContentDeltaPayload {
                        message_id,
                        operation: ContentDeltaOperation::Update,
                        path: vec![0.into()],
                        content: serde_json::to_value(&item).unwrap_or_default(),
                    },
                ));

                let item: ContentItem = item.into();
                message.content.push(item.clone());

                let document = item.try_into_document(message_id)?;
                self.repository.insert_one(document).await?;

                return Ok(());
            }
        };

        macro_rules! send_delta {
            ($delta:expr, $($target:expr),*) => {
                event_queue.push(ConversationStreamEvent::MessageContentDelta(
                    MessageContentDeltaPayload {
                        message_id,
                        operation: ContentDeltaOperation::Append,
                        path: vec![serde_json::json!(message.content.len()), $($target.into()),*],
                        content: $delta.into(),
                    },
                ));
            };
        }

        macro_rules! push_last {
            ($item:expr) => {
                message.content.push($item);

                let document = $item.try_into_document(message.id)?;
                self.repository
                    .replace_one(doc! {ID:document.id}, document)
                    .await?;
            };
        }

        macro_rules! send_content {
            ($item:expr) => {
                last_item = $item;

                event_queue.push(ConversationStreamEvent::MessageContentDelta(
                    MessageContentDeltaPayload {
                        message_id,
                        operation: ContentDeltaOperation::Update,
                        path: vec![serde_json::json!(message.content.len())],
                        content: serde_json::to_value(&last_item).unwrap_or_default(),
                    },
                ));
            };
        }

        macro_rules! send_results {
            ($results:expr, $($target:expr),*) => {
                for result in $results {
                    let mut path = vec![serde_json::json!(message.content.len()), $($target.into()),*];
                    path.extend(result.path);

                    event_queue.push(ConversationStreamEvent::MessageContentDelta(
                        MessageContentDeltaPayload {
                            message_id,
                            operation: result.operation,
                            path,
                            content: result.content,
                        },
                    ));
                }
            };
        }

        match delta.clone() {
            ChatDelta::Text(delta) => match &mut last_item.content {
                ContentItemInner::Text { text, .. } => {
                    *text += &delta;
                    send_delta!(delta, "text");
                }
                _other => {
                    if !delta.trim().is_empty() {
                        push_last!(last_item.clone());
                        send_content!(ContentItemInner::Text { text: delta }.into());
                    }
                }
            },
            ChatDelta::VerifyThink(delta) => match &mut last_item.content {
                ContentItemInner::View {
                    view:
                        ContentView::Card(ContentCard {
                            r#type, details, ..
                        }),
                    ..
                } if r#type == "verify_think" => match details {
                    Some(details) => {
                        let results = details.append_str(&delta);
                        send_results!(results, "view", "content", "details");
                    }
                    None => {
                        *details = Some(ContentViewOrText::Text(delta.clone()));
                        send_delta!(delta, "view", "content", "details");
                    }
                },
                _other => {
                    push_last!(last_item.clone());
                    send_content!(
                        ContentItemInner::View {
                            view: ContentView::Card(ContentCard {
                                r#type: "verify_think".to_string(),
                                details: Some(delta.into()),
                                description: Some("验证结果".to_string()),
                                title: "思考结果".to_string(),
                                ..Default::default()
                            }),
                            hide_all: None,
                        }
                        .into()
                    );
                }
            },
            ChatDelta::Think(delta) => match &mut last_item.content {
                ContentItemInner::View {
                    view:
                        ContentView::Card(ContentCard {
                            r#type, details, ..
                        }),
                    ..
                } if r#type == "think" => match details {
                    Some(details) => {
                        let results = details.append_str(&delta);
                        send_results!(results, "view", "content", "details");
                    }
                    None => {
                        *details = Some(ContentViewOrText::Text(delta.clone()));
                        send_delta!(delta, "view", "content", "details");
                    }
                },
                _other => {
                    push_last!(last_item.clone());
                    send_content!(
                        ContentItemInner::View {
                            view: ContentView::Card(ContentCard {
                                r#type: "think".to_string(),
                                title: "思考结果".to_string(),
                                details: Some(delta.into()),
                                description: Some("针对您的问题已找到解决方案".to_string()),
                                ..Default::default()
                            }),
                            hide_all: None,
                        }
                        .into()
                    );
                }
            },
            ChatDelta::Json(delta) => match &mut last_item.content {
                ContentItemInner::Json { json, .. } => {
                    *json += &delta;
                    send_delta!(delta, "json");
                }
                _other => {
                    push_last!(last_item.clone());
                    send_content!(ContentItemInner::Json { json: delta }.into());
                }
            },
            ChatDelta::Branch(delta) => match &mut last_item.content {
                ContentItemInner::Branch { branch, .. } => {
                    *branch += &delta;
                    send_delta!(delta, "branch");
                }
                _other => {
                    push_last!(last_item.clone());
                    send_content!(ContentItemInner::Branch { branch: delta }.into());
                }
            },
            ChatDelta::Confidence(delta) => match &mut last_item.content {
                ContentItemInner::View {
                    view:
                        ContentView::Card(ContentCard {
                            description,
                            r#type,
                            ..
                        }),
                    ..
                } if r#type == "confidence" => {
                    match description {
                        Some(description) => {
                            *description += &delta;
                        }
                        None => {
                            *description = Some(delta.clone());
                        }
                    }
                    send_delta!(delta, "view", "content", "description");
                }
                _other => {
                    push_last!(last_item.clone());
                    send_content!(
                        ContentItemInner::View {
                            view: ContentView::Card(ContentCard {
                                r#type: "confidence".to_string(),
                                title: "置信度".to_string(),
                                description: Some(delta),
                                ..Default::default()
                            }),
                            hide_all: None,
                        }
                        .into()
                    );
                }
            },
            ChatDelta::Verify(delta) => match &mut last_item.content {
                ContentItemInner::View {
                    view:
                        ContentView::Card(ContentCard {
                            content, r#type, ..
                        }),
                    ..
                } if r#type == "verify" => match content {
                    Some(content) => {
                        let results = content.append_str(&delta);
                        send_results!(results, "view", "content", "content");
                    }
                    None => {
                        *content = Some(ContentViewOrText::Text(delta.clone()));
                        send_delta!(delta, "view", "content", "content");
                    }
                },
                _other => {
                    push_last!(last_item.clone());
                    send_content!(
                        ContentItemInner::View {
                            view: ContentView::Card(ContentCard {
                                content: Some(delta.into()),
                                title: "验证结果".to_string(),
                                r#type: "verify".to_string(),
                                ..Default::default()
                            }),
                            hide_all: None,
                        }
                        .into()
                    );
                }
            },
            ChatDelta::RequireConfirm(delta) => match &mut last_item.content {
                ContentItemInner::RequireConfirm {
                    require_confirm, ..
                } => {
                    *require_confirm += &delta;
                    send_delta!(delta, "require_confirm");
                }
                _other => {
                    push_last!(last_item.clone());
                    send_content!(
                        ContentItemInner::RequireConfirm {
                            require_confirm: delta,
                        }
                        .into()
                    );
                }
            },
            ChatDelta::Title(delta) => match &mut last_item.content {
                ContentItemInner::View {
                    view: ContentView::Card(ContentCard { title, .. }),
                    ..
                } if title == "title" => {
                    *title += &delta;
                    send_delta!(delta, "view", "content", "title");
                }
                _other => {
                    push_last!(last_item.clone());
                    send_content!(
                        ContentItemInner::View {
                            view: ContentView::Card(ContentCard {
                                title: delta,
                                r#type: "title".to_string(),
                                ..Default::default()
                            }),
                            hide_all: None,
                        }
                        .into()
                    );
                }
            },
            ChatDelta::Metadata(_) => {
                // match serde_json::from_str::<ChatTextMetadata>(&delta) {
                //     Ok(md) => *metadata = Some(md),
                //     Err(e) => {
                //         tracing::error!("解析 metadata 失败: {}", e);
                //     }
                // };
            }
            // TODO
            _ => {}
        }

        message.content.push(last_item);

        Ok(())
    }

    pub async fn apply_card_delta(&self, content: ContentCardDelta) -> error::Result<()> {
        let mut event_queue = self.event_queue();
        let mut conversation = self.inner.write().await;

        let current_message_id = match conversation.current_message_id {
            Some(id) => id,
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        let current_message = match conversation.messages.get_mut(&current_message_id) {
            Some(node) => &mut node.message,
            None => {
                return Err(error::MessageError::NotFound(current_message_id).into());
            }
        };

        if let Some(ContentItem {
            content:
                ContentItemInner::View {
                    view: ContentView::Card(card),
                    ..
                },
            ..
        }) = current_message.content.last_mut()
        {
            let results = content.apply(card);

            for result in results {
                let mut path = vec![
                    serde_json::json!(current_message.content.len() - 1),
                    "view".into(),
                    "content".into(),
                ];

                path.extend(result.path);

                event_queue.push(ConversationStreamEvent::MessageContentDelta(
                    MessageContentDeltaPayload {
                        message_id: current_message_id,
                        operation: result.operation,
                        path,
                        content: result.content,
                    },
                ));
            }
        }

        drop(conversation);

        Ok(())
    }

    pub async fn add_warn_card(&self, mut card: ContentCard) -> error::Result<()> {
        card.r#type = "warn".to_string();

        self.add_content(ContentItemInner::View {
            view: ContentView::Card(card),
            hide_all: Some(true),
        })
        .await
    }

    pub async fn add_error_card(&self, mut card: ContentCard) -> error::Result<()> {
        card.r#type = "error".to_string();

        self.add_content(ContentItemInner::View {
            view: ContentView::Card(card),
            hide_all: None,
        })
        .await
    }

    pub async fn set_conversation_status(&self, status: ConversationStatus) -> error::Result<()> {
        let mut event_queue = self.event_queue();
        let mut conversation = self.inner.write().await;

        conversation.status = status.clone();
        // 更新子状态
        if let ConversationStatus::Interaction(ref content) = status {
            conversation.sub_status = content.clone();
        } else {
            conversation.sub_status = ConversationSubStatus::None;
        }

        if let Err(e) = self.status_sender.send(ConversationStatusEvent {
            id: conversation.id,
            status: status.clone(),
            sub_status: conversation.sub_status.clone(),
            user_id: conversation.user_id.clone(),
            tenant_id: conversation.tenant_id.clone(),
        }) {
            let dropped_message = e.0;
            tracing::error!(
                "Failed to send message {:?}: no receivers. This is acceptable.",
                dropped_message,
            );
        }

        let now = OffsetDateTime::now_utc();
        conversation.update_time = Some(now);
        //更新conversation状态
        self.repository
            .update_one::<entity::document::Conversation>(
                doc! {ID:conversation.id},
                doc! {
                    SET: {
                        ConversationField::Status.as_str(): entity::to_bson!(&status),
                        ConversationField::UpdateTime.as_str(): now,
                    }
                },
            )
            .await?;

        drop(conversation);

        event_queue.push(ConversationStreamEvent::ConversationStatusChanged(
            ConversationStatusChangedPayload {
                status,
                metadata: None,
            },
        ));

        Ok(())
    }

    pub async fn set_current_message_status(&self, status: MessageStatus) -> error::Result<()> {
        let mut event_queue = self.event_queue();

        let mut conversation = self.inner.write().await;

        let current_message_id = conversation.current_message_id;

        let message = match current_message_id {
            Some(id) => match conversation.messages.get_mut(&id) {
                Some(node) => &mut node.message,
                None => {
                    return Err(error::MessageError::NotFound(id).into());
                }
            },
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        match message.status {
            MessageStatus::Done | MessageStatus::Cancel | MessageStatus::Error => {
                tracing::debug!("skip set message status: {:?}", status);
                return Ok(());
            }
            _ => {}
        }

        message.status = status.clone();

        let mut message_update = doc! {
            MessageField::Status.as_str(): entity::to_bson!(&status),
        };

        match &status {
            MessageStatus::Done | MessageStatus::Error | MessageStatus::Cancel => {
                let finish_time = OffsetDateTime::now_utc();
                message.finish_time = Some(finish_time);
                message_update.insert(MessageField::FinishTime.as_str(), finish_time);
            }
            _ => {}
        }

        event_queue.push(ConversationStreamEvent::MessageStatusChanged(
            MessageStatusChangedPayload {
                message_id: message.id,
                author: None,
                status,
                metadata: None,
            },
        ));

        // 更新message状态
        self.repository
            .update_one::<entity::document::Message>(
                doc! {ID: message.id},
                doc! {
                    SET:message_update,
                },
            )
            .await?;

        drop(conversation);

        Ok(())
    }

    // get_context
    pub async fn get_context(&self) -> error::Result<Vec<ai::Message>> {
        let conversation = self.inner.read().await;
        let messages = conversation
            .get_context()
            .into_iter()
            .map(Into::into)
            .collect();

        Ok(messages)
    }

    // get_context_until_user_message
    pub async fn get_context_until_user_message(&self) -> error::Result<Vec<ai::Message>> {
        let conversation = self.inner.read().await;
        let messages = conversation
            .get_context_until_user_message()
            .into_iter()
            .map(Into::into)
            .collect();

        Ok(messages)
    }

    // get_last_user_input
    pub async fn get_last_user_input(&self) -> error::Result<String> {
        let conversation = self.inner.read().await;

        let current_message_id = conversation.current_message_id;

        let mut node = match current_message_id {
            Some(id) => match conversation.messages.get(&id) {
                Some(node) => node,
                None => {
                    return Err(error::MessageError::NotFound(id).into());
                }
            },
            None => {
                return Err(error::MessageError::CurrentNotFound.into());
            }
        };

        while !matches!(&node.message.author, Author::User { .. }) {
            if let Some(parent_id) = node.parent_id {
                node = match conversation.messages.get(&parent_id) {
                    Some(node) => node,
                    None => {
                        return Err(error::MessageError::NotFound(parent_id).into());
                    }
                };
            } else {
                break;
            }
        }

        let message = match node.message.author {
            Author::User { .. } => &node.message,
            _ => {
                return Err(error::MessageError::LastUserInputNotFound.into());
            }
        };

        let result = message
            .content
            .iter()
            .rev()
            .find_map(|item| {
                if let ContentItemInner::Text { text, .. } = &item.content {
                    Some(text.clone())
                } else {
                    None
                }
            })
            .ok_or_else(|| error::MessageError::LastUserInputNotFound.into());

        drop(conversation);

        result
    }

    // pub async fn collect_summary_file(&self) -> error::Result<()> {
    //     let mut conversation = self.inner.write().await;

    //     let context = conversation.get_context_until_user_message();

    //     let current_message_id = conversation.current_message_id;

    //     let current_message = match current_message_id {
    //         Some(id) => match conversation.messages.get_mut(&id) {
    //             Some(node) => &mut node.message,
    //             None => {
    //                 return Err(anyhow::anyhow!("current_message not found: {:?}", id));
    //             }
    //         },
    //         None => {
    //             return Err(anyhow::anyhow!("current_message_id not found"));
    //         }
    //     };

    //     let message_id = current_message.id;

    //     let length = current_message.content.len();

    //     let mut event_queue = self.event_queue();

    //     let contents = context.into_iter().fold::<Vec<ContentItemInner>, _>(
    //         vec![],
    //         |mut contents, message| {
    //             if !matches!(message.author, Author::Tool { .. }) {
    //                 return contents;
    //             }

    //             contents.extend(message.content.into_iter().filter_map(|mut item| {
    //                 if let ContentItemInner::View {
    //                     view: ContentView::Card(card),
    //                     ..
    //                 } = &mut item.content
    //                 {
    //                     if card.r#type != "file" {
    //                         return None;
    //                     }

    //                     card.r#type = "summary_file".to_string();

    //                     return Some(item.content);
    //                 }

    //                 None
    //             }));

    //             contents
    //         },
    //     );

    //     event_queue
    //         .events
    //         .extend(contents.iter().enumerate().map(|(index, content)| {
    //             ConversationStreamEvent::MessageContentDelta(MessageContentDeltaPayload {
    //                 message_id,
    //                 operation: ContentDeltaOperation::Update,
    //                 path: vec![(length + index).into()],
    //                 content: serde_json::to_value(content).unwrap_or_default(),
    //             })
    //         }));

    //     current_message
    //         .content
    //         .extend(contents.into_iter().map(|i| i.into()));

    //     drop(conversation);

    //     Ok(())
    // }
}
