use content::{ContentItem, ContentItemInner};
use serde::{Deserialize, Serialize};
use time::OffsetDateTime;
use uuid::Uuid;

/// 消息状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MessageStatus {
    /// 对话中
    #[serde(rename = "chat")]
    Chat,
    /// 完成
    #[serde(rename = "done")]
    Done,
    /// 错误
    #[serde(rename = "error")]
    Error,
    /// 交互
    #[serde(rename = "interaction")]
    Interaction,
    /// 执行
    #[serde(rename = "execute")]
    Execute,
    /// 取消
    #[serde(rename = "cancel")]
    Cancel,
}

impl From<entity::document::MessageStatus> for MessageStatus {
    fn from(value: entity::document::MessageStatus) -> Self {
        match value {
            entity::document::MessageStatus::Chat => Self::Chat,
            entity::document::MessageStatus::Done => Self::Done,
            entity::document::MessageStatus::Error => Self::Error,
            entity::document::MessageStatus::Interaction => Self::Interaction,
            entity::document::MessageStatus::Execute => Self::Execute,
            entity::document::MessageStatus::Cancel => Self::Cancel,
        }
    }
}

impl From<MessageStatus> for entity::document::MessageStatus {
    fn from(value: MessageStatus) -> Self {
        match value {
            MessageStatus::Chat => Self::Chat,
            MessageStatus::Done => Self::Done,
            MessageStatus::Error => Self::Error,
            MessageStatus::Interaction => Self::Interaction,
            MessageStatus::Execute => Self::Execute,
            MessageStatus::Cancel => Self::Cancel,
        }
    }
}

/// 消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    /// 消息ID
    pub id: Uuid,
    /// 作者信息
    pub author: Author,
    /// 创建时间
    #[serde(with = "time::serde::rfc3339")]
    pub create_time: OffsetDateTime,
    /// 更新时间
    #[serde(
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub finish_time: Option<OffsetDateTime>,
    /// 元数据
    pub metadata: serde_json::Map<String, serde_json::Value>,
    /// 消息状态
    pub status: MessageStatus,
    /// 消息内容
    pub content: Vec<ContentItem>,
}

impl From<Message> for ai::Message {
    fn from(message: Message) -> Self {
        Self {
            role: message.author.into(),
            content: message
                .content
                .into_iter()
                .map(|content| content.content)
                .collect(),
        }
    }
}

impl From<Author> for ai::Role {
    fn from(author: Author) -> Self {
        match author {
            Author::User { .. } => ai::Role::User,
            Author::System { .. } => ai::Role::System,
            Author::Assistant { .. } => ai::Role::Assistant,
            Author::Workflow { .. } => ai::Role::Assistant,
            Author::Tool { .. } => ai::Role::Tool,
        }
    }
}

impl Message {
    pub fn system(content: String) -> Self {
        Self {
            id: Uuid::now_v7(),
            author: Author::System { name: None },
            create_time: OffsetDateTime::now_utc(),
            finish_time: None,
            metadata: serde_json::Map::new(),
            status: MessageStatus::Done,
            content: vec![ContentItemInner::Text { text: content }.into()],
        }
    }

    pub fn user(content: String) -> Self {
        Self {
            id: Uuid::now_v7(),
            author: Author::User { name: None },
            create_time: OffsetDateTime::now_utc(),
            finish_time: None,
            metadata: serde_json::Map::new(),
            status: MessageStatus::Done,
            content: vec![ContentItemInner::Text { text: content }.into()],
        }
    }

    pub fn user_with_content(content: Vec<ContentItem>) -> Self {
        Self {
            id: Uuid::now_v7(),
            author: Author::User { name: None },
            create_time: OffsetDateTime::now_utc(),
            finish_time: None,
            metadata: serde_json::Map::new(),
            status: MessageStatus::Done,
            content,
        }
    }

    pub fn assistant() -> Self {
        Self {
            id: Uuid::now_v7(),
            author: Author::Assistant { name: None },
            create_time: OffsetDateTime::now_utc(),
            finish_time: None,
            metadata: serde_json::Map::new(),
            status: MessageStatus::Chat,
            content: vec![],
        }
    }

    pub fn try_into_document(
        &mut self,
        parent_id: Option<Uuid>,
        children: Vec<Uuid>,
        conversation_id: Uuid,
    ) -> error::Result<(entity::document::Message, Vec<entity::document::Content>)> {
        let message = entity::document::Message {
            id: self.id,
            author: entity::to_bson!(&self.author),
            create_time: self.create_time,
            finish_time: self.finish_time,
            metadata: Some(entity::to_bson!(&self.metadata)),
            status: self.status.clone().into(),
            conversation_id,
            parent_id,
            children: entity::to_bson!(&children),
            contents: vec![],
            content_ids: vec![],
        };

        let contents = &mut self.content;
        let contents: error::Result<Vec<entity::document::Content>> = contents
            .iter_mut()
            .map(|c| {
                Ok(entity::document::Content {
                    id: c.id,
                    message_id: self.id,
                    content: entity::to_bson!(c),
                })
            })
            .collect();
        let contents = contents?;

        Ok((message, contents))
    }

    pub fn try_from_document(
        message: entity::document::Message,
        contents: Vec<entity::document::Content>,
    ) -> error::Result<Self> {
        let contents = contents
            .iter()
            .filter_map(|c| match bson::from_bson(c.content.clone()) {
                Ok(content) => Some(ContentItem { id: c.id, content }),
                Err(e) => {
                    tracing::error!("Failed to deserialize content: {}", e);
                    None
                }
            })
            .collect::<Vec<_>>();

        Ok(Self {
            id: message.id,
            author: entity::from_bson!(message.author),
            create_time: message.create_time,
            finish_time: message.finish_time,
            metadata: match message.metadata {
                Some(metadata) => {
                    entity::from_bson!(metadata)
                }
                None => serde_json::Map::new(),
            },
            status: message.status.into(),
            content: contents,
        })
    }
}

/// 消息树结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageNode {
    /// 消息ID
    pub id: Uuid,
    /// 父消息ID
    pub parent_id: Option<Uuid>,
    /// 子消息ID列表
    pub children: Vec<Uuid>,
    /// 消息内容
    pub message: Message,
}

/// 作者类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(tag = "role")]
pub enum Author {
    /// 用户
    #[serde(rename = "user")]
    User {
        /// 名称
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
    },
    /// 系统
    #[serde(rename = "system")]
    System {
        /// 名称
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
    },
    /// 助手
    #[serde(rename = "assistant")]
    Assistant {
        /// 名称
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
    },
    /// 工作流
    #[serde(rename = "workflow")]
    Workflow {
        /// 名称
        name: String,
    },
    /// 工作流节点
    #[serde(rename = "tool")]
    Tool {
        /// 模块
        module: String,
        /// 工具
        name: String,
        /// 节点ID
        #[serde(skip_serializing_if = "Option::is_none")]
        step_id: Option<String>,
        /// 工作流ID
        #[serde(skip_serializing_if = "Option::is_none")]
        workflow_id: Option<Uuid>,
    },
}
