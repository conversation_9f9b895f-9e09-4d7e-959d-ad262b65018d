use std::sync::<PERSON>;

use actor::{ModelRun<PERSON>, WorkflowRunner};
use ai::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use content::{ContentCard, ContentView, ContentViewOrText};
use entity::DocumentRepository;
use integral::IntegralManager;
use interaction::Interaction;
use resource::ResourceManager;
use script::{
    ScriptRunner,
    manager::{FileTarget, ScriptManager},
    runner::FileInfo,
};
use tool::{
    Tool,
    global::GlobalToolRepository,
    repository::{CallToolInput, CallValidatorInput, ToolRepository},
};
use workflow::{Workflow, WorkflowNodeState};

pub struct ConversationRunner {
    pub repository: Arc<GlobalToolRepository>,
    pub model: Arc<dyn ModelRunner + Send + Sync>,
    pub script: Arc<dyn ScriptManager + Send + Sync>,
    pub workflow: Arc<dyn WorkflowRunner + Send + Sync>,
    pub resource_manager: Arc<ResourceManager>,
    pub document_repository: Arc<DocumentRepository>,
    pub integral_manager: Arc<IntegralManager>,
}

pub struct ConversationScriptRunner {
    pub resource_manager: Arc<ResourceManager>,
}

pub struct ConversationWorkflowRunner {
    pub max_retry_count: usize,
    pub resource_manager: Arc<ResourceManager>,
    pub repository: Arc<GlobalToolRepository>,
    pub script: Arc<dyn ScriptManager + Send + Sync>,
    pub model: Arc<dyn ModelRunner + Send + Sync>,
    pub converter_system_prompt: String,
    pub extractor_system_prompt: String,
    pub summary_system_prompt: String,
    pub final_summary_system_prompt: String,
}

#[async_trait::async_trait]
impl ScriptRunner for ConversationScriptRunner {
    async fn get_interaction(
        &self,
        session_id: &str,
        executation_id: &str,
        id: &str,
    ) -> Option<Interaction> {
        self.resource_manager
            .get_interaction(session_id, executation_id, id)
            .await
            .ok()
            .flatten()
    }

    async fn set_interaction(
        &self,
        session_id: &str,
        executation_id: &str,
        id: &str,
        interaction: Interaction,
    ) {
        let _ = self
            .resource_manager
            .set_interaction(session_id, executation_id, id, interaction)
            .await;
    }

    async fn add_view(&self, session_id: &str, view: serde_json::Value) {
        let _ = self.resource_manager.add_view(session_id, view).await;
    }

    async fn get_views(&self, session_id: &str) -> error::Result<Vec<serde_json::Value>> {
        self.resource_manager.get_views(session_id).await
    }

    async fn set_cache(&self, session_id: &str, key: &str, data: serde_json::Value) {
        let _ = self.resource_manager.set_cache(session_id, key, data).await;
    }

    async fn get_cache(&self, session_id: &str, key: &str) -> Option<serde_json::Value> {
        self.resource_manager
            .get_cache(session_id, key)
            .await
            .ok()
            .flatten()
    }

    async fn add_file(&self, target: FileTarget, data: bytes::Bytes) -> error::Result<FileInfo> {
        let info = match target.scope.as_deref() {
            Some("user") => {
                self.resource_manager
                    .write_user_file(
                        &target.context_id.tenant_id,
                        &target.context_id.user_id,
                        &target.object,
                        data,
                    )
                    .await
            }
            Some("session") => {
                self.resource_manager
                    .write_session_file(
                        &target.context_id.tenant_id,
                        &target.context_id.session_id,
                        &target.object,
                        data,
                    )
                    .await
            }
            Some("global") => {
                self.resource_manager
                    .write_global_file(&target.context_id.tenant_id, &target.object, data)
                    .await
            }
            Some(scope) => Err(error::ScriptError::InvalidScope(scope.to_string()).into()),
            None => match target.bucket {
                Some(bucket) => {
                    self.resource_manager
                        .write_file(&bucket, &target.object, data)
                        .await
                }
                None => {
                    Err(error::ScriptError::InvalidBucket(target.bucket.unwrap_or_default()).into())
                }
            },
        }?;

        Ok(FileInfo {
            bucket: info.bucket,
            object: info.object,
            version_id: info.version_id,
        })
    }

    async fn get_file(&self, target: FileTarget) -> error::Result<bytes::Bytes> {
        match target.scope.as_deref() {
            Some("user") => {
                self.resource_manager
                    .read_user_file(
                        &target.context_id.tenant_id,
                        &target.context_id.user_id,
                        &target.object,
                        target.version_id,
                    )
                    .await
            }
            Some("session") => {
                self.resource_manager
                    .read_session_file(
                        &target.context_id.tenant_id,
                        &target.context_id.session_id,
                        &target.object,
                        target.version_id,
                    )
                    .await
            }
            Some("global") => {
                self.resource_manager
                    .read_global_file(
                        &target.context_id.tenant_id,
                        &target.object,
                        target.version_id,
                    )
                    .await
            }
            Some(scope) => Err(error::ScriptError::InvalidScope(scope.to_string()).into()),
            None => match target.bucket {
                Some(bucket) => {
                    self.resource_manager
                        .read_file(&bucket, &target.object, target.version_id)
                        .await
                }
                None => {
                    Err(error::ScriptError::InvalidBucket(target.bucket.unwrap_or_default()).into())
                }
            },
        }
    }
}

#[async_trait::async_trait]
impl WorkflowRunner for ConversationWorkflowRunner {
    fn get_max_retry_count(&self) -> usize {
        self.max_retry_count
    }

    async fn extract_params(
        &self,
        context: &[ai::Message],
        workflow: Arc<Workflow>,
        params_schema: Arc<serde_json::Value>,
        tenant_id: String,
    ) -> error::Result<serde_json::Value> {
        let source = self
            .model
            .search_source(context, params_schema.clone(), workflow.clone(), tenant_id)
            .await
            .inspect_err(|e| {
                tracing::error!("search_source error: {:?}", e);
            })
            .unwrap_or_default();

        let user_message = format!(
            r#"<system>
{}
</system>

<context>
{}
</context>

<workflow>
{}
</workflow>

<output_schema>
{}
</output_schema>

<source>
{}
</source>
"#,
            format_system(),
            format_context(context),
            format_workflow(&workflow),
            format_schema(&params_schema),
            source,
        );

        tracing::debug!(
            "extract_params, user: {}",
            serde_json::to_string(&serde_json::json!({
                "context": context,
                "workflow": workflow,
                "output_schema": params_schema,
                "source": source,
            }))
            .unwrap_or_default()
        );

        let deltas = self
            .model
            .chat_stream(&[
                ai::Message::system(self.extractor_system_prompt.clone()),
                ai::Message::user(user_message.clone()),
            ])
            .await?
            .into_block_stream()
            .done()
            .await;

        tracing::debug!(
            "extract_params, user: {}, result: {:?}",
            user_message,
            deltas
        );

        let result = deltas
            .iter()
            .find_map(|delta| match delta.as_ref() {
                ChatDelta::Code(code) => Some(code.clone()),
                _ => None,
            })
            .unwrap_or_default();

        Ok(serde_json::from_str(&result)?)
    }

    async fn call_validator(&self, input: CallValidatorInput) -> error::Result<serde_json::Value> {
        self.script
            .run_validator(
                input.context_id(),
                &input.module_name,
                &input.validator_name,
                &input.value,
                &input.payload,
            )
            .await
    }

    async fn generate_converter(
        &self,
        context: &[ai::Message],
        workflow: Arc<Workflow>,
        history: Vec<Arc<WorkflowNodeState>>,
        current: Arc<WorkflowNodeState>,
        tenant_id: String,
    ) -> error::Result<String> {
        let input_schema = serde_json::json!({
            "type": "object",
            "properties": serde_json::Map::from_iter(
                history.iter()
                    .map(|h| (h.node.id.clone(), h.tool.result_schema().as_ref().clone())),
            ),
        });

        let params_schema = current.tool.params_schema();

        let source = self
            .model
            .search_source(context, params_schema.clone(), workflow.clone(), tenant_id)
            .await
            .inspect_err(|e| {
                tracing::error!("search_source error: {:?}", e);
            })
            .unwrap_or_default();

        let user_message = format!(
            r#"<system>
{}
</system>

<context>
{}
</context>

<workflow>
{}
</workflow>

<history>
{}
</history>

<input_schema>
{}
</input_schema>

<output_schema>
{}
</output_schema>

<source>
{}
</source>

<last_error>
{}
</last_error>

<last_converter>
{}
</last_converter>

<last_params>
{}
</last_params>

<last_result>
{}
</last_result>
"#,
            format_system(),
            format_context(context),
            format_workflow(&workflow),
            format_history(&history),
            format_schema(&input_schema),
            format_schema(&params_schema),
            source,
            current.error.as_deref().unwrap_or_default(),
            current.converter.as_deref().cloned().unwrap_or_default(),
            format_value(&current.params),
            format_value(&current.result)
        );

        tracing::debug!(
            "generate_converter, user: {}",
            serde_json::to_string(&serde_json::json!({
                "context": context,
                "workflow": workflow,
                "history": history,
                "input_schema": input_schema,
                "output_schema": current.tool.params_schema(),
                "last_error": current.error,
            }))
            .unwrap_or_default()
        );

        let deltas = self
            .model
            .chat_stream(&[
                ai::Message::system(self.converter_system_prompt.clone()),
                ai::Message::user(user_message.clone()),
            ])
            .await?
            .into_block_stream()
            .done()
            .await;

        tracing::debug!(
            "generate_converter, user: {}, result: {:?}",
            user_message,
            deltas
        );

        let result = deltas
            .iter()
            .find_map(|delta| match delta.as_ref() {
                ChatDelta::Code(code) => Some(code.clone()),
                _ => None,
            })
            .unwrap_or_default();

        Ok(result)
    }

    async fn call_converter(
        &self,
        converter: &str,
        history: Vec<Arc<WorkflowNodeState>>,
    ) -> error::Result<serde_json::Value> {
        let input_data = serde_json::Map::from_iter(history.iter().map(|result| {
            (
                result.node.id.clone(),
                result
                    .result
                    .as_ref()
                    .map(|v| v.as_ref().clone())
                    .unwrap_or_default(),
            )
        }));

        let result = self
            .script
            .run_code_function(converter, "convert", &input_data.into())
            .await?;

        Ok(result)
    }

    async fn get_tool(&self, module: &str, tool: &str) -> Option<Arc<Tool>> {
        self.repository.get_tool_with_alias(module, tool).await
    }

    async fn call_tool(&self, input: CallToolInput) -> error::Result<serde_json::Value> {
        let output = self.repository.call_tool(input).await?;
        Ok(output.payload)
    }

    async fn generate_summary(
        &self,
        context: &[ai::Message],
        workflow: Arc<Workflow>,
        history: Vec<Arc<WorkflowNodeState>>,
        current: Arc<WorkflowNodeState>,
    ) -> error::Result<String> {
        let deltas = self
            .model
            .chat_stream(&[
                ai::Message::system(self.summary_system_prompt.clone()),
                ai::Message::user(format!(
                    r#"<system>
{}
</system>

<context>
{}
</context>

<workflow>
{}
</workflow>

<tool>
{:?}
</tool>

<history>
{}
</history>

<input>
{}
</input>

<output>
{}
</output>
"#,
                    format_system(),
                    format_context(context),
                    format_workflow(&workflow),
                    current.tool,
                    format_history(&history),
                    format_value(&current.params),
                    format_value(&current.result)
                )),
            ])
            .await?
            .into_block_stream()
            .done()
            .await;

        tracing::debug!(
            "generate_summary, user: {}",
            serde_json::to_string(&serde_json::json!({
                "context": context,
                "workflow": workflow,
                "tool": current.tool,
                "history": history,
                "input": current.params,
                "output": current.result,
            }))
            .unwrap_or_default()
        );

        let result = deltas
            .iter()
            .find_map(|delta| match delta.as_ref() {
                ChatDelta::Text(text) => Some(text.clone()),
                _ => None,
            })
            .unwrap_or_default();

        Ok(result)
    }

    async fn generate_final_summary(
        &self,
        context: &[ai::Message],
        workflow: Arc<Workflow>,
        history: Vec<Arc<WorkflowNodeState>>,
    ) -> error::Result<String> {
        let deltas = self
            .model
            .chat_stream(&[
                ai::Message::system(self.final_summary_system_prompt.clone()),
                ai::Message::user(format!(
                    r#"<system>
{}
</system>

<context>
{}
</context>

<workflow>
{}
</workflow>

<history>
{}
</history>
"#,
                    format_system(),
                    format_context(context),
                    format_workflow(&workflow),
                    format_verbose_history(&history),
                )),
            ])
            .await?
            .into_block_stream()
            .done()
            .await;

        tracing::debug!(
            "generate_final_summary, user: {}",
            serde_json::to_string(&serde_json::json!({
                "context": context,
                "workflow": workflow,
                "history": history,
            }))
            .unwrap_or_default()
        );

        let result = deltas
            .iter()
            .find_map(|delta| match delta.as_ref() {
                ChatDelta::Text(text) => Some(text.clone()),
                _ => None,
            })
            .unwrap_or_default();

        Ok(result)
    }

    async fn set_interaction(
        &self,
        session_id: &str,
        executation_id: &str,
        interaction_id: &str,
        interaction: Interaction,
    ) -> error::Result<()> {
        self.resource_manager
            .set_interaction(session_id, executation_id, interaction_id, interaction)
            .await
    }
}

fn format_workflow(workflow: &Workflow) -> String {
    let nodes_info = workflow
        .nodes
        .iter()
        .map(|node| format!("ID {}: {}/{}", node.id, node.module, node.tool))
        .collect::<Vec<_>>()
        .join("\n");

    let edges_info = workflow
        .edges
        .iter()
        .map(|edge| format!("{} -> {}", edge.source_node, edge.target_node))
        .collect::<Vec<_>>()
        .join("\n");

    format!("节点信息：\n{nodes_info}\n连线信息：\n{edges_info}")
}

fn format_history(history: &[Arc<WorkflowNodeState>]) -> String {
    history
        .iter()
        .map(|result| {
            format!(
                r#"节点ID：{}

能力输入：{}

能力输出：{}

能力标识：{}/{}

运行总结：{}

运行错误：{}"#,
                result.node.id,
                format_value(&result.params),
                format_value(&result.result),
                result.node.module,
                result.node.tool,
                result
                    .summary
                    .as_ref()
                    .map(|s| s.as_str())
                    .unwrap_or("<empty>"),
                result.error.as_deref().unwrap_or("<empty>")
            )
        })
        .collect::<Vec<_>>()
        .join("\n")
}

fn format_verbose_history(history: &[Arc<WorkflowNodeState>]) -> String {
    history
        .iter()
        .map(|result| {
            format!(
                r#"节点ID：{}

能力标识：{}/{}

能力输入：{}

能力输入结构：{}

能力输出：{}

能力输出结构：{}

运行总结：{}

运行错误：{}

能力执行过程中输出的展示数据：{}

"#,
                result.node.id,
                result.node.module,
                result.node.tool,
                format_value(&result.params),
                format_schema(&result.tool.params_schema()),
                format_value(&result.result),
                format_schema(&result.tool.result_schema()),
                result
                    .summary
                    .as_ref()
                    .map(|s| s.as_str())
                    .unwrap_or("<empty>"),
                result.error.as_deref().unwrap_or("<empty>"),
                format_views(&result.views),
            )
        })
        .collect::<Vec<_>>()
        .join("\n")
}

fn format_system() -> String {
    format!(
        "当前时间：{}",
        time::OffsetDateTime::now_local()
            .unwrap_or_else(|_| time::OffsetDateTime::now_utc())
            .format(&time::format_description::well_known::Rfc3339)
            .unwrap_or_default()
    )
}

fn format_context(context: &[ai::Message]) -> String {
    serde_json::to_string_pretty(context).unwrap_or_default()
}

fn format_schema(schema: &serde_json::Value) -> String {
    serde_json::to_string_pretty(schema).unwrap_or_default()
}

fn format_value(value: &Option<Arc<serde_json::Value>>) -> String {
    // 约束一下，最大长度 4 * 1024
    // 超过长度，截断，并添加省略号，说明剩余长度
    let value = serde_json::to_string_pretty(
        value
            .as_ref()
            .map(|v| v.as_ref())
            .unwrap_or(&serde_json::Value::Null),
    )
    .unwrap_or_default();

    let limit = 4 * 1024;

    let chars = value.chars().collect::<Vec<_>>();

    if chars.len() > limit {
        let value = chars.iter().take(limit).collect::<String>();

        format!("{}...（剩余{}字符）", value, chars.len() - limit)
    } else {
        value
    }
}

fn format_views(views: &[Arc<serde_json::Value>]) -> String {
    views.iter().fold(String::new(), |mut acc, view| {
        if acc.len() > 4096 {
            return acc;
        }

        let view: ContentView = match serde_json::from_value(view.as_ref().clone()) {
            Ok(view) => view,
            Err(_) => return acc,
        };

        match view {
            ContentView::Markdown(text) => {
                acc += "\n<markdown>\n";
                acc += &text;
                acc += "\n</markdown>:\n";
            }
            ContentView::Card(ContentCard {
                title,
                description,
                content,
                ..
            }) => {
                acc += "\n<card>\n";
                acc += &title;
                acc += "\n";
                if let Some(description) = description {
                    acc += &description;
                }
                acc += "\n";
                if let Some(ContentViewOrText::Text(text)) = content {
                    acc += &text;
                }
                acc += "\n";
                acc += "\n</card>:\n";
            }
            _ => {}
        }

        if acc.len() > 4096 {
            acc = acc.chars().take(4096).collect();
            acc += "<因为长度省略更多信息>"
        }

        acc
    })
}
