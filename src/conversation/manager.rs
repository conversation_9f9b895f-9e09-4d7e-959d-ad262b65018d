use std::collections::HashMap;
use std::sync::Arc;

use bson::doc;
use entity::{ID, IN, NE, SET};
use futures::StreamExt;
use ractor::{Actor, ActorRef, ActorStatus};
use time::OffsetDateTime;
use tokio::sync::RwLock;
use uuid::Uuid;

use crate::conversation::{
    Conversation, ConversationCleanEvent, ConversationStatus, ConversationStatusEvent,
    ConversationStreamEvent, controller::ConversationController,
};

use super::{
    actor::{ConversationActor, ConversationActorEvent},
    runner::ConversationRunner,
};

/// 对话管理器
///
/// 负责管理所有对话，提供对话的创建、获取、更新等功能
pub struct ConversationManager {
    /// 对话哈希表，key为对话ID
    conversations: RwLock<HashMap<Uuid, Arc<RwLock<Conversation>>>>,

    actors: Rw<PERSON><PERSON><HashMap<Uuid, ActorRef<ConversationActorEvent>>>,

    channels: RwLock<HashMap<Uuid, tokio::sync::broadcast::Sender<ConversationStreamEvent>>>,

    runner: Arc<ConversationRunner>,

    //(user_id,tenant_id)
    conversation_status_channels:
        RwLock<HashMap<(String, String), tokio::sync::broadcast::Sender<ConversationStatusEvent>>>,

    cleaner_channel: Arc<tokio::sync::broadcast::Sender<ConversationCleanEvent>>,
}

impl ConversationManager {
    /// 创建一个新的对话管理器实例
    pub async fn new(runner: Arc<ConversationRunner>) -> error::Result<Self> {
        let manager = Self {
            conversations: RwLock::new(HashMap::new()),
            actors: RwLock::new(HashMap::new()),
            channels: RwLock::new(HashMap::new()),
            runner,
            conversation_status_channels: RwLock::new(HashMap::new()),
            cleaner_channel: Arc::new(tokio::sync::broadcast::Sender::new(4096)),
        };

        // 启动cleaner_channel监听器
        tokio::spawn(manager.start_cleaner_listener()).await;

        Ok(manager)
    }

    pub fn runner(&self) -> Arc<ConversationRunner> {
        self.runner.clone()
    }

    /// 创建一个新的对话
    pub async fn create_conversation(
        &self,
        title: Option<String>,
        user_id: String,
        tenant_id: String,
        user_name: String,
    ) -> Arc<RwLock<Conversation>> {
        let conversation_id = Uuid::now_v7();
        let now = OffsetDateTime::now_utc();

        // 创建对话实例
        let conversation = Conversation {
            id: conversation_id,
            title: title.unwrap_or_default(),
            create_time: now,
            update_time: None,
            current_message_id: None,
            status: ConversationStatus::Ready,
            metadata: serde_json::Value::Object(serde_json::Map::new()),
            messages: HashMap::new(),
            user_id,
            tenant_id,
            user_name,
            sub_status: crate::conversation::ConversationSubStatus::None,
        };
        let option: Option<entity::document::Conversation> = match conversation.clone().try_into() {
            Ok(document) => Some(document),
            Err(e) => {
                tracing::error!("conversation 转换失败{e:?}");
                None
            }
        };

        // 存储对话
        let conversation_arc = Arc::new(RwLock::new(conversation));
        {
            let mut conversations = self.conversations.write().await;
            conversations.insert(conversation_id, Arc::clone(&conversation_arc));
        }

        if let Some(document) = option {
            match self.runner.document_repository.insert_one(document).await {
                Ok(_) => {}
                Err(e) => tracing::error!("保存对话失败: {:?}", e),
            };
        }

        // 创建对话 actor
        let _ = self.create_actor(Arc::clone(&conversation_arc)).await;

        conversation_arc
    }

    // create actor
    pub async fn create_actor(
        &self,
        conversation: Arc<RwLock<Conversation>>,
    ) -> ActorRef<ConversationActorEvent> {
        let c = conversation.read().await;
        let id = c.id;
        let user_id = c.user_id.clone();
        let tenant_id = c.tenant_id.clone();
        drop(c);

        let channel = self.ensure_channel(&id).await;

        let mut actors = self.actors.write().await;

        if let Some(actor) = actors.get(&id) {
            if actor.get_status() == ActorStatus::Running {
                return actor.clone();
            }
        }

        let (actor, _) = Actor::spawn(
            None,
            ConversationActor {
                controller: Arc::new(ConversationController {
                    inner: conversation.clone(),
                    sender: channel.clone(),
                    repository: self.runner.document_repository.clone(),
                    status_sender: self.ensure_status_channel(user_id, tenant_id).await,
                }),
                runner: self.runner.clone(),
                sender: channel,
                cleaner_sender: self.cleaner_channel.clone(),
            },
            (),
        )
        .await
        .unwrap();

        // 保存actor
        actors.insert(id, actor.clone());

        actor
    }

    /// 获取指定ID的对话
    pub async fn get_conversation(&self, id: &Uuid) -> error::Result<Arc<RwLock<Conversation>>> {
        let conversations = self.conversations.read().await;
        match conversations.get(id).cloned() {
            Some(conversation) => Ok(conversation),
            None => {
                // 从数据库中加载
                self.get_conversation_from_db(id).await
            }
        }
    }

    async fn get_conversation_from_db(
        &self,
        id: &Uuid,
    ) -> error::Result<Arc<RwLock<Conversation>>> {
        match self
            .runner()
            .document_repository
            .load_conversation_by_id(*id)
            .await
        {
            Ok(conversation_document) => match Conversation::try_from(conversation_document) {
                Ok(conversation) => Ok(Arc::new(RwLock::new(conversation))),
                Err(e) => {
                    tracing::error!("转换对话失败: {:?}", e);
                    Err(e)
                }
            },
            Err(e) => Err(e),
        }
    }

    /// 获取 actor
    pub async fn get_actor(&self, id: &Uuid) -> Option<ActorRef<ConversationActorEvent>> {
        let actors = self.actors.read().await;
        actors
            .get(id)
            .filter(|a| a.get_status() == ActorStatus::Running)
            .cloned()
    }

    pub async fn ensure_actor(&self, id: Uuid) -> error::Result<ActorRef<ConversationActorEvent>> {
        match self.get_actor(&id).await {
            Some(actor) => Ok(actor),
            None => {
                // 历史数据需要重建actor
                match self.get_conversation_from_db(&id).await {
                    Ok(conversation) => Ok(self.create_actor(conversation).await),
                    Err(e) => {
                        tracing::error!("{e:?}");
                        Err(error::MongoError::RecordNotFound(id).into())
                    }
                }
            }
        }
    }

    pub async fn get_all_actor(&self) -> Vec<ActorRef<ConversationActorEvent>> {
        let actors = self.actors.read().await;
        actors.values().cloned().collect()
    }

    pub async fn break_actor(&self, id: &Uuid) -> Option<ActorRef<ConversationActorEvent>> {
        let actor = self.ensure_actor(*id).await;

        if let Ok(actor) = actor {
            let _ = actor.send_message(ConversationActorEvent::Break);
            Some(actor)
        } else {
            None
        }
    }

    pub async fn get_receiver(
        &self,
        id: &Uuid,
    ) -> Option<tokio::sync::broadcast::Receiver<ConversationStreamEvent>> {
        let channels = self.channels.read().await;
        channels.get(id).map(|c| c.subscribe())
    }

    pub async fn ensure_channel(
        &self,
        id: &Uuid,
    ) -> tokio::sync::broadcast::Sender<ConversationStreamEvent> {
        let mut channels = self.channels.write().await;
        channels
            .entry(*id)
            .or_insert_with(|| tokio::sync::broadcast::Sender::new(4096))
            .clone()
    }

    pub async fn ensure_status_channel(
        &self,
        user_id: String,
        tenant_id: String,
    ) -> tokio::sync::broadcast::Sender<ConversationStatusEvent> {
        let mut channels = self.conversation_status_channels.write().await;
        channels
            .entry((user_id, tenant_id))
            .or_insert_with(|| tokio::sync::broadcast::Sender::new(4096))
            .clone()
    }

    pub async fn list_conversation_status_by_user(
        &self,
        user_id: String,
        tenant_id: String,
    ) -> Vec<ConversationStatusEvent> {
        // let conversations_map = self.conversations.read().await;
        let conversation_locks: Vec<Arc<RwLock<Conversation>>> = {
            // 使用一个新的作用域来限制读锁的持有时间,防止router中报生命周期错误
            let conversations_map = self.conversations.read().await;
            conversations_map.values().cloned().collect()
        };

        const CONCURRENCY_LIMIT: usize = 20;

        futures::stream::iter(conversation_locks)
            .map(|conversation_lock| {
                let uid = user_id.clone();
                let tid = tenant_id.clone();
                let lock_clone = conversation_lock.clone();

                async move {
                    let conversation = lock_clone.read().await;

                    if conversation.user_id == uid && conversation.tenant_id == tid {
                        Some(ConversationStatusEvent {
                            id: conversation.id,
                            status: conversation.status.clone(),
                            user_id: conversation.user_id.clone(),
                            tenant_id: conversation.tenant_id.clone(),
                            sub_status: conversation.sub_status.clone(),
                        })
                    } else {
                        None
                    }
                }
            })
            .buffer_unordered(CONCURRENCY_LIMIT) // 3. 以指定的并发数执行所有判断任务
            .collect::<Vec<Option<ConversationStatusEvent>>>() // 4. 收集所有 Some(...) 的结果
            .await
            .into_iter()
            .flatten()
            .collect()
    }

    /// 删除指定ID的对话
    pub async fn delete_conversation(&self, id: &Uuid) -> error::Result<()> {
        use entity::document::*;
        // 从哈希表中删除对话
        let mut conversations = self.conversations.write().await;
        let _ = conversations.remove(id).is_some();

        let repository = self.runner().document_repository.clone();

        let messages = repository
            .find::<entity::document::Message>(doc! {"conversation_id":id})
            .await?;
        let message_ids = messages.into_iter().map(|m| m.id).collect::<Vec<_>>();

        repository
            .delete::<entity::document::Content>(doc! {
                    ContentField::MessageId.as_str(): {
                        IN:message_ids,
                    }
            })
            .await?;
        repository
            .delete::<entity::document::Message>(doc! {
                MessageField::ConversationId.as_str(): id
            })
            .await?;
        repository
            .delete::<entity::document::Conversation>(doc! {
                ID: id
            })
            .await?;

        Ok(())
    }

    pub async fn delete_conversation_by_condition(
        &self,
        ids: Vec<Uuid>,
        time_range: Option<(OffsetDateTime, OffsetDateTime)>,
    ) -> error::Result<()> {
        use entity::document::ConversationField;

        let repository = self.runner().document_repository.clone();

        let mut r#match = doc! {};
        if !ids.is_empty() {
            r#match.insert(
                ID,
                doc! {
                IN: ids.clone()
                },
            );
        }

        if let Some((start, end)) = time_range {
            r#match.insert(
                ConversationField::CreateTime.as_str(),
                doc! {
                    "$gte": start,
                    "$lte": end,
                },
            );
        }

        if r#match.is_empty() {
            return Err(error::MongoError::FilterCanNotBeEmpty.into());
        }

        let ids = repository.find_all_id_by_condition(r#match).await?;

        let conversation_filter = doc! {
            ID: {
                IN: ids.0.clone()
            }
        };

        let message_filter = doc! {
            ID: {
                IN: ids.1
            }
        };

        let content_filter = doc! {
            ID: {
                IN: ids.2
            }
        };

        match repository
            .delete::<entity::document::Conversation>(conversation_filter)
            .await
        {
            Ok(_) => {
                // 从哈希表中删除对话
                let mut conversations = self.conversations.write().await;
                conversations.retain(|id, _| !ids.0.contains(id));

                // 删除message和content
                repository
                    .delete::<entity::document::Message>(message_filter)
                    .await?;

                repository
                    .delete::<entity::document::Content>(content_filter)
                    .await?;

                Ok(())
            }
            Err(e) => Err(e),
        }
    }

    pub async fn delete_all_conversation(
        &self,
        user_id: String,
        tenant_id: String,
    ) -> error::Result<()> {
        use entity::document::*;

        let repository = self.runner().document_repository.clone();

        let ids = repository
            .find_all_id_by_condition(doc! {
                ConversationField::UserId.as_str(): user_id,
                ConversationField::TenantId.as_str(): tenant_id,
            })
            .await?;

        let conversation_filter = doc! {
            ID: {
                IN: ids.0.clone()
            }
        };

        let message_filter = doc! {
            ID: {
                IN: ids.1
            }
        };

        let content_filter = doc! {
            ID: {
                IN: ids.2
            }
        };

        match repository
            .delete::<entity::document::Conversation>(conversation_filter)
            .await
        {
            Ok(_) => {
                // 从哈希表中删除对话
                let mut conversations = self.conversations.write().await;
                conversations.clear();

                // 删除message和content
                repository
                    .delete::<entity::document::Message>(message_filter)
                    .await?;

                repository
                    .delete::<entity::document::Content>(content_filter)
                    .await?;

                Ok(())
            }
            Err(e) => Err(e),
        }
    }

    #[tracing::instrument(skip_all)]
    pub async fn reset_all_conversation_status(&self) -> error::Result<()> {
        use entity::document::*;

        self.runner
            .document_repository
            .update_many::<entity::document::Conversation>(
                doc! {
                    ConversationField::Status.as_str(): {
                        NE: entity::to_bson!(&ConversationStatus::Ready),
                    }
                },
                doc! {
                    SET:{
                        ConversationField::Status.as_str(): entity::to_bson!(&ConversationStatus::Ready),
                    }
                },
            )
            .await
    }

    async fn start_cleaner_listener(&self) {
        let mut receiver = self.cleaner_channel.subscribe();
        // let conversations = self.conversations.read().await.clone();
        // let actors = self.actors.clone();
        // let channels = self.channels.clone();

        tracing::info!("启动cleaner_channel监听器");

        while let Ok(event) = receiver.recv().await {
            match event {
                crate::conversation::ConversationCleanEvent::Clean(conversation_id) => {
                    tracing::info!("收到清理事件，对话ID: {}", conversation_id);
                    {
                        tracing::info!("size: {:?}", self.conversations.read().await.len());
                    }
                    // // 清理conversations
                    // {
                    //     let mut conversations_guard = self.conversations.write().await;
                    //     if conversations_guard.remove(&conversation_id).is_some() {
                    //         tracing::debug!("从conversations中移除对话: {}", conversation_id);
                    //     }
                    // }

                    // // 清理actors
                    // {
                    //     let mut actors_guard = self.actors.write().await;
                    //     if let Some(actor) = actors_guard.remove(&conversation_id) {
                    //         tracing::debug!("停止并移除actor: {}", conversation_id);
                    //         actor.kill();
                    //     }
                    // }

                    // // 清理channels
                    // {
                    //     let mut channels_guard = self.channels.write().await;
                    //     if channels_guard.remove(&conversation_id).is_some() {
                    //         tracing::debug!("从channels中移除对话: {}", conversation_id);
                    //     }
                    // }

                    tracing::info!("完成对话清理: {}", conversation_id);
                }
            }
        }

        tracing::warn!("cleaner_channel监听器退出");
    }

    pub fn get_cleaner_sender(
        &self,
    ) -> Arc<tokio::sync::broadcast::Sender<crate::conversation::ConversationCleanEvent>> {
        self.cleaner_channel.clone()
    }
}
