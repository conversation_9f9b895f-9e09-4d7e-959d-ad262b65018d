use content::{ContentDeltaOperation, ContentItemInner};
use interaction::InteractionResult;
use serde::{Deserialize, Serialize, Serializer};
use std::collections::{HashMap, LinkedList};
use time::OffsetDateTime;
use uuid::Uuid;

pub mod actor;
pub mod controller;
pub mod manager;
pub mod message;
pub mod parser;
pub mod runner;

pub use message::*;

/// 对话状态
#[derive(Debug, Clone, Deserialize, PartialEq)]
pub enum ConversationStatus {
    /// 对话就绪状态，可以进行用户输入
    #[serde(rename = "ready")]
    Ready,
    /// 对话中状态，指发出对话后接收和解析的过程
    #[serde(rename = "chat")]
    Chat,
    /// 对话内的工作流执行中状态
    #[serde(rename = "execute")]
    Execute,
    /// 交互状态
    #[serde(rename = "interaction")]
    Interaction(ConversationSubStatus),
}

impl Serialize for ConversationStatus {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match self {
            ConversationStatus::Ready => serializer.serialize_str("ready"),
            ConversationStatus::Chat => serializer.serialize_str("chat"),
            ConversationStatus::Execute => serializer.serialize_str("execute"),
            ConversationStatus::Interaction(_) => serializer.serialize_str("interaction"),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Default)]
pub enum ConversationSubStatus {
    #[serde(rename = "select")]
    Select,
    /// 确认类型交互
    #[serde(rename = "confirm")]
    Confirm,
    /// 运行前确认
    #[serde(rename = "execute_confirm")]
    ExecuteConfirm,
    /// 打开页面类型交互
    #[serde(rename = "open_page")]
    OpenPage,
    /// 创建动态表单
    #[serde(rename = "form")]
    Form,
    #[serde(rename = "")]
    #[default]
    None,
}

impl From<interaction::InteractionContent> for ConversationSubStatus {
    fn from(value: interaction::InteractionContent) -> Self {
        match value {
            interaction::InteractionContent::Select { .. } => ConversationSubStatus::Select,
            interaction::InteractionContent::Confirm { .. } => ConversationSubStatus::Confirm,
            interaction::InteractionContent::ExecuteConfirm { .. } => {
                ConversationSubStatus::ExecuteConfirm
            }
            interaction::InteractionContent::OpenPage { .. } => ConversationSubStatus::OpenPage,
            interaction::InteractionContent::Form { .. } => ConversationSubStatus::Form,
        }
    }
}

impl From<ConversationSubStatus> for entity::document::ConversationSubStatus {
    fn from(value: ConversationSubStatus) -> Self {
        match value {
            ConversationSubStatus::Select => entity::document::ConversationSubStatus::Select,
            ConversationSubStatus::Confirm => entity::document::ConversationSubStatus::Confirm,
            ConversationSubStatus::ExecuteConfirm => {
                entity::document::ConversationSubStatus::ExecuteConfirm
            }
            ConversationSubStatus::OpenPage => entity::document::ConversationSubStatus::OpenPage,
            ConversationSubStatus::Form => entity::document::ConversationSubStatus::Form,
            ConversationSubStatus::None => entity::document::ConversationSubStatus::None,
        }
    }
}

impl From<entity::document::ConversationSubStatus> for ConversationSubStatus {
    fn from(value: entity::document::ConversationSubStatus) -> Self {
        match value {
            entity::document::ConversationSubStatus::Select => ConversationSubStatus::Select,
            entity::document::ConversationSubStatus::Confirm => ConversationSubStatus::Confirm,
            entity::document::ConversationSubStatus::ExecuteConfirm => {
                ConversationSubStatus::ExecuteConfirm
            }
            entity::document::ConversationSubStatus::OpenPage => ConversationSubStatus::OpenPage,
            entity::document::ConversationSubStatus::Form => ConversationSubStatus::Form,
            entity::document::ConversationSubStatus::None => ConversationSubStatus::None,
        }
    }
}

impl From<entity::document::ConversationStatus> for ConversationStatus {
    fn from(value: entity::document::ConversationStatus) -> Self {
        match value {
            entity::document::ConversationStatus::Ready => Self::Ready,
            entity::document::ConversationStatus::Chat => Self::Chat,
            entity::document::ConversationStatus::Execute => Self::Execute,
            entity::document::ConversationStatus::Interaction => {
                Self::Interaction(Default::default())
            }
        }
    }
}

impl From<ConversationStatus> for entity::document::ConversationStatus {
    fn from(value: ConversationStatus) -> Self {
        match value {
            ConversationStatus::Ready => Self::Ready,
            ConversationStatus::Chat => Self::Chat,
            ConversationStatus::Execute => Self::Execute,
            ConversationStatus::Interaction(..) => Self::Interaction,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationStatusEvent {
    pub id: Uuid,
    pub status: ConversationStatus,
    pub sub_status: ConversationSubStatus,
    pub user_id: String,
    pub tenant_id: String,
}

/// 对话结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Conversation {
    /// 对话ID
    pub id: Uuid,
    /// 对话标题
    pub title: String,
    /// 创建时间
    #[serde(with = "time::serde::rfc3339")]
    pub create_time: OffsetDateTime,
    /// 更新时间
    #[serde(
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub update_time: Option<OffsetDateTime>,
    /// 当前消息ID
    pub current_message_id: Option<Uuid>,
    /// 对话状态
    pub status: ConversationStatus,
    /// 元数据
    pub metadata: serde_json::Value,
    /// 消息哈希表，key为消息ID
    pub messages: HashMap<uuid::Uuid, MessageNode>,

    pub user_id: String,
    pub tenant_id: String,
    pub user_name: String,
    pub sub_status: ConversationSubStatus,
}

#[allow(dead_code)]
impl Conversation {
    pub fn get_context(&self) -> Vec<Message> {
        // from current_message_id to root message
        // then reverse
        let mut context = vec![];
        let mut current_message_id = match self.current_message_id {
            Some(id) => id,
            None => return context,
        };

        let mut visited = HashMap::new();
        while let Some(message_node) = self.messages.get(&current_message_id) {
            if visited.contains_key(&message_node.id) {
                break;
            }
            visited.insert(message_node.id, true);
            context.push(message_node.message.clone());
            current_message_id = message_node.parent_id.unwrap_or(current_message_id);
        }
        context.reverse();
        context
    }

    pub fn get_context_with_workflow(&self, workflow_id: uuid::Uuid) -> Vec<Message> {
        let assistant_message_node = self
            .messages
            .values()
            .filter(|m| matches!(m.message.author, Author::Assistant { .. }))
            .find(|m|
                m.message.content.iter().any(|c|
                    matches!(&c.content, ContentItemInner::Workflow { workflow, .. } if *workflow == workflow_id)
                )
            );

        let assistant_message = match assistant_message_node {
            Some(node) => &node.message,
            None => return vec![],
        };

        let mut messages = HashMap::new();

        let mut parent_messages = LinkedList::new();
        // 向下查找所有的 tool 类 message

        parent_messages.push_back(assistant_message);

        while let Some(parent_message) = parent_messages.pop_front() {
            match messages.insert(parent_message.id, parent_message) {
                Some(_) => continue,
                None => {
                    if let Some(current_node) = self.messages.get(&parent_message.id) {
                        current_node
                            .children
                            .iter()
                            .filter_map(|id| self.messages.get(id))
                            .filter(|n| matches!(n.message.author, Author::Tool { .. }))
                            .for_each(|n| parent_messages.push_back(&n.message));
                    }
                }
            }
        }

        messages.values().cloned().cloned().collect()
    }

    pub fn get_context_until_user_message(&self) -> Vec<Message> {
        // from current_message_id to root message
        // then reverse
        let mut context = vec![];
        let mut current_message_id = match self.current_message_id {
            Some(id) => id,
            None => return context,
        };

        let mut visited = HashMap::new();
        while let Some(message_node) = self.messages.get(&current_message_id) {
            if visited.contains_key(&message_node.id) {
                break;
            }
            visited.insert(message_node.id, true);
            context.push(message_node.message.clone());
            current_message_id = message_node.parent_id.unwrap_or(current_message_id);
        }
        context.reverse();
        context
    }

    pub async fn try_into_documents(
        &mut self,
    ) -> error::Result<(
        entity::document::Conversation,
        Vec<(entity::document::Message, Vec<entity::document::Content>)>,
    )> {
        let documents = self
            .messages
            .values_mut()
            .map(|node| {
                let message = &mut node.message;
                message.try_into_document(node.parent_id, node.children.clone(), self.id)
            })
            .collect::<error::Result<Vec<_>>>();
        let documents = documents?;

        let value = self.clone();
        let document = entity::document::Conversation {
            id: value.id,
            title: value.title,
            create_time: value.create_time,
            update_time: value.update_time,
            current_message_id: value.current_message_id,
            status: value.status.into(),
            metadata: Some(entity::to_bson!(&value.metadata)),
            user_id: value.user_id,
            tenant_id: value.tenant_id,
            user_name: value.user_name,
            messages: vec![],
            sub_status: value.sub_status.into(),
        };

        Ok((document, documents))
    }
}

impl TryFrom<entity::document::Conversation> for Conversation {
    type Error = error::Error;

    fn try_from(value: entity::document::Conversation) -> error::Result<Self> {
        let messages = value
            .messages
            .iter()
            .flat_map(|message_document| {
                let children = entity::from_bson!(message_document.children.clone());
                match Message::try_from_document(
                    message_document.clone(),
                    message_document.contents.clone(),
                ) {
                    Ok(message) => {
                        let node = MessageNode {
                            id: message.id,
                            parent_id: message_document.parent_id,
                            children,
                            message,
                        };
                        Ok((node.id, node))
                    }
                    Err(e) => {
                        tracing::error!("Failed to deserialize message: {}", e);
                        Err(e)
                    }
                }
            })
            .collect::<HashMap<_, _>>();

        Ok(Self {
            id: value.id,
            title: value.title,
            create_time: value.create_time,
            update_time: value.update_time,
            current_message_id: value.current_message_id,
            status: value.status.into(),
            metadata: match value.metadata {
                Some(metadata) => {
                    entity::from_bson!(metadata)
                }
                None => serde_json::Value::Null,
            },
            messages,
            user_id: value.user_id,
            tenant_id: value.tenant_id,
            user_name: value.user_name,
            sub_status: value.sub_status.into(),
        })
    }
}

impl TryFrom<Conversation> for entity::document::Conversation {
    type Error = error::Error;

    fn try_from(value: Conversation) -> Result<Self, Self::Error> {
        Ok(Self {
            id: value.id,
            title: value.title,
            create_time: value.create_time,
            update_time: value.update_time,
            current_message_id: value.current_message_id,
            status: value.status.into(),
            metadata: Some(entity::to_bson!(&value.metadata)),
            user_id: value.user_id,
            tenant_id: value.tenant_id,
            user_name: value.user_name,
            messages: vec![],
            sub_status: value.sub_status.into(),
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationPlayback {
    #[serde(default)]
    pub id: Option<Uuid>,
    pub title: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    pub conversation_id: Uuid,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub r#type: Option<String>,
    pub images: Vec<ImageInfo>,
    pub user_id: String,
    pub tenant_id: String,
}

impl TryFrom<entity::document::Playback> for ConversationPlayback {
    type Error = error::Error;

    fn try_from(value: entity::document::Playback) -> error::Result<Self> {
        Ok(Self {
            id: Some(value.id),
            title: value.title,
            description: value.description,
            conversation_id: value.conversation_id,
            r#type: value.r#type,
            images: entity::from_bson!(value.images),
            user_id: value.user_id,
            tenant_id: value.tenant_id,
        })
    }
}

impl TryFrom<ConversationPlayback> for entity::document::Playback {
    type Error = error::Error;

    fn try_from(value: ConversationPlayback) -> error::Result<Self> {
        Ok(Self {
            id: value.id.unwrap_or(Uuid::now_v7()),
            title: value.title,
            description: value.description,
            conversation_id: value.conversation_id,
            r#type: value.r#type,
            images: entity::to_bson!(&value.images),
            user_id: value.user_id,
            tenant_id: value.tenant_id,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageInfo {
    pub bucket: String,
    pub object: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub version_id: Option<String>,
}

/// 对话列表请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationListRequest {
    /// 查询限制
    pub limit: Option<usize>,
    /// 查询偏移
    pub offset: Option<usize>,
    pub reversed: Option<bool>,
    /// 模糊查询标题
    pub title: Option<String>,
}

/// 对话列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationListResponse {
    /// 对话列表
    pub conversations: Vec<ConversationListItem>,
    /// 查询限制
    pub limit: usize,
    /// 查询偏移
    pub offset: usize,
    /// 总数
    pub total: usize,
}

/// 对话列表项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationListItem {
    /// 对话ID
    pub id: Uuid,
    /// 对话标题
    pub title: String,
    /// 创建时间
    #[serde(with = "time::serde::rfc3339")]
    pub create_time: OffsetDateTime,
    /// 更新时间
    #[serde(
        with = "time::serde::rfc3339::option",
        skip_serializing_if = "Option::is_none"
    )]
    pub update_time: Option<OffsetDateTime>,

    pub status: ConversationStatus,

    pub sub_status: ConversationSubStatus,
}

// #[derive(Debug, Clone, Serialize, Deserialize)]
// pub struct ChatTextLayout {
//     pub name: String,
//     pub position: Vec<ChatTextPosition>,
// }

// #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
// pub enum ChatTextPosition {
//     #[serde(rename = "content")]
//     Content,
//     #[serde(rename = "details")]
//     Details,
// }

/// 消息内容增量更新负载
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageContentDeltaPayload {
    /// 消息ID
    pub message_id: Uuid,
    /// 路径
    pub path: Vec<serde_json::Value>,
    /// 操作
    pub operation: ContentDeltaOperation,
    /// 内容
    pub content: serde_json::Value,
}

/// 对话状态变更负载
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationStatusChangedPayload {
    /// 状态
    pub status: ConversationStatus,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Map<String, serde_json::Value>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewMessagePayload {
    pub parent_id: Option<Uuid>,
    /// 消息内容
    pub message: Message,
}

/// 消息状态变更负载
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageStatusChangedPayload {
    /// 消息ID
    pub message_id: Uuid,
    /// 状态
    pub status: MessageStatus,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub author: Option<Author>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Map<String, serde_json::Value>>,
}

/// 用户交互请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InteractionRequest {
    /// 消息ID
    pub message_id: Uuid,
    /// 内容索引
    pub content_index: usize,
    /// 结果
    #[serde(flatten)]
    pub result: InteractionResult,
}

/// 流式消息请求
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum StreamRequest {
    #[serde(rename = "user_input")]
    UserInput(UserInput),
    #[serde(rename = "workflow_input")]
    WorkflowInput(WorkflowInput),
    #[serde(rename = "continue")]
    Continue(ContinueRequest),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInput {
    /// 父消息ID
    pub parent_id: Option<Uuid>,
    /// 消息内容
    pub message: StreamRequestMessage,
    /// 是否使用深度探索
    #[serde(skip_serializing_if = "Option::is_none")]
    pub use_deep_explore: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowInput {
    pub workflow_id: Uuid,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<StreamRequestMessage>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub workflow_name: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub input: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContinueRequest {}

/// 流式消息请求内的消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamRequestMessage {
    /// 消息内容
    pub content: Vec<ContentItemInner>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ConversationStreamEvent {
    /// 全量推送
    #[serde(rename = "full")]
    Full(Conversation),
    /// 新消息
    #[serde(rename = "new_message")]
    NewMessage(NewMessagePayload),
    /// 消息状态变更
    #[serde(rename = "message_status_changed")]
    MessageStatusChanged(MessageStatusChangedPayload),
    /// 对话状态变更
    #[serde(rename = "conversation_status_changed")]
    ConversationStatusChanged(ConversationStatusChangedPayload),
    /// 消息内容增量更新
    #[serde(rename = "message_content_delta")]
    MessageContentDelta(MessageContentDeltaPayload),
}

impl ConversationStreamEvent {
    pub fn is_ready(&self) -> bool {
        match self {
            ConversationStreamEvent::ConversationStatusChanged(
                ConversationStatusChangedPayload { status, .. },
            ) => *status == ConversationStatus::Ready,
            ConversationStreamEvent::Full(conversation) => {
                conversation.status == ConversationStatus::Ready
            }
            _ => false,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConversationCleanEvent {
    Clean(Uuid),
}
