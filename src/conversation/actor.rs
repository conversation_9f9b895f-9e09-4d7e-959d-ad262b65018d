use std::sync::Arc;

use actor::{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChatWorkflowActorEvent, ChatWorkflowActorNotification,
    ExploreActor, ExploreActorEvent, ExploreActorNotification, WorkflowActor, WorkflowActorEvent,
    WorkflowActorNotification, WorkflowActorOptions,
};
use ai::{ExploreEvent, SimpleWorkflow};
use bson::doc;
use content::{ContentCard, ContentCardDelta, ContentItemInner, ContentView, ContentViewOrDelta};
use entity::ID;
use integral::{IntegralEvent, IntegralType};
use interaction::InteractionContent;
use ractor::{Actor, ActorProcessingErr, ActorRef, OutputPort, RpcReplyPort, SupervisionEvent};
use time::OffsetDateTime;
use uuid::Uuid;
use workflow::Workflow;

use crate::conversation::{
    ConversationCleanEvent, UserInput, WorkflowInput, controller::ConversationController,
};

use super::{
    Author, ConversationStatus, ConversationStreamEvent, InteractionRequest, Message,
    MessageStatus, runner::ConversationRunner,
};

pub struct ConversationActor {
    pub controller: Arc<ConversationController>,
    pub runner: Arc<ConversationRunner>,
    pub sender: tokio::sync::broadcast::Sender<ConversationStreamEvent>,
    pub cleaner_sender: Arc<tokio::sync::broadcast::Sender<ConversationCleanEvent>>,
}

#[derive(Debug)]
pub enum ConversationActorEvent {
    UserInput(UserInput),
    WorkflowInput(WorkflowInput),
    Break,
    Interaction(InteractionRequest, RpcReplyPort<Result<(), error::Error>>),

    // new
    ReceiveWorkflowNotification(Box<WorkflowActorNotification>),
    ReceiveExploreEvent(Vec<ExploreEvent>),
    ReceiveChatDeltaBlock(ChatDeltaBlock),
    ReceiveMetadata(serde_json::Value),
    ReceiveError(String),
    ReceiveStart(Option<serde_json::Value>),
    ReceiveDone,
}

#[derive(Debug)]
pub enum ConversationActorState {
    /// 对话状态
    Ready,
    /// 对话中状态
    Chat {
        //
        actor: ActorRef<ChatWorkflowActorEvent>,
    },
    /// 探索状态
    Explore {
        //
        last_stage_id: Option<uuid::Uuid>,
        actor: ActorRef<ExploreActorEvent>,
    },
    /// 执行状态
    Execute {
        //
        actor: ActorRef<WorkflowActorEvent>,
    },
}

impl ConversationActor {
    async fn handle_user_input(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        request: UserInput,
        integral_result: error::Result<()>,
    ) -> Result<(), ActorProcessingErr> {
        if !matches!(state, ConversationActorState::Ready) {
            tracing::warn!("对话状态不正确，忽略用户输入，当前状态: {:?}", state);
            return Ok(());
        }

        let user_message_id = uuid::Uuid::now_v7();

        self.controller
            .add_message(Message {
                id: user_message_id,
                author: Author::User { name: None },
                create_time: OffsetDateTime::now_utc(),
                finish_time: None,
                metadata: serde_json::Map::new(),
                status: MessageStatus::Done,
                content: request
                    .message
                    .content
                    .clone()
                    .into_iter()
                    .map(Into::into)
                    .collect(),
            })
            .await?;

        match integral_result {
            Ok(_) => {}
            Err(e) => {
                tracing::error!("扣除积分失败: {:?}", e);
                self.handle_integral_error(myself, state, e).await?;
                return Ok(());
            }
        }

        if request.use_deep_explore.unwrap_or_default() {
            let output_port = Arc::new(OutputPort::default());

            output_port.subscribe(myself.clone(), |notification| match notification {
                ExploreActorNotification::Workflow(notification) => {
                    tracing::debug!("转换 Workflow 通知: {:?}", notification);

                    Some(ConversationActorEvent::ReceiveWorkflowNotification(
                        Box::new(notification),
                    ))
                }
                ExploreActorNotification::Receive(event) => {
                    tracing::debug!("转换 Receive 通知: {:?}", event);

                    Some(ConversationActorEvent::ReceiveExploreEvent(event))
                }
                ExploreActorNotification::Start(metadata) => {
                    tracing::debug!("转换 Start 通知: {:?}", metadata);

                    Some(ConversationActorEvent::ReceiveStart(metadata))
                }
                ExploreActorNotification::Error(error) => {
                    tracing::debug!("转换 Error 通知: {:?}", error);

                    Some(ConversationActorEvent::ReceiveError(error))
                }
                ExploreActorNotification::Done => {
                    tracing::debug!("转换 Done 通知");

                    Some(ConversationActorEvent::ReceiveDone)
                }
            });

            let (actor, _) = myself
                .spawn_linked(
                    None,
                    ExploreActor {
                        session_id: user_message_id,
                        tenant_id: self.controller.tenant_id().await,
                        user_id: Some(self.controller.user_id().await),
                        output_port,
                        model_runner: self.runner.model.clone(),
                        workflow_runner: self.runner.workflow.clone(),
                    },
                    (),
                )
                .await?;

            self.controller
                .set_conversation_status(ConversationStatus::Chat)
                .await?;

            actor.send_message(ExploreActorEvent::Start {
                topic: self.controller.get_last_user_input().await?,
            })?;

            *state = ConversationActorState::Explore {
                actor,
                last_stage_id: None,
            };
        } else {
            let output_port = Arc::new(OutputPort::default());

            output_port.subscribe(myself.clone(), |notification| match notification {
                ChatWorkflowActorNotification::Workflow(notification) => {
                    tracing::debug!("转换 Workflow 通知: {:?}", notification);

                    Some(ConversationActorEvent::ReceiveWorkflowNotification(
                        Box::new(notification),
                    ))
                }
                ChatWorkflowActorNotification::Receive(delta_block) => {
                    tracing::debug!("转换 Receive 通知: {:?}", delta_block);

                    Some(ConversationActorEvent::ReceiveChatDeltaBlock(delta_block))
                }
                ChatWorkflowActorNotification::Metadata(metadata) => {
                    tracing::debug!("转换 Metadata 通知: {:?}", metadata);

                    Some(ConversationActorEvent::ReceiveMetadata(metadata))
                }
                ChatWorkflowActorNotification::Start(metadata) => {
                    tracing::debug!("转换 Start 通知: {:?}", metadata);

                    Some(ConversationActorEvent::ReceiveStart(metadata))
                }
                ChatWorkflowActorNotification::Error(error) => {
                    tracing::debug!("转换 Error 通知: {:?}", error);

                    Some(ConversationActorEvent::ReceiveError(error))
                }
                ChatWorkflowActorNotification::Done => {
                    tracing::debug!("转换 Done 通知");

                    Some(ConversationActorEvent::ReceiveDone)
                }
            });

            let (actor, _) = myself
                .spawn_linked(
                    None,
                    ChatWorkflowActor {
                        session_id: user_message_id,
                        tenant_id: self.controller.tenant_id().await,
                        user_id: Some(self.controller.user_id().await),
                        output_port,
                        model_runner: self.runner.model.clone(),
                        workflow_runner: self.runner.workflow.clone(),
                        repository: self.runner.document_repository.clone(),
                    },
                    (),
                )
                .await?;

            self.controller
                .set_conversation_status(ConversationStatus::Chat)
                .await?;

            actor.send_message(ChatWorkflowActorEvent::Input(
                self.controller.get_context().await?,
            ))?;

            *state = ConversationActorState::Chat { actor };
        }

        Ok(())
    }

    async fn handle_workflow_input(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        request: WorkflowInput,
    ) -> Result<(), ActorProcessingErr> {
        if !matches!(state, ConversationActorState::Ready) {
            tracing::warn!("对话状态不正确，忽略工作流输入，当前状态: {:?}", state);
            return Ok(());
        }

        let workflow: Workflow = self
            .runner
            .document_repository
            .find_one::<entity::document::Workflow>(doc! {ID: request.workflow_id})
            .await?
            .ok_or_else(|| anyhow::anyhow!("工作流不存在"))?
            .try_into()?;

        let workflow = Arc::new(workflow);

        let user_message = if let Some(message) = request.message {
            Message::user_with_content(message.content.into_iter().map(|c| c.into()).collect())
        } else {
            Message::user(
                request
                    .workflow_name
                    .unwrap_or_else(|| "<未命名工作流>".to_string()),
            )
        };

        let user_message_id = user_message.id;

        self.controller.add_message(user_message).await?;

        let output_port = Arc::new(OutputPort::default());

        output_port.subscribe(myself.clone(), |notification| match notification {
            WorkflowActorNotification::Done { .. } => Some(ConversationActorEvent::ReceiveDone),
            _ => Some(ConversationActorEvent::ReceiveWorkflowNotification(
                Box::new(notification),
            )),
        });

        tracing::debug!("创建工作流 Actor");

        let (json, branch) = SimpleWorkflow::build_string(workflow.as_ref())?;

        let (workflow_actor, _) = myself
            .spawn_linked(
                None,
                WorkflowActor {
                    session_id: user_message_id,
                    tenant_id: self.controller.tenant_id().await,
                    user_id: Some(self.controller.user_id().await),
                    context: self.controller.get_context().await?,
                    output_port,
                    workflow,
                    runner: self.runner.workflow.clone(),
                    options: WorkflowActorOptions {
                        input_first_params: true,
                        confirm_start: false,
                        ..Default::default()
                    },
                },
                (),
            )
            .await?;

        self.controller
            .set_conversation_status(ConversationStatus::Execute)
            .await?;

        let mut metadata = serde_json::Map::new();

        metadata.insert("chat_type".into(), "agent".into());

        self.controller
            .add_message(Message {
                id: uuid::Uuid::now_v7(),
                author: Author::Assistant { name: None },
                create_time: OffsetDateTime::now_utc(),
                finish_time: None,
                metadata,
                status: MessageStatus::Execute,
                content: vec![
                    ContentItemInner::Json { json: json.clone() }.into(),
                    ContentItemInner::Branch {
                        branch: branch.clone(),
                    }
                    .into(),
                    ContentItemInner::Workflow {
                        workflow: request.workflow_id,
                        name: Some(self.controller.get_last_user_input().await?),
                        json: Some(json),
                        branch: Some(branch),
                    }
                    .into(),
                ],
            })
            .await?;

        workflow_actor.send_message(WorkflowActorEvent::Start)?;

        *state = ConversationActorState::Execute {
            actor: workflow_actor,
        };

        Ok(())
    }

    async fn break_inner(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
    ) -> Result<bool, ActorProcessingErr> {
        Ok(match state {
            ConversationActorState::Chat { actor } => {
                tracing::debug!("中断对话");
                actor.unlink(myself.get_cell());
                actor.kill();
                true
            }
            ConversationActorState::Explore { actor, .. } => {
                tracing::debug!("中断探索");
                actor.unlink(myself.get_cell());
                actor.kill();
                true
            }
            ConversationActorState::Execute { actor } => {
                tracing::debug!("中断执行");
                actor.unlink(myself.get_cell());
                actor.kill();
                true
            }
            _ => self.controller.inner.read().await.status.clone() != ConversationStatus::Ready,
        })
    }

    async fn break_all(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
    ) -> Result<(), ActorProcessingErr> {
        let is_break = self.break_inner(myself.clone(), state).await?;

        if is_break {
            *state = ConversationActorState::Ready;

            self.controller
                .add_content(ContentItemInner::ErrorText {
                    error_text: "用户已中断对话".to_string(),
                })
                .await?;

            self.controller
                .set_conversation_status(ConversationStatus::Ready)
                .await?;

            self.controller
                .set_current_message_status(MessageStatus::Cancel)
                .await?;
        }

        Ok(())
    }

    async fn handle_receive_start(
        &self,
        _myself: ActorRef<ConversationActorEvent>,
        _state: &mut ConversationActorState,
        metadata: Option<serde_json::Value>,
    ) -> Result<(), ActorProcessingErr> {
        self.controller
            .add_message(Message {
                id: uuid::Uuid::now_v7(),
                author: Author::Assistant { name: None },
                create_time: OffsetDateTime::now_utc(),
                finish_time: None,
                metadata: metadata
                    .map(|v| {
                        if let serde_json::Value::Object(map) = v {
                            map
                        } else {
                            let mut map = serde_json::Map::new();
                            map.insert("data".to_string(), v);
                            map
                        }
                    })
                    .unwrap_or_default(),
                status: MessageStatus::Chat,
                content: vec![],
            })
            .await?;

        Ok(())
    }

    async fn handle_receive_metadata(
        &self,
        _myself: ActorRef<ConversationActorEvent>,
        _state: &mut ConversationActorState,
        metadata: serde_json::Value,
    ) -> Result<(), ActorProcessingErr> {
        self.controller.update_metadata(metadata).await?;

        Ok(())
    }

    async fn handle_chat_delta_block(
        &self,
        _myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        delta_block: ChatDeltaBlock,
    ) -> Result<(), ActorProcessingErr> {
        if !matches!(state, ConversationActorState::Chat { .. }) {
            tracing::warn!("对话状态不正确，忽略对话流消息，当前状态: {:?}", state);
            return Ok(());
        }

        match delta_block {
            ChatDeltaBlock::Delta(delta) => {
                self.controller.apply_delta(delta.as_ref()).await?;
            }
            ChatDeltaBlock::Block(delta) => {
                tracing::debug!("接收 block: {:?}", delta);
                self.controller.apply_delta(delta.as_ref()).await?;
            }
            ChatDeltaBlock::Done(_) => {
                // tracing::debug!("接收 done: {:?}", deltas);
            }
        }

        Ok(())
    }

    async fn handle_explore_events(
        &self,
        _myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        events: Vec<ExploreEvent>,
    ) -> Result<(), ActorProcessingErr> {
        let last_stage_id = match state {
            ConversationActorState::Explore { last_stage_id, .. } => last_stage_id,
            _ => {
                tracing::warn!("探索状态不正确，忽略探索流消息，当前状态: {:?}", state);
                return Ok(());
            }
        };

        for event in events {
            match event {
                ExploreEvent::Stage { content } => {
                    tracing::debug!("探索流阶段: {}", content.title);

                    let stage_id = uuid::Uuid::now_v7();
                    *last_stage_id = Some(stage_id);

                    self.controller
                        .add_message(Message {
                            id: stage_id,
                            author: Author::Tool {
                                module: "".to_string(),
                                name: content.title.clone(),
                                step_id: None,
                                workflow_id: None,
                            },
                            create_time: OffsetDateTime::now_utc(),
                            finish_time: None,
                            metadata: serde_json::Map::new(),
                            status: MessageStatus::Execute,
                            content: vec![],
                        })
                        .await?;
                }
                ExploreEvent::Workflow { content } => {
                    tracing::debug!("探索流工作流: {:?}", content);
                    self.controller
                        .add_content(ContentItemInner::Json { json: content.json })
                        .await?;

                    self.controller
                        .add_content(ContentItemInner::Branch {
                            branch: content.branch,
                        })
                        .await?
                }
                ExploreEvent::Task { content } => {
                    tracing::debug!("探索流任务: {:?}", content);
                    match serde_json::from_value(content) {
                        Ok(content) => {
                            self.controller
                                .add_content(ContentItemInner::View {
                                    view: ContentView::Card(content),
                                    hide_all: None,
                                })
                                .await?
                        }
                        Err(e) => {
                            tracing::error!("探索流任务解析失败: {}", e);
                        }
                    }
                }
                ExploreEvent::TaskAppend { content } => {
                    tracing::debug!("探索流任务追加: {:?}", content);
                    match serde_json::from_value::<ContentCardDelta>(content) {
                        Ok(content) => self.controller.apply_card_delta(content).await?,
                        Err(e) => {
                            tracing::error!("探索流任务追加解析失败: {}", e);
                        }
                    }
                }
                ExploreEvent::Text { text } => {
                    tracing::debug!("探索流文本: {}", text);
                    self.controller
                        .add_content(ContentItemInner::Text { text })
                        .await?;
                }
                ExploreEvent::Interaction { content } => {
                    tracing::debug!("探索流交互: {:?}", content);

                    // 已经在 ExploreActor 中转换为 WorkflowActor 的 Interact 通知
                    // 此处不需要额外处理
                }
                ExploreEvent::Todolist => {
                    tracing::debug!("探索流待办事项");

                    self.controller
                        .update_metadata(serde_json::json!({
                            "chat_type": "todolist",
                            "use_deep_explore": true,
                            "todolist": true,
                        }))
                        .await?;
                }
                ExploreEvent::Done => {
                    tracing::debug!("探索流结束");
                    *state = ConversationActorState::Ready;

                    self.controller
                        .set_current_message_status(MessageStatus::Done)
                        .await?;

                    self.controller
                        .set_conversation_status(ConversationStatus::Ready)
                        .await?;

                    return Ok(());
                }
                ExploreEvent::Other(event) => {
                    tracing::debug!("探索流其他事件: {:?}", event);

                    self.controller
                        .add_content(ContentItemInner::Other(event))
                        .await?;
                }
            }
        }

        Ok(())
    }

    async fn handle_workflow_notification(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        notification: WorkflowActorNotification,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("处理 Workflow 通知: {:?}", notification);
        let conversation = self.controller.inner.read().await;
        let conversation_status = conversation.status.clone();
        drop(conversation);
        let parent_stage_id = match state {
            ConversationActorState::Explore { last_stage_id, .. } => *last_stage_id,
            ConversationActorState::Chat { .. } => {
                match conversation_status {
                    ConversationStatus::Chat => {}
                    _ => {
                        self.controller
                            .set_conversation_status(ConversationStatus::Chat)
                            .await?;
                    }
                }
                None
            }
            ConversationActorState::Execute { .. } => {
                match conversation_status {
                    ConversationStatus::Execute => {}
                    _ => {
                        self.controller
                            .set_conversation_status(ConversationStatus::Execute)
                            .await?;
                    }
                }
                None
            }
            _ => {
                tracing::warn!("对话状态不正确，忽略工作流通知，当前状态: {:?}", state);
                return Ok(());
            }
        };

        match notification {
            WorkflowActorNotification::Move { node, workflow_id } => {
                tracing::debug!("处理 Move 通知: {:?}", node);

                let mut metadata = serde_json::Map::new();
                if let Some(parent_stage_id) = parent_stage_id {
                    metadata.insert("parent_stage".into(), parent_stage_id.to_string().into());
                }

                self.controller
                    .add_message(Message {
                        id: uuid::Uuid::now_v7(),
                        author: Author::Tool {
                            name: node.tool.clone(),
                            module: node.module.clone(),
                            step_id: Some(node.id.clone()),
                            workflow_id: Some(workflow_id),
                        },
                        create_time: OffsetDateTime::now_utc(),
                        finish_time: None,
                        metadata,
                        status: MessageStatus::Chat,
                        content: vec![
                            ContentItemInner::Tool {
                                input: None,
                                output: None,
                            }
                            .into(),
                        ],
                    })
                    .await?;
            }
            WorkflowActorNotification::Execute { current_node } => {
                tracing::debug!("处理 Execute 通知: {:?}", current_node);

                self.controller
                    .add_content(ContentItemInner::View {
                        view: ContentView::Card(ContentCard {
                            title: "正在执行".to_string(),
                            description: Some(current_node.tool.clone()),
                            r#type: "execute".to_string(),
                            ..Default::default()
                        }),
                        hide_all: Some(true),
                    })
                    .await?;
            }
            WorkflowActorNotification::Interact { interaction } => {
                tracing::debug!("处理 Interact 通知: {:?}", interaction);

                self.controller
                    .set_conversation_status(ConversationStatus::Interaction(
                        interaction.content.clone().into(),
                    ))
                    .await?;

                self.controller
                    .set_current_message_status(MessageStatus::Interaction)
                    .await?;

                self.controller
                    .add_content(ContentItemInner::Interaction {
                        interaction: interaction.as_ref().clone(),
                    })
                    .await?;

                if let InteractionContent::ExecuteConfirm {
                    execute_confirm, ..
                } = interaction.content.clone()
                {
                    self.controller
                        .add_content(ContentItemInner::ExecuteConfirm { execute_confirm })
                        .await?;
                }
            }
            WorkflowActorNotification::InteractDone { interaction } => {
                tracing::debug!("处理 InteractDone 通知: {:?}", interaction);

                self.controller
                    .update_last_content(ContentItemInner::Interaction {
                        interaction: interaction.as_ref().clone(),
                    })
                    .await?;
            }
            WorkflowActorNotification::Converter { converter } => {
                tracing::debug!("处理 Converter 通知: {:?}", converter);

                self.controller
                    .add_content(ContentItemInner::View {
                        view: ContentView::Card(ContentCard {
                            title: "转换函数".to_string(),
                            r#type: "code".to_string(),
                            details: Some(format!("```python\n{converter}\n```").into()),
                            ..Default::default()
                        }),
                        hide_all: Some(true),
                    })
                    .await?;
            }
            WorkflowActorNotification::Params { params } => {
                tracing::debug!("处理 Params 通知: {:?}", params);

                self.controller
                    .add_content(ContentItemInner::View {
                        view: ContentView::Card(ContentCard {
                            title: "输入参数".to_string(),
                            r#type: "input".to_string(),
                            details: Some(
                                format!(
                                    "```json\n{}\n```",
                                    serde_json::to_string_pretty(params.as_ref())
                                        .unwrap_or_default()
                                )
                                .into(),
                            ),
                            ..Default::default()
                        }),
                        hide_all: Some(true),
                    })
                    .await?;
            }
            WorkflowActorNotification::Result { result } => {
                tracing::debug!("处理 Result 通知: {:?}", result);

                self.controller
                    .add_content(ContentItemInner::View {
                        view: ContentView::Card(ContentCard {
                            title: "输出参数".to_string(),
                            r#type: "output".to_string(),
                            details: Some(
                                format!(
                                    "```json\n{}\n```",
                                    serde_json::to_string_pretty(result.as_ref())
                                        .unwrap_or_default()
                                )
                                .into(),
                            ),
                            ..Default::default()
                        }),
                        hide_all: Some(true),
                    })
                    .await?;
            }
            WorkflowActorNotification::Warn { warn } => {
                tracing::debug!("处理 Warn 通知: {:?}", warn);

                self.controller.add_warn_card(warn.as_ref().clone()).await?;
            }
            WorkflowActorNotification::Error { error } => {
                tracing::debug!("处理 Error 通知: {:?}", error);

                self.controller
                    .add_error_card(error.as_ref().clone())
                    .await?;
            }
            WorkflowActorNotification::Done { .. } => {
                tracing::debug!("处理 Finish 通知");
            }
            WorkflowActorNotification::Views { views } => {
                tracing::debug!("处理 Views 通知: {:?}", views);

                for view in views {
                    match view.as_ref().clone() {
                        ContentViewOrDelta::View { view } => {
                            self.controller
                                .add_content(ContentItemInner::View {
                                    view,
                                    hide_all: None,
                                })
                                .await?;
                        }
                        ContentViewOrDelta::Delta { delta } => {
                            self.controller.apply_card_delta(delta).await?;
                        }
                        ContentViewOrDelta::Update { update } => {
                            self.controller
                                .update_last_content(ContentItemInner::View {
                                    view: update,
                                    hide_all: None,
                                })
                                .await?;
                        }
                    }
                }
            }
            WorkflowActorNotification::Summary { summary } => {
                tracing::debug!("处理 Summary 通知: {:?}", summary);

                self.controller
                    .add_content(ContentItemInner::View {
                        view: ContentView::Card(ContentCard {
                            title: "执行小结".to_string(),
                            r#type: "summary".to_string(),
                            details: Some(summary.as_ref().clone().into()),
                            ..Default::default()
                        }),
                        hide_all: Some(true),
                    })
                    .await?;
            }
            WorkflowActorNotification::FinalSummary {
                final_summary: summary,
            } => {
                tracing::debug!("处理 FinalSummary 通知: {:?}", summary);

                let mut metadata = serde_json::Map::new();
                if let Some(parent_stage_id) = parent_stage_id {
                    metadata.insert("parent_stage".into(), parent_stage_id.to_string().into());
                }

                self.controller
                    .add_message(Message {
                        id: uuid::Uuid::now_v7(),
                        author: Author::Assistant {
                            name: Some("总结".to_string()),
                        },
                        create_time: OffsetDateTime::now_utc(),
                        finish_time: None,
                        metadata,
                        status: MessageStatus::Chat,
                        content: vec![
                            ContentItemInner::Text {
                                text: summary.as_ref().clone(),
                            }
                            .into(),
                        ],
                    })
                    .await?;
            }
            WorkflowActorNotification::Created { workflow, name } => {
                self.handle_workflow_created(myself, state, workflow, name)
                    .await?;
            }
        }

        Ok(())
    }

    async fn handle_workflow_created(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        workflow: Arc<Workflow>,
        name: Option<String>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("处理 WorkflowCreated 事件");

        let conversation = self.controller.inner.read().await;
        let conversation_id = conversation.id;
        let user_name = conversation.user_name.clone();
        let tenant_id = conversation.tenant_id.clone();
        drop(conversation);

        match self
            .runner
            .integral_manager
            .deduct(IntegralEvent::CalculateIntegral {
                conversation_id,
                user_name,
                tenant_id,
                integral_type: IntegralType::AgentAction,
            })
            .await
        {
            Ok(_) => {}
            Err(e) => {
                tracing::error!("扣除积分失败: {:?}", e);
                self.handle_integral_error(myself, state, e).await?;
                return Ok(());
            }
        }

        let document: entity::document::Workflow = (*workflow).clone().try_into()?;
        self.runner.document_repository.insert_one(document).await?;

        let (json, branch) = SimpleWorkflow::build_string(workflow.as_ref())?;

        self.controller
            .add_content(ContentItemInner::Workflow {
                workflow: workflow.id,
                json: Some(json),
                branch: Some(branch),
                name: match name {
                    Some(name) => Some(name),
                    None => Some(self.controller.get_last_user_input().await?),
                },
            })
            .await?;

        Ok(())
    }

    async fn handle_done(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("处理 Done 事件");

        // self.controller.collect_summary_file().await?;

        let _ = self.break_inner(myself.clone(), state).await?;

        *state = ConversationActorState::Ready;

        self.controller
            .set_conversation_status(ConversationStatus::Ready)
            .await?;

        self.controller
            .set_current_message_status(MessageStatus::Done)
            .await?;

        let mut conversation = self.controller.inner.read().await.to_owned();
        let documents = conversation.try_into_documents().await?;
        self.runner
            .document_repository
            .update_full_conversation(documents)
            .await?;

        self.cleaner_sender
            .send(ConversationCleanEvent::Clean(conversation.id))
            .map_err(|e| error::MongoError::RecordNotFound_)?;

        Ok(())
    }

    async fn handle_error_event(
        &self,
        _myself: ActorRef<ConversationActorEvent>,
        _state: &mut ConversationActorState,
        error: String,
    ) -> Result<(), ActorProcessingErr> {
        tracing::error!("处理 Error 事件: {}", error);

        self.controller
            .add_content(ContentItemInner::ErrorText { error_text: error })
            .await?;

        self.controller
            .set_current_message_status(MessageStatus::Error)
            .await?;

        Ok(())
    }

    async fn handle_interaction(
        &self,
        _myself: ActorRef<ConversationActorEvent>,
        request: InteractionRequest,
        state: &mut ConversationActorState,
        reply: RpcReplyPort<Result<(), error::Error>>,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!("收到 Interaction 事件");

        let conversation = self.controller.inner.read().await;
        if let Some(current_message_id) = conversation.current_message_id {
            if let Some(message_node) = conversation.messages.get(&current_message_id) {
                // 判断content_index是不是最后一个
                if request.content_index + 1 != message_node.message.content.len() {
                    tracing::warn!("重复交互");
                    return Ok(());
                }
            }
        }
        drop(conversation);

        match state {
            ConversationActorState::Chat { actor } => {
                actor.send_message(ChatWorkflowActorEvent::Interact(request.result, reply))?;
                self.controller
                    .set_conversation_status(ConversationStatus::Chat)
                    .await?;
                self.controller
                    .set_current_message_status(MessageStatus::Chat)
                    .await?;
            }
            ConversationActorState::Explore { actor, .. } => {
                actor.send_message(ExploreActorEvent::Interact(request.result, reply))?;
                self.controller
                    .set_conversation_status(ConversationStatus::Chat)
                    .await?;
                self.controller
                    .set_current_message_status(MessageStatus::Chat)
                    .await?;
            }
            ConversationActorState::Execute { actor } => {
                actor.send_message(WorkflowActorEvent::Interact(request.result, reply))?;
                self.controller
                    .set_conversation_status(ConversationStatus::Execute)
                    .await?;
                self.controller
                    .set_current_message_status(MessageStatus::Execute)
                    .await?;
            }
            _ => {
                tracing::warn!(
                    "对话状态不正确，忽略 Interaction 事件，当前状态: {:?}",
                    state
                );
                return Ok(());
            }
        }

        Ok(())
    }

    async fn handle_error(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        error: ActorProcessingErr,
    ) -> Result<(), ActorProcessingErr> {
        tracing::error!("处理 Error 执行: {:?}", error);

        *state = ConversationActorState::Ready;

        let _ = self
            .controller
            .add_error_card(ContentCard {
                title: "执行错误".to_string(),
                details: Some(format!("{error:#?}").into()),
                ..Default::default()
            })
            .await;

        let _ = self
            .controller
            .set_current_message_status(MessageStatus::Error)
            .await;

        let _ = self
            .controller
            .set_conversation_status(ConversationStatus::Ready)
            .await;

        // 关闭所有子 actor
        myself.stop_children(None);

        let mut conversation = self.controller.inner.read().await.to_owned();
        let documents = conversation.try_into_documents().await?;
        self.runner
            .document_repository
            .update_full_conversation(documents)
            .await?;

        Ok(())
    }

    async fn handle_integral_error(
        &self,
        myself: ActorRef<ConversationActorEvent>,
        state: &mut ConversationActorState,
        error: error::Error,
    ) -> Result<(), ActorProcessingErr> {
        tracing::error!("处理 IntegralError 事件: {:?}", error);

        *state = ConversationActorState::Ready;

        self.controller
            .add_message(Message {
                id: Uuid::now_v7(),
                author: Author::System { name: None },
                create_time: OffsetDateTime::now_utc(),
                finish_time: None,
                metadata: serde_json::Map::new(),
                status: MessageStatus::Error,
                content: vec![
                    ContentItemInner::ErrorText {
                        error_text: format!("{error:#?}"),
                    }
                    .into(),
                ],
            })
            .await?;

        let _ = self
            .controller
            .set_current_message_status(MessageStatus::Error)
            .await;

        let _ = self
            .controller
            .set_conversation_status(ConversationStatus::Ready)
            .await;

        // 关闭所有子 actor
        myself.stop_children(None);

        let mut conversation = self.controller.inner.read().await.to_owned();
        let documents = conversation.try_into_documents().await?;
        self.runner
            .document_repository
            .update_full_conversation(documents)
            .await?;

        Ok(())
    }
}

#[async_trait::async_trait]
impl Actor for ConversationActor {
    type Msg = ConversationActorEvent;
    type State = ConversationActorState;
    type Arguments = ();

    async fn pre_start(
        &self,
        _myself: ActorRef<Self::Msg>,
        _args: Self::Arguments,
    ) -> Result<Self::State, ActorProcessingErr> {
        tracing::debug!("ConversationActor pre_start");

        let _ = self
            .controller
            .set_conversation_status(ConversationStatus::Ready)
            .await;

        let _ = self
            .controller
            .set_current_message_status(MessageStatus::Done)
            .await;

        Ok(ConversationActorState::Ready)
    }

    async fn handle(
        &self,
        myself: ActorRef<Self::Msg>,
        message: Self::Msg,
        mut state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        tracing::debug!(
            "ConversationActor handle: {:?}, state: {:?}",
            message,
            state
        );

        let actor = myself.clone();
        let conversation = self.controller.inner.read().await;
        let conversation_id = conversation.id;
        let user_name = conversation.user_name.clone();
        let tenant_id = conversation.tenant_id.clone();
        drop(conversation);

        let result = match (message, &mut state) {
            (Self::Msg::UserInput(request), Self::State::Ready) => {
                let integral_result = self
                    .runner
                    .integral_manager
                    .deduct(IntegralEvent::CalculateIntegral {
                        conversation_id,
                        user_name,
                        tenant_id,
                        integral_type: IntegralType::NormalQa,
                    })
                    .await;
                self.handle_user_input(myself, &mut *state, request, integral_result)
                    .await
            }
            (Self::Msg::WorkflowInput(request), Self::State::Ready) => {
                self.handle_workflow_input(myself, &mut *state, request)
                    .await
            }
            (Self::Msg::Break, Self::State::Chat { .. })
            | (Self::Msg::Break, Self::State::Explore { .. })
            | (Self::Msg::Break, Self::State::Execute { .. }) => {
                self.break_all(myself, &mut *state).await
            }
            (Self::Msg::Interaction(request, reply), Self::State::Chat { .. })
            | (Self::Msg::Interaction(request, reply), Self::State::Explore { .. })
            | (Self::Msg::Interaction(request, reply), Self::State::Execute { .. }) => {
                self.handle_interaction(myself, request, &mut *state, reply)
                    .await
            }
            (Self::Msg::ReceiveChatDeltaBlock(delta_block), Self::State::Chat { .. }) => {
                self.handle_chat_delta_block(myself, &mut *state, delta_block)
                    .await
            }
            (Self::Msg::ReceiveWorkflowNotification(notification), Self::State::Chat { .. })
            | (Self::Msg::ReceiveWorkflowNotification(notification), Self::State::Explore { .. })
            | (Self::Msg::ReceiveWorkflowNotification(notification), Self::State::Execute { .. }) => {
                self.handle_workflow_notification(myself, &mut *state, *notification)
                    .await
            }
            (Self::Msg::ReceiveExploreEvent(events), Self::State::Explore { .. }) => {
                match self
                    .runner
                    .integral_manager
                    .deduct(IntegralEvent::CalculateIntegral {
                        conversation_id,
                        user_name,
                        tenant_id,
                        integral_type: IntegralType::DeepExploration,
                    })
                    .await
                {
                    Ok(_) => {}
                    Err(e) => {
                        tracing::error!("扣除积分失败: {:?}", e);
                        match self.handle_integral_error(myself, state, e).await {
                            Ok(_) => {}
                            Err(e) => {
                                tracing::error!("{:?}", e);
                            }
                        }
                        return Ok(());
                    }
                }
                self.handle_explore_events(myself, &mut *state, events)
                    .await
            }
            (Self::Msg::ReceiveStart(metadata), Self::State::Chat { .. })
            | (Self::Msg::ReceiveStart(metadata), Self::State::Explore { .. })
            | (Self::Msg::ReceiveStart(metadata), Self::State::Execute { .. }) => {
                self.handle_receive_start(myself, &mut *state, metadata)
                    .await
            }
            (Self::Msg::ReceiveMetadata(metadata), Self::State::Chat { .. })
            | (Self::Msg::ReceiveMetadata(metadata), Self::State::Explore { .. })
            | (Self::Msg::ReceiveMetadata(metadata), Self::State::Execute { .. }) => {
                self.handle_receive_metadata(myself, &mut *state, metadata)
                    .await
            }
            (Self::Msg::ReceiveDone, Self::State::Chat { .. })
            | (Self::Msg::ReceiveDone, Self::State::Explore { .. })
            | (Self::Msg::ReceiveDone, Self::State::Execute { .. }) => {
                self.handle_done(myself, &mut *state).await
            }
            (Self::Msg::ReceiveError(error), Self::State::Chat { .. })
            | (Self::Msg::ReceiveError(error), Self::State::Explore { .. })
            | (Self::Msg::ReceiveError(error), Self::State::Execute { .. }) => {
                self.handle_error_event(myself, &mut *state, error).await
            }
            (event, state) => {
                tracing::debug!("未匹配的事件-状态组合: {:?} {:?}", event, state);
                Ok(())
            }
        };

        match result {
            Ok(()) => Ok(()),
            Err(e) => {
                let _ = self.handle_error(actor, state, e).await;
                Ok(())
            }
        }
    }

    async fn handle_supervisor_evt(
        &self,
        myself: ActorRef<Self::Msg>,
        message: SupervisionEvent,
        state: &mut Self::State,
    ) -> Result<(), ActorProcessingErr> {
        match message {
            SupervisionEvent::ActorTerminated(_, _, _) | SupervisionEvent::ActorFailed(_, _) => {
                tracing::error!("actor terminated or failed: {:?}", message);
                return self
                    .handle_error(
                        myself,
                        state,
                        anyhow::anyhow!("Actor 异常终止: {:?}", message).into(),
                    )
                    .await;
            }
            _ => {}
        }

        Ok(())
    }
}
